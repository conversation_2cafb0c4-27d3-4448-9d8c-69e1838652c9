{"build": "hb9d3cd8_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/linux-64", "constrains": [], "depends": ["__glibc >=2.17,<3.0.a0", "libgcc >=13"], "extracted_package_dir": "/data1/home/<USER>/anaconda3/pkgs/tree-2.2.1-hb9d3cd8_0", "files": ["bin/tree", "share/man/man1/man1/tree.1"], "fn": "tree-2.2.1-hb9d3cd8_0.conda", "license": "GPL-2.0-only", "link": {"source": "/data1/home/<USER>/anaconda3/pkgs/tree-2.2.1-hb9d3cd8_0", "type": 1}, "md5": "dab87bfc7ce750a1c2d8a68dc2dad2e8", "name": "tree", "package_tarball_full_path": "/data1/home/<USER>/anaconda3/pkgs/tree-2.2.1-hb9d3cd8_0.conda", "paths_data": {"paths": [{"_path": "bin/tree", "path_type": "hardlink", "sha256": "497f9e7b29df073ef4b08e55c1a235892a072b6bef89dea1f1650f8e43393c63", "sha256_in_prefix": "497f9e7b29df073ef4b08e55c1a235892a072b6bef89dea1f1650f8e43393c63", "size_in_bytes": 90160}, {"_path": "share/man/man1/man1/tree.1", "path_type": "hardlink", "sha256": "0900385101aa663c970b3e558ed3eec8b4fc96e175b70bedefcf32cf4f8bc3dd", "sha256_in_prefix": "0900385101aa663c970b3e558ed3eec8b4fc96e175b70bedefcf32cf4f8bc3dd", "size_in_bytes": 17079}], "paths_version": 1}, "requested_spec": "tree", "sha256": "737f906d21a3ac03d317a0aaa86f91afd0b87f077f83bcfcc7f3b5466f523690", "size": 52264, "subdir": "linux-64", "timestamp": 1733474145000, "url": "https://conda.anaconda.org/conda-forge/linux-64/tree-2.2.1-hb9d3cd8_0.conda", "version": "2.2.1"}