<chapter id='Keyboard_Bells'>
<title>Keyboard Bells</title>

<para>
The core protocol provides requests to control the pitch, volume and duration
of the keyboard bell and a request to explicitly sound the bell.
</para>


<para>
The X Keyboard Extension allows clients to disable the audible bell, attach a
symbolic name to a bell request or receive an event when the keyboard bell is
rung.
</para>

<sect1 id='Client_Notification_of_Bells'>
<title>Client Notification of Bells</title>

<para>
Clients can ask to receive <emphasis>
XkbBellNotify</emphasis>
 event when a bell is requested by a client or generated by the server. Bells
can be sounded due to core protocol <emphasis>
Bell</emphasis>
 requests, X Input Extension <emphasis>
DeviceBell</emphasis>
 requests, X Keyboard Extension <emphasis>
XkbBell</emphasis>
 requests or for reasons internal to the server such as the XKB <emphasis>
AccessXFeedback</emphasis>
 control.
</para>


<para>
Bell events caused by the <emphasis>
XkbBell</emphasis>
 request or by the <emphasis>
AccessXFeedback</emphasis>
 control include an optional window and symbolic name for the bell. If present,
the window makes it possible to provide some kind of visual indication of which
window caused the sound. The symbolic name can report some information about
the reason the bell was generated and makes it possible to generate a distinct
sound for each type of bell.
</para>


</sect1>
<sect1 id='Disabling_Server_Generated_Bells'>
<title>Disabling Server Generated Bells</title>

<para>
The global <emphasis>
AudibleBell</emphasis>
 boolean control for a keyboard indicates whether bells sent to that device
should normally cause the server to generate a sound. Applications which
provide "sound effects" for the various named bells will typically disable the
server generation of bells to avoid burying the user in sounds.
</para>


<para>
When the <emphasis>
AudibleBell</emphasis>
 control is active, all bells caused by core protocol <emphasis>
Bell</emphasis>
 and X Input Extension <emphasis>
DeviceBell</emphasis>
 requests cause the server to generate a sound, as do all bells generated by
the XKB <emphasis>
AccessXFeedback</emphasis>
 control. Bells requested via the X<emphasis>
kbBell</emphasis>
 request normally cause a server-generated sound, but clients can ask the
server not to sound the default keyboard bell.
</para>


<para>
When the <emphasis>
AudibleBell</emphasis>
 control is disabled, the server generates a sound only for bells that are
generated using the <emphasis>
XkbBell</emphasis>
 request and which specify forced delivery of the bell.
</para>


</sect1>
<sect1 id='Generating_Named_Bells'>
<title>Generating Named Bells</title>

<para>
The <emphasis>
XkbBell</emphasis>
 request allows clients to specify a symbolic name which is reported in the
bell events they cause. Bells generated by the <emphasis>
AccessXFeedback</emphasis>
 control of this extension also include a symbolic name, but all kinds of
feedback cause a single event even if they sound multiple tones.
</para>


<para>
The X server is permitted to use symbolic bell names (when present) to generate
sounds other than simple tones, but it is not required to do so.
</para>


<para>
Aside from those used by the XKB <emphasis>
AccessXFeedback</emphasis>
 control (see <link linkend='The_AccessXFeedback_Control'>The AccessXFeedback
Control</link>), this extension does not specify bell names or their
interpretation.
</para>


</sect1>
<sect1 id='Generating_Optional_Named_Bells'>
<title>Generating Optional Named Bells</title>

<para>
Under some circumstances, some kind of quiet audio feedback is useful, but a
normal keyboard bell is not. For example, a quiet "launch effect" can be
helpful to let the user know that an application has been started, but a loud
bell would simply be annoying.
</para>


<para>
To simplify generation of these kinds of effects, the <emphasis>
XkbBell</emphasis>
 request allows clients to specify "event only" bells. The X server never
generates a normal keyboard bell for "event only" bells, regardless of the
setting of the global <emphasis>
AudibleBell</emphasis>
 control.
</para>


<para>
If the X server generates different sounds depending bell name, it is permitted
to generate a sound even for "event only" bells. This field is intended simply
to weed out "normal" keyboard bells.
</para>


</sect1>
<sect1 id='Forcing_a_Server_Generated_Bell'>
<title>Forcing a Server Generated Bell</title>

<para>
Occasionally, it is useful to force the server to generate a sound. For
example, a client could "filter" server bells, generating sound effects for
some but sounding the normal server bell for others. Such a client needs a way
to tell the server that the requested bell should be generated regardless of
the setting of the <emphasis>
AudibleBell</emphasis>
 control.
</para>


<para>
To simplify this process, clients which call the <emphasis>
XkbBell</emphasis>
 request can specify that a bell is forced. A forced bell always causes a
server generated sound and never causes a <emphasis>
XkbBellNotify</emphasis>
 event. Because forced bells do not cause bell notify events, they have no
associated symbolic name or event window.
</para>
</sect1>
</chapter>
