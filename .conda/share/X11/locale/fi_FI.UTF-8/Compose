#
# Official compose sequences for Finland based on SFS 5966:2019 standard
#
# This compose sequence map implements all the functionality of Annex 3
# and Annex 4 of the standard and additionally defines the sequences
# provided in en_US.UTF-8/Compose. SFS 5966 sequences override any
# conflicting rules from en_US.UTF-8/Compose.
#
# Annex 1 of the standard is implemented in the keymap symbols/fi
#
# Complete rewrite by <PERSON><PERSON>
# Original version by <PERSON>
#
# Use en_US.UTF-8/Compose as the base compose sequence definition set
include "/data1/home/<USER>/EQACL/finetune/.conda/share/X11/locale/en_US.UTF-8/Compose"
# Define all the sequences of the standard overriding any sequence
# from en_US.UTF-8/Compose if such a sequence was provided already
# Sequences from Annex 3
# Sequences with COMBINING ACUTE ACCENT / <dead_acute>
<dead_acute> <space>                    :  "´"  U00B4  #  ACUTE ACCENT
<dead_acute> <a>                        :  "á"  U00E1  #  LATIN SMALL LETTER A WITH ACUTE
<dead_acute> <A>                        :  "Á"  U00C1  #  LATIN CAPITAL LETTER A WITH ACUTE
<dead_acute> <c>                        :  "ć"  U0107  #  LATIN SMALL LETTER C WITH ACUTE
<dead_acute> <C>                        :  "Ć"  U0106  #  LATIN CAPITAL LETTER C WITH ACUTE
<dead_acute> <e>                        :  "é"  U00E9  #  LATIN SMALL LETTER E WITH ACUTE
<dead_acute> <E>                        :  "É"  U00C9  #  LATIN CAPITAL LETTER E WITH ACUTE
<dead_acute> <i>                        :  "í"  U00ED  #  LATIN SMALL LETTER I WITH ACUTE
<dead_acute> <I>                        :  "Í"  U00CD  #  LATIN CAPITAL LETTER I WITH ACUTE
<dead_acute> <l>                        :  "ĺ"  U013A  #  LATIN SMALL LETTER L WITH ACUTE
<dead_acute> <L>                        :  "Ĺ"  U0139  #  LATIN CAPITAL LETTER L WITH ACUTE
<dead_acute> <n>                        :  "ń"  U0144  #  LATIN SMALL LETTER N WITH ACUTE
<dead_acute> <N>                        :  "Ń"  U0143  #  LATIN CAPITAL LETTER N WITH ACUTE
<dead_acute> <o>                        :  "ó"  U00F3  #  LATIN SMALL LETTER O WITH ACUTE
<dead_acute> <O>                        :  "Ó"  U00D3  #  LATIN CAPITAL LETTER O WITH ACUTE
<dead_acute> <r>                        :  "ŕ"  U0155  #  LATIN SMALL LETTER R WITH ACUTE
<dead_acute> <R>                        :  "Ŕ"  U0154  #  LATIN CAPITAL LETTER R WITH ACUTE
<dead_acute> <s>                        :  "ś"  U015B  #  LATIN SMALL LETTER S WITH ACUTE
<dead_acute> <S>                        :  "Ś"  U015A  #  LATIN CAPITAL LETTER S WITH ACUTE
<dead_acute> <u>                        :  "ú"  U00FA  #  LATIN SMALL LETTER U WITH ACUTE
<dead_acute> <U>                        :  "Ú"  U00DA  #  LATIN CAPITAL LETTER U WITH ACUTE
<dead_acute> <w>                        :  "ẃ"  U1E83  #  LATIN SMALL LETTER W WITH ACUTE
<dead_acute> <W>                        :  "Ẃ"  U1E82  #  LATIN CAPITAL LETTER W WITH ACUTE
<dead_acute> <y>                        :  "ý"  U00FD  #  LATIN SMALL LETTER Y WITH ACUTE
<dead_acute> <Y>                        :  "Ý"  U00DD  #  LATIN CAPITAL LETTER Y WITH ACUTE
<dead_acute> <z>                        :  "ź"  U017A  #  LATIN SMALL LETTER Z WITH ACUTE
<dead_acute> <Z>                        :  "Ź"  U0179  #  LATIN CAPITAL LETTER Z WITH ACUTE
<dead_acute> <ae>                       :  "ǽ"  U01FD  #  LATIN SMALL LETTER AE WITH ACUTE
<dead_acute> <AE>                       :  "Ǽ"  U01FC  #  LATIN CAPITAL LETTER AE WITH ACUTE
<dead_acute> <oslash>                   :  "ǿ"  U01FF  #  LATIN SMALL LETTER O WITH STROKE AND ACUTE
<dead_acute> <Oslash>                   :  "Ǿ"  U01FE  #  LATIN CAPITAL LETTER O WITH STROKE AND ACUTE
<dead_acute> <aring>                    :  "ǻ"  U01FB  #  LATIN SMALL LETTER A WITH RING ABOVE AND ACUTE
<dead_acute> <Aring>                    :  "Ǻ"  U01FA  #  LATIN CAPITAL LETTER A WITH RING ABOVE AND ACUTE
# Sequences with COMBINING BREVE / <dead_breve>
<dead_breve> <space>                    :  "˘"  U02D8  #  BREVE
<dead_breve> <a>                        :  "ă"  U0103  #  LATIN SMALL LETTER A WITH BREVE
<dead_breve> <A>                        :  "Ă"  U0102  #  LATIN CAPITAL LETTER A WITH BREVE
<dead_breve> <e>                        :  "ĕ"  U0115  #  LATIN SMALL LETTER E WITH BREVE
<dead_breve> <E>                        :  "Ĕ"  U0114  #  LATIN CAPITAL LETTER E WITH BREVE
<dead_breve> <g>                        :  "ğ"  U011F  #  LATIN SMALL LETTER G WITH BREVE
<dead_breve> <G>                        :  "Ğ"  U011E  #  LATIN CAPITAL LETTER G WITH BREVE
<dead_breve> <i>                        :  "ĭ"  U012D  #  LATIN SMALL LETTER I WITH BREVE
<dead_breve> <I>                        :  "Ĭ"  U012C  #  LATIN CAPITAL LETTER I WITH BREVE
<dead_breve> <o>                        :  "ŏ"  U014F  #  LATIN SMALL LETTER O WITH BREVE
<dead_breve> <O>                        :  "Ŏ"  U014E  #  LATIN CAPITAL LETTER O WITH BREVE
<dead_breve> <u>                        :  "ŭ"  U016D  #  LATIN SMALL LETTER U WITH BREVE
<dead_breve> <U>                        :  "Ŭ"  U016C  #  LATIN CAPITAL LETTER U WITH BREVE
# Sequences with COMBINING CARON / <dead_caron>
<dead_caron> <space>                    :  "ˇ"  U02C7  #  CARON
<dead_caron> <a>                        :  "ǎ"  U01CE  #  LATIN SMALL LETTER A WITH CARON
<dead_caron> <A>                        :  "Ǎ"  U01CD  #  LATIN CAPITAL LETTER A WITH CARON
<dead_caron> <c>                        :  "č"  U010D  #  LATIN SMALL LETTER C WITH CARON
<dead_caron> <C>                        :  "Č"  U010C  #  LATIN CAPITAL LETTER C WITH CARON
<dead_caron> <d>                        :  "ď"  U010F  #  LATIN SMALL LETTER D WITH CARON
<dead_caron> <D>                        :  "Ď"  U010E  #  LATIN CAPITAL LETTER D WITH CARON
<dead_caron> <e>                        :  "ě"  U011B  #  LATIN SMALL LETTER E WITH CARON
<dead_caron> <E>                        :  "Ě"  U011A  #  LATIN CAPITAL LETTER E WITH CARON
<dead_caron> <g>                        :  "ǧ"  U01E7  #  LATIN SMALL LETTER G WITH CARON
<dead_caron> <G>                        :  "Ǧ"  U01E6  #  LATIN CAPITAL LETTER G WITH CARON
<dead_caron> <h>                        :  "ȟ"  U021F  #  LATIN SMALL LETTER H WITH CARON
<dead_caron> <H>                        :  "Ȟ"  U021E  #  LATIN CAPITAL LETTER H WITH CARON
<dead_caron> <i>                        :  "ǐ"  U01D0  #  LATIN SMALL LETTER I WITH CARON
<dead_caron> <I>                        :  "Ǐ"  U01CF  #  LATIN CAPITAL LETTER I WITH CARON
<dead_caron> <k>                        :  "ǩ"  U01E9  #  LATIN SMALL LETTER K WITH CARON
<dead_caron> <K>                        :  "Ǩ"  U01E8  #  LATIN CAPITAL LETTER K WITH CARON
<dead_caron> <l>                        :  "ľ"  U013E  #  LATIN SMALL LETTER L WITH CARON
<dead_caron> <L>                        :  "Ľ"  U013D  #  LATIN CAPITAL LETTER L WITH CARON
<dead_caron> <n>                        :  "ň"  U0148  #  LATIN SMALL LETTER N WITH CARON
<dead_caron> <N>                        :  "Ň"  U0147  #  LATIN CAPITAL LETTER N WITH CARON
<dead_caron> <o>                        :  "ǒ"  U01D2  #  LATIN SMALL LETTER O WITH CARON
<dead_caron> <O>                        :  "Ǒ"  U01D1  #  LATIN CAPITAL LETTER O WITH CARON
<dead_caron> <r>                        :  "ř"  U0159  #  LATIN SMALL LETTER R WITH CARON
<dead_caron> <R>                        :  "Ř"  U0158  #  LATIN CAPITAL LETTER R WITH CARON
<dead_caron> <s>                        :  "š"  U0161  #  LATIN SMALL LETTER S WITH CARON
<dead_caron> <S>                        :  "Š"  U0160  #  LATIN CAPITAL LETTER S WITH CARON
<dead_caron> <t>                        :  "ť"  U0165  #  LATIN SMALL LETTER T WITH CARON
<dead_caron> <T>                        :  "Ť"  U0164  #  LATIN CAPITAL LETTER T WITH CARON
<dead_caron> <u>                        :  "ǔ"  U01D4  #  LATIN SMALL LETTER U WITH CARON
<dead_caron> <U>                        :  "Ǔ"  U01D3  #  LATIN CAPITAL LETTER U WITH CARON
<dead_caron> <z>                        :  "ž"  U017E  #  LATIN SMALL LETTER Z WITH CARON
<dead_caron> <Z>                        :  "Ž"  U017D  #  LATIN CAPITAL LETTER Z WITH CARON
<dead_caron> <ezh>                      :  "ǯ"  U01EF  #  LATIN SMALL LETTER EZH WITH CARON
<dead_caron> <EZH>                      :  "Ǯ"  U01EE  #  LATIN CAPITAL LETTER EZH WITH CARON
# Sequences with COMBINING CEDILLA / <dead_cedilla>
<dead_cedilla> <space>                  :  "¸"  U00B8  #  CEDILLA
<dead_cedilla> <c>                      :  "ç"  U00E7  #  LATIN SMALL LETTER C WITH CEDILLA
<dead_cedilla> <C>                      :  "Ç"  U00C7  #  LATIN CAPITAL LETTER C WITH CEDILLA
<dead_cedilla> <g>                      :  "ģ"  U0123  #  LATIN SMALL LETTER G WITH CEDILLA
<dead_cedilla> <G>                      :  "Ģ"  U0122  #  LATIN CAPITAL LETTER G WITH CEDILLA
<dead_cedilla> <k>                      :  "ķ"  U0137  #  LATIN SMALL LETTER K WITH CEDILLA
<dead_cedilla> <K>                      :  "Ķ"  U0136  #  LATIN CAPITAL LETTER K WITH CEDILLA
<dead_cedilla> <l>                      :  "ļ"  U013C  #  LATIN SMALL LETTER L WITH CEDILLA
<dead_cedilla> <L>                      :  "Ļ"  U013B  #  LATIN CAPITAL LETTER L WITH CEDILLA
<dead_cedilla> <n>                      :  "ņ"  U0146  #  LATIN SMALL LETTER N WITH CEDILLA
<dead_cedilla> <N>                      :  "Ņ"  U0145  #  LATIN CAPITAL LETTER N WITH CEDILLA
<dead_cedilla> <r>                      :  "ŗ"  U0157  #  LATIN SMALL LETTER R WITH CEDILLA
<dead_cedilla> <R>                      :  "Ŗ"  U0156  #  LATIN CAPITAL LETTER R WITH CEDILLA
<dead_cedilla> <s>                      :  "ş"  U015F  #  LATIN SMALL LETTER S WITH CEDILLA
<dead_cedilla> <S>                      :  "Ş"  U015E  #  LATIN CAPITAL LETTER S WITH CEDILLA
<dead_cedilla> <t>                      :  "ţ"  U0163  #  LATIN SMALL LETTER T WITH CEDILLA
<dead_cedilla> <T>                      :  "Ţ"  U0162  #  LATIN CAPITAL LETTER T WITH CEDILLA
# Sequences with COMBINING CIRCUMFLEX ACCENT / <dead_circumflex>
<dead_circumflex> <space>               :  "^"  U005E  #  CIRCUMFLEX
<dead_circumflex> <a>                   :  "â"  U00E2  #  LATIN SMALL LETTER A WITH CIRCUMFLEX
<dead_circumflex> <A>                   :  "Â"  U00C2  #  LATIN CAPITAL LETTER A WITH CIRCUMFLEX
<dead_circumflex> <c>                   :  "ĉ"  U0109  #  LATIN SMALL LETTER C WITH CIRCUMFLEX
<dead_circumflex> <C>                   :  "Ĉ"  U0108  #  LATIN CAPITAL LETTER C WITH CIRCUMFLEX
<dead_circumflex> <e>                   :  "ê"  U00EA  #  LATIN SMALL LETTER E WITH CIRCUMFLEX
<dead_circumflex> <E>                   :  "Ê"  U00CA  #  LATIN CAPITAL LETTER E WITH CIRCUMFLEX
<dead_circumflex> <g>                   :  "ĝ"  U011D  #  LATIN SMALL LETTER G WITH CIRCUMFLEX
<dead_circumflex> <G>                   :  "Ĝ"  U011C  #  LATIN CAPITAL LETTER G WITH CIRCUMFLEX
<dead_circumflex> <h>                   :  "ĥ"  U0125  #  LATIN SMALL LETTER H WITH CIRCUMFLEX
<dead_circumflex> <H>                   :  "Ĥ"  U0124  #  LATIN CAPITAL LETTER H WITH CIRCUMFLEX
<dead_circumflex> <i>                   :  "î"  U00EE  #  LATIN SMALL LETTER I WITH CIRCUMFLEX
<dead_circumflex> <I>                   :  "Î"  U00CE  #  LATIN CAPITAL LETTER I WITH CIRCUMFLEX
<dead_circumflex> <j>                   :  "ĵ"  U0135  #  LATIN SMALL LETTER J WITH CIRCUMFLEX
<dead_circumflex> <J>                   :  "Ĵ"  U0134  #  LATIN CAPITAL LETTER J WITH CIRCUMFLEX
<dead_circumflex> <o>                   :  "ô"  U00F4  #  LATIN SMALL LETTER O WITH CIRCUMFLEX
<dead_circumflex> <O>                   :  "Ô"  U00D4  #  LATIN CAPITAL LETTER O WITH CIRCUMFLEX
<dead_circumflex> <s>                   :  "ŝ"  U015D  #  LATIN SMALL LETTER S WITH CIRCUMFLEX
<dead_circumflex> <S>                   :  "Ŝ"  U015C  #  LATIN CAPITAL LETTER S WITH CIRCUMFLEX
<dead_circumflex> <u>                   :  "û"  U00FB  #  LATIN SMALL LETTER U WITH CIRCUMFLEX
<dead_circumflex> <U>                   :  "Û"  U00DB  #  LATIN CAPITAL LETTER U WITH CIRCUMFLEX
<dead_circumflex> <w>                   :  "ŵ"  U0175  #  LATIN SMALL LETTER W WITH CIRCUMFLEX
<dead_circumflex> <W>                   :  "Ŵ"  U0174  #  LATIN CAPITAL LETTER W WITH CIRCUMFLEX
<dead_circumflex> <y>                   :  "ŷ"  U0177  #  LATIN SMALL LETTER Y WITH CIRCUMFLEX
<dead_circumflex> <Y>                   :  "Ŷ"  U0176  #  LATIN CAPITAL LETTER Y WITH CIRCUMFLEX
# Sequences with COMBINING COMMA BELOW / <dead_belowcomma>
<dead_belowcomma> <s>                   :  "ș"  U0219  #  LATIN SMALL LETTER S WITH COMMA BELOW
<dead_belowcomma> <S>                   :  "Ș"  U0218  #  LATIN CAPITAL LETTER S WITH COMMA BELOW
<dead_belowcomma> <t>                   :  "ț"  U021B  #  LATIN SMALL LETTER T WITH COMMA BELOW
<dead_belowcomma> <T>                   :  "Ț"  U021A  #  LATIN CAPITAL LETTER T WITH COMMA BELOW
# Sequences with COMBINING DIAERESIS / <dead_diaeresis>
<dead_diaeresis> <space>                :  "¨"  U00A8  #  DIAERESIS
<dead_diaeresis> <a>                    :  "ä"  U00E4  #  LATIN SMALL LETTER A WITH DIAERESIS
<dead_diaeresis> <A>                    :  "Ä"  U00C4  #  LATIN CAPITAL LETTER A WITH DIAERESIS
<dead_diaeresis> <e>                    :  "ë"  U00EB  #  LATIN SMALL LETTER E WITH DIAERESIS
<dead_diaeresis> <E>                    :  "Ë"  U00CB  #  LATIN CAPITAL LETTER E WITH DIAERESIS
<dead_diaeresis> <i>                    :  "ï"  U00EF  #  LATIN SMALL LETTER I WITH DIAERESIS
<dead_diaeresis> <I>                    :  "Ï"  U00CF  #  LATIN CAPITAL LETTER I WITH DIAERESIS
<dead_diaeresis> <o>                    :  "ö"  U00F6  #  LATIN SMALL LETTER O WITH DIAERESIS
<dead_diaeresis> <O>                    :  "Ö"  U00D6  #  LATIN CAPITAL LETTER O WITH DIAERESIS
<dead_diaeresis> <u>                    :  "ü"  U00FC  #  LATIN SMALL LETTER U WITH DIAERESIS
<dead_diaeresis> <U>                    :  "Ü"  U00DC  #  LATIN CAPITAL LETTER U WITH DIAERESIS
<dead_diaeresis> <w>                    :  "ẅ"  U1E85  #  LATIN SMALL LETTER W WITH DIAERESIS
<dead_diaeresis> <W>                    :  "Ẅ"  U1E84  #  LATIN CAPITAL LETTER W WITH DIAERESIS
<dead_diaeresis> <y>                    :  "ÿ"  U00FF  #  LATIN SMALL LETTER Y WITH DIAERESIS
<dead_diaeresis> <Y>                    :  "Ÿ"  U0178  #  LATIN CAPITAL LETTER Y WITH DIAERESIS
# Sequences with COMBINING DOT ABOVE / <dead_abovedot>
<dead_abovedot> <space>                 :  "˙"  U02D9  #  DOT ABOVE
<dead_abovedot> <b>                     :  "ḃ"  U1E03  #  LATIN SMALL LETTER B WITH DOT ABOVE
<dead_abovedot> <B>                     :  "Ḃ"  U1E02  #  LATIN CAPITAL LETTER B WITH DOT ABOVE
<dead_abovedot> <c>                     :  "ċ"  U010B  #  LATIN SMALL LETTER C WITH DOT ABOVE
<dead_abovedot> <C>                     :  "Ċ"  U010A  #  LATIN CAPITAL LETTER C WITH DOT ABOVE
<dead_abovedot> <d>                     :  "ḋ"  U1E0B  #  LATIN SMALL LETTER D WITH DOT ABOVE
<dead_abovedot> <D>                     :  "Ḋ"  U1E0A  #  LATIN CAPITAL LETTER D WITH DOT ABOVE
<dead_abovedot> <e>                     :  "ė"  U0117  #  LATIN SMALL LETTER E WITH DOT ABOVE
<dead_abovedot> <E>                     :  "Ė"  U0116  #  LATIN CAPITAL LETTER E WITH DOT ABOVE
<dead_abovedot> <f>                     :  "ḟ"  U1E1F  #  LATIN SMALL LETTER F WITH DOT ABOVE
<dead_abovedot> <F>                     :  "Ḟ"  U1E1E  #  LATIN CAPITAL LETTER F WITH DOT ABOVE
<dead_abovedot> <g>                     :  "ġ"  U0121  #  LATIN SMALL LETTER G WITH DOT ABOVE
<dead_abovedot> <G>                     :  "Ġ"  U0120  #  LATIN CAPITAL LETTER G WITH DOT ABOVE
<dead_abovedot> <I>                     :  "İ"  U0130  #  LATIN CAPITAL LETTER I WITH DOT ABOVE
<dead_abovedot> <m>                     :  "ṁ"  U1E41  #  LATIN SMALL LETTER M WITH DOT ABOVE
<dead_abovedot> <M>                     :  "Ṁ"  U1E40  #  LATIN CAPITAL LETTER M WITH DOT ABOVE
<dead_abovedot> <p>                     :  "ṗ"  U1E57  #  LATIN SMALL LETTER P WITH DOT ABOVE
<dead_abovedot> <P>                     :  "Ṗ"  U1E56  #  LATIN CAPITAL LETTER P WITH DOT ABOVE
<dead_abovedot> <s>                     :  "ṡ"  U1E61  #  LATIN SMALL LETTER S WITH DOT ABOVE
<dead_abovedot> <S>                     :  "Ṡ"  U1E60  #  LATIN CAPITAL LETTER S WITH DOT ABOVE
<dead_abovedot> <t>                     :  "ṫ"  U1E6B  #  LATIN SMALL LETTER T WITH DOT ABOVE
<dead_abovedot> <T>                     :  "Ṫ"  U1E6A  #  LATIN CAPITAL LETTER T WITH DOT ABOVE
<dead_abovedot> <z>                     :  "ż"  U017C  #  LATIN SMALL LETTER Z WITH DOT ABOVE
<dead_abovedot> <Z>                     :  "Ż"  U017B  #  LATIN CAPITAL LETTER Z WITH DOT ABOVE
# Sequences with COMBINING DOT BELOW / <dead_belowdot>
# <dead_belowdot> <space>              :  " "  U....  #  DOT BELOW
<dead_belowdot> <a>                     :  "ạ"  U1EA1  #  LATIN SMALL LETTER A WITH DOT BELOW
<dead_belowdot> <A>                     :  "Ạ"  U1EA0  #  LATIN CAPITAL LETTER A WITH DOT BELOW
<dead_belowdot> <e>                     :  "ẹ"  U1EB9  #  LATIN SMALL LETTER E WITH DOT BELOW
<dead_belowdot> <E>                     :  "Ẹ"  U1EB8  #  LATIN CAPITAL LETTER E WITH DOT BELOW
<dead_belowdot> <i>                     :  "ị"  U1ECB  #  LATIN SMALL LETTER I WITH DOT BELOW
<dead_belowdot> <I>                     :  "Ị"  U1ECA  #  LATIN CAPITAL LETTER I WITH DOT BELOW
<dead_belowdot> <o>                     :  "ọ"  U1ECD  #  LATIN SMALL LETTER O WITH DOT BELOW
<dead_belowdot> <O>                     :  "Ọ"  U1ECC  #  LATIN CAPITAL LETTER O WITH DOT BELOW
<dead_belowdot> <u>                     :  "ụ"  U1EE5  #  LATIN SMALL LETTER U WITH DOT BELOW
<dead_belowdot> <U>                     :  "Ụ"  U1EE4  #  LATIN CAPITAL LETTER U WITH DOT BELOW
<dead_belowdot> <y>                     :  "ỵ"  U1EF5  #  LATIN SMALL LETTER Y WITH DOT BELOW
<dead_belowdot> <Y>                     :  "Ỵ"  U1EF4  #  LATIN CAPITAL LETTER Y WITH DOT BELOW
# Sequences with COMBINING DOUBLE ACUTE ACCENT / <dead_doubleacute>
<dead_doubleacute> <space>              :  "˝"  U02DD  #  DOUBLE ACUTE ACCENT
<dead_doubleacute> <o>                  :  "ő"  U0151  #  LATIN SMALL LETTER O WITH DOUBLE ACUTE
<dead_doubleacute> <O>                  :  "Ő"  U0150  #  LATIN CAPITAL LETTER O WITH DOUBLE ACUTE
<dead_doubleacute> <u>                  :  "ű"  U0171  #  LATIN SMALL LETTER U WITH DOUBLE ACUTE
<dead_doubleacute> <U>                  :  "Ű"  U0170  #  LATIN CAPITAL LETTER U WITH DOUBLE ACUTE
# Sequences with COMBINING GRAVE ACCENT / <dead_grave>
<dead_grave> <space>                    :  "`"  U0060  #  GRAVE ACCENT
<dead_grave> <a>                        :  "à"  U00E0  #  LATIN SMALL LETTER A WITH GRAVE
<dead_grave> <A>                        :  "À"  U00C0  #  LATIN CAPITAL LETTER A WITH GRAVE
<dead_grave> <e>                        :  "è"  U00E8  #  LATIN SMALL LETTER E WITH GRAVE
<dead_grave> <E>                        :  "È"  U00C8  #  LATIN CAPITAL LETTER E WITH GRAVE
<dead_grave> <i>                        :  "ì"  U00EC  #  LATIN SMALL LETTER I WITH GRAVE
<dead_grave> <I>                        :  "Ì"  U00CC  #  LATIN CAPITAL LETTER I WITH GRAVE
<dead_grave> <o>                        :  "ò"  U00F2  #  LATIN SMALL LETTER O WITH GRAVE
<dead_grave> <O>                        :  "Ò"  U00D2  #  LATIN CAPITAL LETTER O WITH GRAVE
<dead_grave> <u>                        :  "ù"  U00F9  #  LATIN SMALL LETTER U WITH GRAVE
<dead_grave> <U>                        :  "Ù"  U00D9  #  LATIN CAPITAL LETTER U WITH GRAVE
<dead_grave> <w>                        :  "ẁ"  U1E81  #  LATIN SMALL LETTER W WITH GRAVE
<dead_grave> <W>                        :  "Ẁ"  U1E80  #  LATIN CAPITAL LETTER W WITH GRAVE
<dead_grave> <y>                        :  "ỳ"  U1EF3  #  LATIN SMALL LETTER Y WITH GRAVE
<dead_grave> <Y>                        :  "Ỳ"  U1EF2  #  LATIN CAPITAL LETTER Y WITH GRAVE
# Sequences with COMBINING MACRON / <dead_macron>
<dead_macron> <space>                   :  "¯"  U00AF  #  MACRON
<dead_macron> <a>                       :  "ā"  U0101  #  LATIN SMALL LETTER A WITH MACRON
<dead_macron> <A>                       :  "Ā"  U0100  #  LATIN CAPITAL LETTER A WITH MACRON
<dead_macron> <e>                       :  "ē"  U0113  #  LATIN SMALL LETTER E WITH MACRON
<dead_macron> <E>                       :  "Ē"  U0112  #  LATIN CAPITAL LETTER E WITH MACRON
<dead_macron> <i>                       :  "ī"  U012B  #  LATIN SMALL LETTER I WITH MACRON
<dead_macron> <I>                       :  "Ī"  U012A  #  LATIN CAPITAL LETTER I WITH MACRON
<dead_macron> <o>                       :  "ō"  U014D  #  LATIN SMALL LETTER O WITH MACRON
<dead_macron> <O>                       :  "Ō"  U014C  #  LATIN CAPITAL LETTER O WITH MACRON
<dead_macron> <u>                       :  "ū"  U016B  #  LATIN SMALL LETTER U WITH MACRON
<dead_macron> <U>                       :  "Ū"  U016A  #  LATIN CAPITAL LETTER U WITH MACRON
<dead_macron> <ae>                      :  "ǣ"  U01E3  #  LATIN SMALL LETTER AE WITH MACRON
<dead_macron> <AE>                      :  "Ǣ"  U01E2  #  LATIN CAPITAL LETTER AE WITH MACRON
<dead_macron> <adiaeresis>              :  "ǟ"  U01DF  #  LATIN SMALL LETTER A WITH DIAERESIS AND MACRON
<dead_macron> <Adiaeresis>              :  "Ǟ"  U01DE  #  LATIN CAPITAL LETTER A WITH DIAERESIS AND MACRON
# Sequences with COMBINING OGONEK / <dead_ogonek>
<dead_ogonek> <space>                   :  "˛"  U02DB  #  OGONEK
<dead_ogonek> <a>                       :  "ą"  U0105  #  LATIN SMALL LETTER A WITH OGONEK
<dead_ogonek> <A>                       :  "Ą"  U0104  #  LATIN CAPITAL LETTER A WITH OGONEK
<dead_ogonek> <e>                       :  "ę"  U0119  #  LATIN SMALL LETTER E WITH OGONEK
<dead_ogonek> <E>                       :  "Ę"  U0118  #  LATIN CAPITAL LETTER E WITH OGONEK
<dead_ogonek> <i>                       :  "į"  U012F  #  LATIN SMALL LETTER I WITH OGONEK
<dead_ogonek> <I>                       :  "Į"  U012E  #  LATIN CAPITAL LETTER I WITH OGONEK
<dead_ogonek> <o>                       :  "ǫ"  U01EB  #  LATIN SMALL LETTER O WITH OGONEK
<dead_ogonek> <O>                       :  "Ǫ"  U01EA  #  LATIN CAPITAL LETTER O WITH OGONEK
<dead_ogonek> <u>                       :  "ų"  U0173  #  LATIN SMALL LETTER U WITH OGONEK
<dead_ogonek> <U>                       :  "Ų"  U0172  #  LATIN CAPITAL LETTER U WITH OGONEK
# Sequences with COMBINING RING ABOVE / <dead_abovering>
<dead_abovering> <space>                :  "˚"  U02DA  #  RING ABOVE
<dead_abovering> <a>                    :  "å"  U00E5  #  LATIN SMALL LETTER A WITH RING ABOVE
<dead_abovering> <A>                    :  "Å"  U00C5  #  LATIN CAPITAL LETTER A WITH RING ABOVE
<dead_abovering> <u>                    :  "ů"  U016F  #  LATIN SMALL LETTER U WITH RING ABOVE
<dead_abovering> <U>                    :  "Ů"  U016E  #  LATIN CAPITAL LETTER U WITH RING ABOVE
# Sequences with COMBINING TILDE / <dead_tilde>
<dead_tilde> <space>                    :  "~"  U007E  #  TILDE
<dead_tilde> <a>                        :  "ã"  U00E3  #  LATIN SMALL LETTER A WITH TILDE
<dead_tilde> <A>                        :  "Ã"  U00C3  #  LATIN CAPITAL LETTER A WITH TILDE
<dead_tilde> <e>                        :  "ẽ"  U1EBD  #  LATIN SMALL LETTER E WITH TILDE
<dead_tilde> <E>                        :  "Ẽ"  U1EBC  #  LATIN CAPITAL LETTER E WITH TILDE
<dead_tilde> <i>                        :  "ĩ"  U0129  #  LATIN SMALL LETTER I WITH TILDE
<dead_tilde> <I>                        :  "Ĩ"  U0128  #  LATIN CAPITAL LETTER I WITH TILDE
<dead_tilde> <n>                        :  "ñ"  U00F1  #  LATIN SMALL LETTER N WITH TILDE
<dead_tilde> <N>                        :  "Ñ"  U00D1  #  LATIN CAPITAL LETTER N WITH TILDE
<dead_tilde> <o>                        :  "õ"  U00F5  #  LATIN SMALL LETTER O WITH TILDE
<dead_tilde> <O>                        :  "Õ"  U00D5  #  LATIN CAPITAL LETTER O WITH TILDE
<dead_tilde> <u>                        :  "ũ"  U0169  #  LATIN SMALL LETTER U WITH TILDE
<dead_tilde> <U>                        :  "Ũ"  U0168  #  LATIN CAPITAL LETTER U WITH TILDE
<dead_tilde> <y>                        :  "ỹ"  U1EF9  #  LATIN SMALL LETTER Y WITH TILDE
<dead_tilde> <Y>                        :  "Ỹ"  U1EF8  #  LATIN CAPITAL LETTER Y WITH TILDE
# Sequences with AUXILIARY STROKE / <dead_stroke>
<dead_stroke> <d>                       :  "đ"  U0111  #  LATIN SMALL LETTER D WITH STROKE
<dead_stroke> <D>                       :  "Đ"  U0110  #  LATIN CAPITAL LETTER D WITH STROKE
<dead_stroke> <g>                       :  "ǥ"  U01E5  #  LATIN SMALL LETTER G WITH STROKE
<dead_stroke> <G>                       :  "Ǥ"  U01E4  #  LATIN CAPITAL LETTER G WITH STROKE
<dead_stroke> <h>                       :  "ħ"  U0127  #  LATIN SMALL LETTER H WITH STROKE
<dead_stroke> <H>                       :  "Ħ"  U0126  #  LATIN CAPITAL LETTER H WITH STROKE
<dead_stroke> <l>                       :  "ł"  U0142  #  LATIN SMALL LETTER L WITH STROKE
<dead_stroke> <L>                       :  "Ł"  U0141  #  LATIN CAPITAL LETTER L WITH STROKE
<dead_stroke> <o>                       :  "ø"  U00F8  #  LATIN SMALL LETTER O WITH STROKE
<dead_stroke> <O>                       :  "Ø"  U00D8  #  LATIN CAPITAL LETTER O WITH STROKE
<dead_stroke> <t>                       :  "ŧ"  U0167  #  LATIN SMALL LETTER T WITH STROKE
<dead_stroke> <T>                       :  "Ŧ"  U0166  #  LATIN CAPITAL LETTER T WITH STROKE
# Sequences from Annex 4
# Sequences with COMBINING BREVE / <dead_breve>
<dead_acute>    <dead_breve> <a>        :  "ắ"  U1EAF  #  LATIN SMALL LETTER A WITH BREVE AND ACUTE
<dead_belowdot> <dead_breve> <a>        :  "ặ"  U1EB7  #  LATIN SMALL LETTER A WITH BREVE AND DOT BELOW
<dead_grave>    <dead_breve> <a>        :  "ằ"  U1EB1  #  LATIN SMALL LETTER A WITH BREVE AND GRAVE
<dead_hook>     <dead_breve> <a>        :  "ẳ"  U1EB3  #  LATIN SMALL LETTER A WITH BREVE AND HOOK ABOVE
<dead_tilde>    <dead_breve> <a>        :  "ẵ"  U1EB5  #  LATIN SMALL LETTER A WITH BREVE AND TILDE
<dead_acute>    <dead_breve> <A>        :  "Ắ"  U1EAE  #  LATIN CAPITAL LETTER A WITH BREVE AND ACUTE
<dead_belowdot> <dead_breve> <A>        :  "Ặ"  U1EB6  #  LATIN CAPITAL LETTER A WITH BREVE AND DOT BELOW
<dead_grave>    <dead_breve> <A>        :  "Ằ"  U1EB0  #  LATIN CAPITAL LETTER A WITH BREVE AND GRAVE
<dead_hook>     <dead_breve> <A>        :  "Ẳ"  U1EB2  #  LATIN CAPITAL LETTER A WITH BREVE AND HOOK ABOVE
<dead_tilde>    <dead_breve> <A>        :  "Ẵ"  U1EB4  #  LATIN CAPITAL LETTER A WITH BREVE AND TILDE
# Sequences with COMBINING CIRCUMFLEX ACCENT / <dead_circumflex>
<dead_acute>    <dead_circumflex> <a>   :  "ấ"  U1EA5  #  LATIN SMALL LETTER A WITH CIRCUMFLEX AND ACUTE
<dead_belowdot> <dead_circumflex> <a>   :  "ậ"  U1EAD  #  LATIN SMALL LETTER A WITH CIRCUMFLEX AND DOT BELOW
<dead_grave>    <dead_circumflex> <a>   :  "ầ"  U1EA7  #  LATIN SMALL LETTER A WITH CIRCUMFLEX AND GRAVE
<dead_hook>     <dead_circumflex> <a>   :  "ẩ"  U1EA9  #  LATIN SMALL LETTER A WITH CIRCUMFLEX AND HOOK ABOVE
<dead_tilde>    <dead_circumflex> <a>   :  "ẫ"  U1EAB  #  LATIN SMALL LETTER A WITH CIRCUMFLEX AND TILDE
<dead_acute>    <dead_circumflex> <A>   :  "Ấ"  U1EA4  #  LATIN CAPITAL LETTER A WITH CIRCUMFLEX AND ACUTE
<dead_belowdot> <dead_circumflex> <A>   :  "Ậ"  U1EAC  #  LATIN CAPITAL LETTER A WITH CIRCUMFLEX AND DOT BELOW
<dead_grave>    <dead_circumflex> <A>   :  "Ầ"  U1EA6  #  LATIN CAPITAL LETTER A WITH CIRCUMFLEX AND GRAVE
<dead_hook>     <dead_circumflex> <A>   :  "Ẩ"  U1EA8  #  LATIN CAPITAL LETTER A WITH CIRCUMFLEX AND HOOK ABOVE
<dead_tilde>    <dead_circumflex> <A>   :  "Ẫ"  U1EAA  #  LATIN CAPITAL LETTER A WITH CIRCUMFLEX AND TILDE
<dead_acute>    <dead_circumflex> <e>   :  "ế"  U1EBF  #  LATIN SMALL LETTER E WITH CIRCUMFLEX AND ACUTE
<dead_belowdot> <dead_circumflex> <e>   :  "ệ"  U1EC7  #  LATIN SMALL LETTER E WITH CIRCUMFLEX AND DOT BELOW
<dead_grave>    <dead_circumflex> <e>   :  "ề"  U1EC1  #  LATIN SMALL LETTER E WITH CIRCUMFLEX AND GRAVE
<dead_hook>     <dead_circumflex> <e>   :  "ể"  U1EC3  #  LATIN SMALL LETTER E WITH CIRCUMFLEX AND HOOK ABOVE
<dead_tilde>    <dead_circumflex> <e>   :  "ễ"  U1EC5  #  LATIN SMALL LETTER E WITH CIRCUMFLEX AND TILDE
<dead_acute>    <dead_circumflex> <E>   :  "Ế"  U1EBE  #  LATIN CAPITAL LETTER E WITH CIRCUMFLEX AND ACUTE
<dead_belowdot> <dead_circumflex> <E>   :  "Ệ"  U1EC6  #  LATIN CAPITAL LETTER E WITH CIRCUMFLEX AND DOT BELOW
<dead_grave>    <dead_circumflex> <E>   :  "Ề"  U1EC0  #  LATIN CAPITAL LETTER E WITH CIRCUMFLEX AND GRAVE
<dead_hook>     <dead_circumflex> <E>   :  "Ể"  U1EC2  #  LATIN CAPITAL LETTER E WITH CIRCUMFLEX AND HOOK ABOVE
<dead_tilde>    <dead_circumflex> <E>   :  "Ễ"  U1EC4  #  LATIN CAPITAL LETTER E WITH CIRCUMFLEX AND TILDE
<dead_acute>    <dead_circumflex> <o>   :  "ố"  U1ED1  #  LATIN SMALL LETTER O WITH CIRCUMFLEX AND ACUTE
<dead_belowdot> <dead_circumflex> <o>   :  "ộ"  U1ED9  #  LATIN SMALL LETTER O WITH CIRCUMFLEX AND DOT BELOW
<dead_grave>    <dead_circumflex> <o>   :  "ồ"  U1ED3  #  LATIN SMALL LETTER O WITH CIRCUMFLEX AND GRAVE
<dead_hook>     <dead_circumflex> <o>   :  "ổ"  U1ED5  #  LATIN SMALL LETTER O WITH CIRCUMFLEX AND HOOK ABOVE
<dead_tilde>    <dead_circumflex> <o>   :  "ỗ"  U1ED7  #  LATIN SMALL LETTER O WITH CIRCUMFLEX AND TILDE
<dead_acute>    <dead_circumflex> <O>   :  "Ố"  U1ED0  #  LATIN CAPITAL LETTER O WITH CIRCUMFLEX AND ACUTE
<dead_belowdot> <dead_circumflex> <O>   :  "Ộ"  U1ED8  #  LATIN CAPITAL LETTER O WITH CIRCUMFLEX AND DOT BELOW
<dead_grave>    <dead_circumflex> <O>   :  "Ồ"  U1ED2  #  LATIN CAPITAL LETTER O WITH CIRCUMFLEX AND GRAVE
<dead_hook>     <dead_circumflex> <O>   :  "Ổ"  U1ED4  #  LATIN CAPITAL LETTER O WITH CIRCUMFLEX AND HOOK ABOVE
<dead_tilde>    <dead_circumflex> <O>   :  "Ỗ"  U1ED6  #  LATIN CAPITAL LETTER O WITH CIRCUMFLEX AND TILDE
# Sequences with COMBINING DIAERESIS / <dead_diaeresis>
<dead_macron>   <dead_diaeresis> <a>    :  "ǟ"  U01DF  #  LATIN SMALL LETTER A WITH DIAERESIS AND MACRON
<dead_macron>   <dead_diaeresis> <A>    :  "Ǟ"  U01DE  #  LATIN CAPITAL LETTER A WITH DIAERESIS AND MACRON
# Sequences with COMBINING DOT ABOVE / <dead_abovedot>
                <dead_abovedot> <a>     :  "ȧ"  U0227  #  LATIN SMALL LETTER A WITH DOT ABOVE
<dead_macron>   <dead_abovedot> <a>     :  "ǡ"  U01E1  #  LATIN SMALL LETTER A WITH DOT ABOVE AND MACRON
                <dead_abovedot> <A>     :  "Ȧ"  U0226  #  LATIN CAPITAL LETTER A WITH DOT ABOVE
<dead_macron>   <dead_abovedot> <A>     :  "Ǡ"  U01E0  #  LATIN CAPITAL LETTER A WITH DOT ABOVE AND MACRON
# Sequences with COMBINING HOOK ABOVE / <dead_hook>
<dead_hook> <a>                         :  "ả"  U1EA3  #  LATIN SMALL LETTER A WITH HOOK ABOVE
<dead_hook> <A>                         :  "Ả"  U1EA2  #  LATIN CAPITAL LETTER A WITH HOOK ABOVE
<dead_hook> <e>                         :  "ẻ"  U1EBB  #  LATIN SMALL LETTER E WITH HOOK ABOVE
<dead_hook> <E>                         :  "Ẻ"  U1EBA  #  LATIN CAPITAL LETTER E WITH HOOK ABOVE
<dead_hook> <i>                         :  "ỉ"  U1EC9  #  LATIN SMALL LETTER I WITH HOOK ABOVE
<dead_hook> <I>                         :  "Ỉ"  U1EC8  #  LATIN CAPITAL LETTER I WITH HOOK ABOVE
<dead_hook> <o>                         :  "ỏ"  U1ECF  #  LATIN SMALL LETTER O WITH HOOK ABOVE
<dead_hook> <O>                         :  "Ỏ"  U1ECE  #  LATIN CAPITAL LETTER O WITH HOOK ABOVE
<dead_hook> <u>                         :  "ủ"  U1EE7  #  LATIN SMALL LETTER U WITH HOOK ABOVE
<dead_hook> <U>                         :  "Ủ"  U1EE6  #  LATIN CAPITAL LETTER U WITH HOOK ABOVE
<dead_hook> <y>                         :  "ỷ"  U1EF7  #  LATIN SMALL LETTER Y WITH HOOK ABOVE
<dead_hook> <Y>                         :  "Ỷ"  U1EF6  #  LATIN CAPITAL LETTER Y WITH HOOK ABOVE
# Sequences with COMBINING HORN / <dead_horn>
                <dead_horn> <o>         :  "ơ"  U01A1  #  LATIN SMALL LETTER O WITH HORN
<dead_acute>    <dead_horn> <o>         :  "ớ"  U1EDB  #  LATIN SMALL LETTER O WITH HORN AND ACUTE
<dead_belowdot> <dead_horn> <o>         :  "ợ"  U1EE3  #  LATIN SMALL LETTER O WITH HORN AND DOT BELOW
<dead_grave>    <dead_horn> <o>         :  "ờ"  U1EDD  #  LATIN SMALL LETTER O WITH HORN AND GRAVE
<dead_hook>     <dead_horn> <o>         :  "ờ"  U1EDD  #  LATIN SMALL LETTER O WITH HORN AND HOOK ABOVE
<dead_tilde>    <dead_horn> <o>         :  "ỡ"  U1EE1  #  LATIN SMALL LETTER O WITH HORN AND TILDE
                <dead_horn> <O>         :  "Ơ"  U01A0  #  LATIN CAPITAL LETTER O WITH HORN
<dead_acute>    <dead_horn> <O>         :  "Ớ"  U1EDA  #  LATIN CAPITAL LETTER O WITH HORN AND ACUTE
<dead_belowdot> <dead_horn> <O>         :  "Ợ"  U1EE2  #  LATIN CAPITAL LETTER O WITH HORN AND DOT BELOW
<dead_grave>    <dead_horn> <O>         :  "Ờ"  U1EDC  #  LATIN CAPITAL LETTER O WITH HORN AND GRAVE
<dead_hook>     <dead_horn> <O>         :  "Ở"  U1EDE  #  LATIN CAPITAL LETTER O WITH HORN AND HOOK ABOVE
<dead_tilde>    <dead_horn> <O>         :  "Ỡ"  U1EE0  #  LATIN CAPITAL LETTER O WITH HORN AND TILDE
                <dead_horn> <u>         :  "ư"  U01B0  #  LATIN SMALL LETTER U WITH HORN
<dead_acute>    <dead_horn> <u>         :  "ứ"  U1EE9  #  LATIN SMALL LETTER U WITH HORN AND ACUTE
<dead_belowdot> <dead_horn> <u>         :  "ự"  U1EF1  #  LATIN SMALL LETTER U WITH HORN AND DOT BELOW
<dead_grave>    <dead_horn> <u>         :  "ừ"  U1EEB  #  LATIN SMALL LETTER U WITH HORN AND GRAVE
<dead_hook>     <dead_horn> <u>         :  "ử"  U1EED  #  LATIN SMALL LETTER U WITH HORN AND HOOK ABOVE
<dead_tilde>    <dead_horn> <u>         :  "ữ"  U1EEF  #  LATIN SMALL LETTER U WITH HORN AND TILDE
                <dead_horn> <U>         :  "Ư"  U01AF  #  LATIN CAPITAL LETTER U WITH HORN
<dead_acute>    <dead_horn> <U>         :  "Ứ"  U1EE8  #  LATIN CAPITAL LETTER U WITH HORN AND ACUTE
<dead_belowdot> <dead_horn> <U>         :  "Ự"  U1EF0  #  LATIN CAPITAL LETTER U WITH HORN AND DOT BELOW
<dead_grave>    <dead_horn> <U>         :  "Ừ"  U1EEA  #  LATIN CAPITAL LETTER U WITH HORN AND GRAVE
<dead_hook>     <dead_horn> <U>         :  "Ử"  U1EEC  #  LATIN CAPITAL LETTER U WITH HORN AND HOOK ABOVE
<dead_tilde>    <dead_horn> <U>         :  "Ữ"  U1EEE  #  LATIN CAPITAL LETTER U WITH HORN AND TILDE
# Sequences with COMBINING OGONEK / <dead_ogonek>
<dead_macron>   <dead_ogonek> <o>       :  "ǭ"  U01ED  #  LATIN SMALL LETTER O WITH OGONEK AND MACRON
<dead_macron>   <dead_ogonek> <O>       :  "Ǭ"  U01EC  #  LATIN CAPITAL LETTER O WITH OGONEK AND MACRON
# Sequences with COMBINING RING ABOVE / <dead_abovering>
<dead_acute>    <dead_abovering> <a>    :  "ǻ"  U01FB  #  LATIN SMALL LETTER A WITH RING ABOVE AND ACUTE
<dead_acute>    <dead_abovering> <A>    :  "Ǻ"  U01FA  #  LATIN CAPITAL LETTER A WITH RING ABOVE AND ACUTE
# Additional sequences from Annex 5 for the reportoire of the MES-2
# (Multilingual European Subset No. 2 in ISO/IEC 10646, Collection 282)
<dead_hook> <f>                         :  "ƒ"  U0192  #  LATIN SMALL LETTER F WITH HOOK
<Multi_key> <f> <i>                     :  "ﬁ"  UFB01  #  LATIN SMALL LIGATURE FI
<Multi_key> <f> <l>                     :  "ﬂ"  UFB02  #  LATIN SMALL LIGATURE FL
<Multi_key> <i> <j>                     :  "ĳ"  U0133  #  LATIN SMALL LIGATURE IJ
<Multi_key> <I> <J>                     :  "Ĳ"  U0132  #  LATIN CAPITAL LIGATURE IJ
<dead_abovedot> <l>                     :  "ŀ"  U0140  #  LATIN SMALL LETTER L WITH MIDDLE DOT
<dead_abovedot> <L>                     :  "Ŀ"  U013F  #  LATIN CAPITAL LETTER L WITH MIDDLE DOT
<Multi_key> <apostrophe> <space> <n>    :  "ŉ"  U0149  #  LATIN SMALL LETTER N PRECEDED BY APOSTROPHE
<dead_hook> <r>                         :  "ɼ"  U027C  #  LATIN SMALL LETTER R WITH LONG LEG
<Multi_key> <f> <s>                     :  "ſ"  U017F  #  LATIN SMALL LETTER LONG S
<dead_abovedot> <Multi_key> <f> <s>     :  "ẛ"  U1E9B  #  LATIN SMALL LETTER LONG S WITH DOT ABOVE
<Multi_key> <dead_abovedot> <f> <s>     :  "ẛ"  U1E9B  #  LATIN SMALL LETTER LONG S WITH DOT ABOVE
