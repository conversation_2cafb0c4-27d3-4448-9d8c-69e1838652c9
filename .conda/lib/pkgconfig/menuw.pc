# pkg-config file generated by gen-pkgconfig
# vile:makemode

prefix=/data1/home/<USER>/EQACL/finetune/.conda
exec_prefix=${prefix}
libdir=${exec_prefix}/lib
includedir=${prefix}/include/ncursesw
abi_version=6
major_version=6
version=6.4.20221231

Name: menuw
Description: ncurses 6.4 add-on library
Version: ${version}
URL: https://invisible-island.net/ncurses
Requires.private: ncursesw
Libs:  -L/data1/home/<USER>/EQACL/finetune/.conda/lib -Wl,-O2 -Wl,--sort-common -Wl,--disable-new-dtags -Wl,--gc-sections -Wl,-rpath,/data1/home/<USER>/EQACL/finetune/.conda/lib -Wl,-rpath-link,/data1/home/<USER>/EQACL/finetune/.conda/lib -lmenuw
Libs.private:  
Cflags:  -D_GNU_SOURCE -DNCURSES_WIDECHAR -I${includedir} -I/data1/home/<USER>/EQACL/finetune/.conda/include
