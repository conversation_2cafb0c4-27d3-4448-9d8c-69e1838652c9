#
# UTF-8 (Unicode) compose sequences for the Ethiopian layout.
# Designed as part of the OLPC project.
#
# Original author: <PERSON> <<EMAIL>>, 2007.
#
# Include the base sequences too (for other layouts):
include "/data1/home/<USER>/EQACL/finetune/.conda/share/X11/locale/en_US.UTF-8/Compose"
#
# Group I
#
# Modifier: /u/
<dead_u> <U1200> : "ሁ" U1201 # key h (base character ሀ)
<dead_u> <U1208> : "ሉ" U1209 # key l (base character ለ)
<dead_u> <U1218> : "ሙ" U1219 # key m (base character መ)
<dead_u> <U1228> : "ሩ" U1229 # key r (base character ረ)
<dead_u> <U1230> : "ሱ" U1231 # key s (base character ሰ)
<dead_u> <U1240> : "ቁ" U1241 # key q (base character ቀ)
<dead_u> <U1260> : "ቡ" U1261 # key b (base character በ)
<dead_u> <U1270> : "ቱ" U1271 # key t (base character ተ)
<dead_u> <U1290> : "ኑ" U1291 # key n (base character ነ)
<dead_u> <U12A0> : "ኡ" U12A1 # key x (base character አ)
<dead_u> <U12A8> : "ኩ" U12A9 # key k (base character ከ)
<dead_u> <U12C8> : "ዉ" U12C9 # key w (base character ወ)
<dead_u> <U12D8> : "ዙ" U12D9 # key z (base character ዘ)
<dead_u> <U12E8> : "ዩ" U12E9 # key y (base character የ)
<dead_u> <U12F0> : "ዱ" U12F1 # key d (base character ደ)
<dead_u> <U1300> : "ጁ" U1301 # key j (base character ጀ)
<dead_u> <U1308> : "ጉ" U1309 # key g (base character ገ)
<dead_u> <U1340> : "ፁ" U1341 # key [ (base character ፀ)
<dead_u> <U1348> : "ፉ" U1349 # key f (base character ፈ)
<dead_u> <U1350> : "ፑ" U1351 # key p (base character ፐ)
<dead_u> <U1238> : "ሹ" U1239 # key v (base character ሸ)
<dead_u> <U1328> : "ጩ" U1329 # key ] (base character ጨ)
# Modifier: /i/
<dead_i> <U1200> : "ሂ" U1202 # key h (base character ሀ)
<dead_i> <U1208> : "ሊ" U120A # key l (base character ለ)
<dead_i> <U1218> : "ሚ" U121A # key m (base character መ)
<dead_i> <U1228> : "ሪ" U122A # key r (base character ረ)
<dead_i> <U1230> : "ሲ" U1232 # key s (base character ሰ)
<dead_i> <U1240> : "ቂ" U1242 # key q (base character ቀ)
<dead_i> <U1260> : "ቢ" U1262 # key b (base character በ)
<dead_i> <U1270> : "ቲ" U1272 # key t (base character ተ)
<dead_i> <U1290> : "ኒ" U1292 # key n (base character ነ)
<dead_i> <U12A0> : "ኢ" U12A2 # key x (base character አ)
<dead_i> <U12A8> : "ኪ" U12AA # key k (base character ከ)
<dead_i> <U12C8> : "ዊ" U12CA # key w (base character ወ)
<dead_i> <U12D8> : "ዚ" U12DA # key z (base character ዘ)
<dead_i> <U12E8> : "ዪ" U12EA # key y (base character የ)
<dead_i> <U12F0> : "ዲ" U12F2 # key d (base character ደ)
<dead_i> <U1300> : "ጂ" U1302 # key j (base character ጀ)
<dead_i> <U1308> : "ጊ" U130A # key g (base character ገ)
<dead_i> <U1340> : "ፂ" U1342 # key [ (base character ፀ)
<dead_i> <U1348> : "ፊ" U134A # key f (base character ፈ)
<dead_i> <U1350> : "ፒ" U1352 # key p (base character ፐ)
<dead_i> <U1238> : "ሺ" U123A # key v (base character ሸ)
<dead_i> <U1328> : "ጪ" U132A # key ] (base character ጨ)
# Modifier: /a/
<dead_a> <U1200> : "ሃ" U1203 # key h (base character ሀ)
<dead_a> <U1208> : "ላ" U120B # key l (base character ለ)
<dead_a> <U1218> : "ማ" U121B # key m (base character መ)
<dead_a> <U1228> : "ራ" U122B # key r (base character ረ)
<dead_a> <U1230> : "ሳ" U1233 # key s (base character ሰ)
<dead_a> <U1240> : "ቃ" U1243 # key q (base character ቀ)
<dead_a> <U1260> : "ባ" U1263 # key b (base character በ)
<dead_a> <U1270> : "ታ" U1273 # key t (base character ተ)
<dead_a> <U1290> : "ና" U1293 # key n (base character ነ)
<dead_a> <U12A0> : "ኣ" U12A3 # key x (base character አ)
<dead_a> <U12A8> : "ካ" U12AB # key k (base character ከ)
<dead_a> <U12C8> : "ዋ" U12CB # key w (base character ወ)
<dead_a> <U12D8> : "ዛ" U12DB # key z (base character ዘ)
<dead_a> <U12E8> : "ያ" U12EB # key y (base character የ)
<dead_a> <U12F0> : "ዳ" U12F3 # key d (base character ደ)
<dead_a> <U1300> : "ጃ" U1303 # key j (base character ጀ)
<dead_a> <U1308> : "ጋ" U130B # key g (base character ገ)
<dead_a> <U1340> : "ፃ" U1343 # key [ (base character ፀ)
<dead_a> <U1348> : "ፋ" U134B # key f (base character ፈ)
<dead_a> <U1350> : "ፓ" U1353 # key p (base character ፐ)
<dead_a> <U1238> : "ሻ" U123B # key v (base character ሸ)
<dead_a> <U1328> : "ጫ" U132B # key ] (base character ጨ)
# Modifier: /e/
<dead_e> <U1200> : "ሄ" U1204 # key h (base character ሀ)
<dead_e> <U1208> : "ሌ" U120C # key l (base character ለ)
<dead_e> <U1218> : "ሜ" U121C # key m (base character መ)
<dead_e> <U1228> : "ሬ" U122C # key r (base character ረ)
<dead_e> <U1230> : "ሴ" U1234 # key s (base character ሰ)
<dead_e> <U1240> : "ቄ" U1244 # key q (base character ቀ)
<dead_e> <U1260> : "ቤ" U1264 # key b (base character በ)
<dead_e> <U1270> : "ቴ" U1274 # key t (base character ተ)
<dead_e> <U1290> : "ኔ" U1294 # key n (base character ነ)
<dead_e> <U12A0> : "ኤ" U12A4 # key x (base character አ)
<dead_e> <U12A8> : "ኬ" U12AC # key k (base character ከ)
<dead_e> <U12C8> : "ዌ" U12CC # key w (base character ወ)
<dead_e> <U12D8> : "ዜ" U12DC # key z (base character ዘ)
<dead_e> <U12E8> : "ዬ" U12EC # key y (base character የ)
<dead_e> <U12F0> : "ዴ" U12F4 # key d (base character ደ)
<dead_e> <U1300> : "ጄ" U1304 # key j (base character ጀ)
<dead_e> <U1308> : "ጌ" U130C # key g (base character ገ)
<dead_e> <U1340> : "ፄ" U1344 # key [ (base character ፀ)
<dead_e> <U1348> : "ፌ" U134C # key f (base character ፈ)
<dead_e> <U1350> : "ፔ" U1354 # key p (base character ፐ)
<dead_e> <U1238> : "ሼ" U123C # key v (base character ሸ)
<dead_e> <U1328> : "ጬ" U132C # key ] (base character ጨ)
# Modifier: /C/
<dead_SCHWA> <U1200> : "ህ" U1205 # key h (base character ሀ)
<dead_SCHWA> <U1208> : "ል" U120D # key l (base character ለ)
<dead_SCHWA> <U1218> : "ም" U121D # key m (base character መ)
<dead_SCHWA> <U1228> : "ር" U122D # key r (base character ረ)
<dead_SCHWA> <U1230> : "ስ" U1235 # key s (base character ሰ)
<dead_SCHWA> <U1240> : "ቅ" U1245 # key q (base character ቀ)
<dead_SCHWA> <U1260> : "ብ" U1265 # key b (base character በ)
<dead_SCHWA> <U1270> : "ት" U1275 # key t (base character ተ)
<dead_SCHWA> <U1290> : "ን" U1295 # key n (base character ነ)
<dead_SCHWA> <U12A0> : "እ" U12A5 # key x (base character አ)
<dead_SCHWA> <U12A8> : "ክ" U12AD # key k (base character ከ)
<dead_SCHWA> <U12C8> : "ው" U12CD # key w (base character ወ)
<dead_SCHWA> <U12D8> : "ዝ" U12DD # key z (base character ዘ)
<dead_SCHWA> <U12E8> : "ይ" U12ED # key y (base character የ)
<dead_SCHWA> <U12F0> : "ድ" U12F5 # key d (base character ደ)
<dead_SCHWA> <U1300> : "ጅ" U1305 # key j (base character ጀ)
<dead_SCHWA> <U1308> : "ግ" U130D # key g (base character ገ)
<dead_SCHWA> <U1340> : "ፅ" U1345 # key [ (base character ፀ)
<dead_SCHWA> <U1348> : "ፍ" U134D # key f (base character ፈ)
<dead_SCHWA> <U1350> : "ፕ" U1355 # key p (base character ፐ)
<dead_SCHWA> <U1238> : "ሽ" U123D # key v (base character ሸ)
<dead_SCHWA> <U1328> : "ጭ" U132D # key ] (base character ጨ)
# Modifier: /o/
<dead_o> <U1200> : "ሆ" U1206 # key h (base character ሀ)
<dead_o> <U1208> : "ሎ" U120E # key l (base character ለ)
<dead_o> <U1218> : "ሞ" U121E # key m (base character መ)
<dead_o> <U1228> : "ሮ" U122E # key r (base character ረ)
<dead_o> <U1230> : "ሶ" U1236 # key s (base character ሰ)
<dead_o> <U1240> : "ቆ" U1246 # key q (base character ቀ)
<dead_o> <U1260> : "ቦ" U1266 # key b (base character በ)
<dead_o> <U1270> : "ቶ" U1276 # key t (base character ተ)
<dead_o> <U1290> : "ኖ" U1296 # key n (base character ነ)
<dead_o> <U12A0> : "ኦ" U12A6 # key x (base character አ)
<dead_o> <U12A8> : "ኮ" U12AE # key k (base character ከ)
<dead_o> <U12C8> : "ዎ" U12CE # key w (base character ወ)
<dead_o> <U12D8> : "ዞ" U12DE # key z (base character ዘ)
<dead_o> <U12E8> : "ዮ" U12EE # key y (base character የ)
<dead_o> <U12F0> : "ዶ" U12F6 # key d (base character ደ)
<dead_o> <U1300> : "ጆ" U1306 # key j (base character ጀ)
<dead_o> <U1308> : "ጎ" U130E # key g (base character ገ)
<dead_o> <U1340> : "ፆ" U1346 # key [ (base character ፀ)
<dead_o> <U1348> : "ፎ" U134E # key f (base character ፈ)
<dead_o> <U1350> : "ፖ" U1356 # key p (base character ፐ)
<dead_o> <U1238> : "ሾ" U123E # key v (base character ሸ)
<dead_o> <U1328> : "ጮ" U132E # key ] (base character ጨ)
# Modifier: /A/
<dead_A> <U1208> : "ሏ" U120F # key l (base character ለ)
<dead_A> <U1218> : "ሟ" U121F # key m (base character መ)
<dead_A> <U1228> : "ሯ" U122F # key r (base character ረ)
<dead_A> <U1230> : "ሷ" U1237 # key s (base character ሰ)
<dead_A> <U1240> : "ቋ" U124B # key q (base character ቀ)
<dead_A> <U1260> : "ቧ" U1267 # key b (base character በ)
<dead_A> <U1270> : "ቷ" U1277 # key t (base character ተ)
<dead_A> <U1290> : "ኗ" U1297 # key n (base character ነ)
<dead_A> <U12A0> : "ኧ" U12A7 # key x (base character አ)
<dead_A> <U12A8> : "ኳ" U12B3 # key k (base character ከ)
<dead_A> <U12D8> : "ዟ" U12DF # key z (base character ዘ)
<dead_A> <U12F0> : "ዷ" U12F7 # key d (base character ደ)
<dead_A> <U1300> : "ጇ" U1307 # key j (base character ጀ)
<dead_A> <U1308> : "ጓ" U1313 # key g (base character ገ)
<dead_A> <U1348> : "ፏ" U134F # key f (base character ፈ)
<dead_A> <U1350> : "ፗ" U1357 # key p (base character ፐ)
<dead_A> <U1238> : "ሿ" U123F # key v (base character ሸ)
<dead_A> <U1328> : "ጯ" U132F # key ] (base character ጨ)
# Modifier: /U/
<dead_U> <U1240> : "ቍ" U124D # key q (base character ቀ)
<dead_U> <U12A8> : "ኵ" U12B5 # key k (base character ከ)
<dead_U> <U1308> : "ጕ" U1315 # key g (base character ገ)
# Modifier: /I/
<dead_I> <U1240> : "ቊ" U124A # key q (base character ቀ)
<dead_I> <U12A8> : "ኲ" U12B2 # key k (base character ከ)
<dead_I> <U1308> : "ጒ" U1312 # key g (base character ገ)
# Modifier: /E/
<dead_E> <U1240> : "ቌ" U124C # key q (base character ቀ)
<dead_E> <U12A8> : "ኴ" U12B4 # key k (base character ከ)
<dead_E> <U1308> : "ጔ" U1314 # key g (base character ገ)
# Modifier: /O/
<dead_O> <U1240> : "ቈ" U1248 # key q (base character ቀ)
<dead_O> <U12A8> : "ኰ" U12B0 # key k (base character ከ)
<dead_O> <U1308> : "ጐ" U1310 # key g (base character ገ)
#
# Group II
#
# Modifier: /u/
<dead_u> <U1210> : "ሑ" U1211 # key h (base character ሐ)
<dead_u> <U1220> : "ሡ" U1221 # key s (base character ሠ)
<dead_u> <U1250> : "ቑ" U1251 # key q (base character ቐ)
<dead_u> <U1278> : "ቹ" U1279 # key c (base character ቸ)
<dead_u> <U1320> : "ጡ" U1321 # key t (base character ጠ)
<dead_u> <U1298> : "ኙ" U1299 # key n (base character ኘ)
<dead_u> <U12D0> : "ዑ" U12D1 # key x (base character ዐ)
<dead_u> <U12B8> : "ኹ" U12B9 # key k (base character ኸ)
<dead_u> <U12E0> : "ዡ" U12E1 # key z (base character ዠ)
<dead_u> <U12F8> : "ዹ" U12F9 # key d (base character ዸ)
<dead_u> <U1318> : "ጙ" U1319 # key g (base character ጘ)
<dead_u> <U1338> : "ጹ" U1339 # key [ (base character ጸ)
<dead_u> <U1330> : "ጱ" U1331 # key p (base character ጰ)
<dead_u> <U1280> : "ኁ" U1281 # key ] (base character ኀ)
<dead_u> <U1268> : "ቩ" U1269 # key v (base character ቨ)
# Modifier: /i/
<dead_i> <U1210> : "ሒ" U1212 # key h (base character ሐ)
<dead_i> <U1220> : "ሢ" U1222 # key s (base character ሠ)
<dead_i> <U1250> : "ቒ" U1252 # key q (base character ቐ)
<dead_i> <U1278> : "ቺ" U127A # key c (base character ቸ)
<dead_i> <U1320> : "ጢ" U1322 # key t (base character ጠ)
<dead_i> <U1298> : "ኚ" U129A # key n (base character ኘ)
<dead_i> <U12D0> : "ዒ" U12D2 # key x (base character ዐ)
<dead_i> <U12B8> : "ኺ" U12BA # key k (base character ኸ)
<dead_i> <U12E0> : "ዢ" U12E2 # key z (base character ዠ)
<dead_i> <U12F8> : "ዺ" U12FA # key d (base character ዸ)
<dead_i> <U1318> : "ጚ" U131A # key g (base character ጘ)
<dead_i> <U1338> : "ጺ" U133A # key [ (base character ጸ)
<dead_i> <U1330> : "ጲ" U1332 # key p (base character ጰ)
<dead_i> <U1280> : "ኂ" U1282 # key ] (base character ኀ)
<dead_i> <U1268> : "ቪ" U126A # key v (base character ቨ)
# Modifier: /a/
<dead_a> <U1210> : "ሓ" U1213 # key h (base character ሐ)
<dead_a> <U1220> : "ሣ" U1223 # key s (base character ሠ)
<dead_a> <U1250> : "ቓ" U1253 # key q (base character ቐ)
<dead_a> <U1278> : "ቻ" U127B # key c (base character ቸ)
<dead_a> <U1320> : "ጣ" U1323 # key t (base character ጠ)
<dead_a> <U1298> : "ኛ" U129B # key n (base character ኘ)
<dead_a> <U12D0> : "ዓ" U12D3 # key x (base character ዐ)
<dead_a> <U12B8> : "ኻ" U12BB # key k (base character ኸ)
<dead_a> <U12E0> : "ዣ" U12E3 # key z (base character ዠ)
<dead_a> <U12F8> : "ዻ" U12FB # key d (base character ዸ)
<dead_a> <U1318> : "ጛ" U131B # key g (base character ጘ)
<dead_a> <U1338> : "ጻ" U133B # key [ (base character ጸ)
<dead_a> <U1330> : "ጳ" U1333 # key p (base character ጰ)
<dead_a> <U1280> : "ኃ" U1283 # key ] (base character ኀ)
<dead_a> <U1268> : "ቫ" U126B # key v (base character ቨ)
# Modifier: /e/
<dead_e> <U1210> : "ሔ" U1214 # key h (base character ሐ)
<dead_e> <U1220> : "ሤ" U1224 # key s (base character ሠ)
<dead_e> <U1250> : "ቔ" U1254 # key q (base character ቐ)
<dead_e> <U1278> : "ቼ" U127C # key c (base character ቸ)
<dead_e> <U1320> : "ጤ" U1324 # key t (base character ጠ)
<dead_e> <U1298> : "ኜ" U129C # key n (base character ኘ)
<dead_e> <U12D0> : "ዔ" U12D4 # key x (base character ዐ)
<dead_e> <U12B8> : "ኼ" U12BC # key k (base character ኸ)
<dead_e> <U12E0> : "ዤ" U12E4 # key z (base character ዠ)
<dead_e> <U12F8> : "ዼ" U12FC # key d (base character ዸ)
<dead_e> <U1318> : "ጜ" U131C # key g (base character ጘ)
<dead_e> <U1338> : "ጼ" U133C # key [ (base character ጸ)
<dead_e> <U1330> : "ጴ" U1334 # key p (base character ጰ)
<dead_e> <U1280> : "ኄ" U1284 # key ] (base character ኀ)
<dead_e> <U1268> : "ቬ" U126C # key v (base character ቨ)
# Modifier: /C/
<dead_SCHWA> <U1210> : "ሕ" U1215 # key h (base character ሐ)
<dead_SCHWA> <U1220> : "ሥ" U1225 # key s (base character ሠ)
<dead_SCHWA> <U1250> : "ቕ" U1255 # key q (base character ቐ)
<dead_SCHWA> <U1278> : "ች" U127D # key c (base character ቸ)
<dead_SCHWA> <U1320> : "ጥ" U1325 # key t (base character ጠ)
<dead_SCHWA> <U1298> : "ኝ" U129D # key n (base character ኘ)
<dead_SCHWA> <U12D0> : "ዕ" U12D5 # key x (base character ዐ)
<dead_SCHWA> <U12B8> : "ኽ" U12BD # key k (base character ኸ)
<dead_SCHWA> <U12E0> : "ዥ" U12E5 # key z (base character ዠ)
<dead_SCHWA> <U12F8> : "ዽ" U12FD # key d (base character ዸ)
<dead_SCHWA> <U1318> : "ጝ" U131D # key g (base character ጘ)
<dead_SCHWA> <U1338> : "ጽ" U133D # key [ (base character ጸ)
<dead_SCHWA> <U1330> : "ጵ" U1335 # key p (base character ጰ)
<dead_SCHWA> <U1280> : "ኅ" U1285 # key ] (base character ኀ)
<dead_SCHWA> <U1268> : "ቭ" U126D # key v (base character ቨ)
# Modifier: /o/
<dead_o> <U1210> : "ሖ" U1216 # key h (base character ሐ)
<dead_o> <U1220> : "ሦ" U1226 # key s (base character ሠ)
<dead_o> <U1250> : "ቖ" U1256 # key q (base character ቐ)
<dead_o> <U1278> : "ቾ" U127E # key c (base character ቸ)
<dead_o> <U1320> : "ጦ" U1326 # key t (base character ጠ)
<dead_o> <U1298> : "ኞ" U129E # key n (base character ኘ)
<dead_o> <U12D0> : "ዖ" U12D6 # key x (base character ዐ)
<dead_o> <U12B8> : "ኾ" U12BE # key k (base character ኸ)
<dead_o> <U12E0> : "ዦ" U12E6 # key z (base character ዠ)
<dead_o> <U12F8> : "ዾ" U12FE # key d (base character ዸ)
<dead_o> <U1318> : "ጞ" U131E # key g (base character ጘ)
<dead_o> <U1338> : "ጾ" U133E # key [ (base character ጸ)
<dead_o> <U1330> : "ጶ" U1336 # key p (base character ጰ)
<dead_o> <U1280> : "ኆ" U1286 # key ] (base character ኀ)
<dead_o> <U1268> : "ቮ" U126E # key v (base character ቨ)
# Modifier: /A/
<dead_A> <U1210> : "ሗ" U1217 # key h (base character ሐ)
<dead_A> <U1220> : "ሧ" U1227 # key s (base character ሠ)
<dead_A> <U1250> : "ቛ" U125B # key q (base character ቐ)
<dead_A> <U1278> : "ቿ" U127F # key c (base character ቸ)
<dead_A> <U1320> : "ጧ" U1327 # key t (base character ጠ)
<dead_A> <U1298> : "ኟ" U129F # key n (base character ኘ)
<dead_A> <U12B8> : "ዃ" U12C3 # key k (base character ኸ)
<dead_A> <U12E0> : "ዧ" U12E7 # key z (base character ዠ)
<dead_A> <U12F8> : "ዿ" U12FF # key d (base character ዸ)
<dead_A> <U1338> : "ጿ" U133F # key [ (base character ጸ)
<dead_A> <U1330> : "ጷ" U1337 # key p (base character ጰ)
<dead_A> <U1280> : "ኋ" U128B # key ] (base character ኀ)
<dead_A> <U1268> : "ቯ" U126F # key v (base character ቨ)
# Modifier: /U/
<dead_U> <U1250> : "ቝ" U125D # key q (base character ቐ)
<dead_U> <U12B8> : "ዅ" U12C5 # key k (base character ኸ)
# Modifier: /I/
<dead_I> <U1250> : "ቚ" U125A # key q (base character ቐ)
<dead_I> <U12B8> : "ዂ" U12C2 # key k (base character ኸ)
# Modifier: /E/
<dead_E> <U1250> : "ቜ" U125C # key q (base character ቐ)
<dead_E> <U12B8> : "ዄ" U12C4 # key k (base character ኸ)
# Modifier: /O/
<dead_O> <U1250> : "ቘ" U1258 # key q (base character ቐ)
<dead_O> <U12B8> : "ዀ" U12C0 # key k (base character ኸ)
#
# Group III
#
<backslash> <quotedbl> : "፥" U1365 # key "
<backslash> <apostrophe> : "፦" U1366 # key '
<backslash> <minus> : "|" U007C # key -
<backslash> <underscore> : "¥" U00A5 # key _
<backslash> <question> : "፧" U1367 # key ?
