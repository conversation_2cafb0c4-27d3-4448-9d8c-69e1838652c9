/*
 * Copyright (c) 1991, Oracle and/or its affiliates.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice (including the next
 * paragraph) shall be included in all copies or substantial portions of the
 * Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 * THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.
 */
/************************************************************

Copyright 1991, 1998  The Open Group

Permission to use, copy, modify, distribute, and sell this software and its
documentation for any purpose is hereby granted without fee, provided that
the above copyright notice appear in all copies and that both that
copyright notice and this permission notice appear in supporting
documentation.

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE
OPEN GROUP BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN
AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the name of The Open Group shall not be
used in advertising or otherwise to promote the sale, use or other dealings
in this Software without prior written authorization from The Open Group.

***********************************************************/

/*
 * Floating Accent
 */

#define SunXK_FA_Grave               0x1005ff00
#define SunXK_FA_Circum              0x1005ff01
#define SunXK_FA_Tilde               0x1005ff02
#define SunXK_FA_Acute               0x1005ff03
#define SunXK_FA_Diaeresis           0x1005ff04
#define SunXK_FA_Cedilla             0x1005ff05

/*
 * Miscellaneous Functions
 */

#define SunXK_F36                    0x1005ff10  /* Labeled F11 */
#define SunXK_F37                    0x1005ff11  /* Labeled F12 */

#define SunXK_Sys_Req                0x1005ff60
#define SunXK_Print_Screen           0x0000ff61  /* Same as XK_Print */

/*
 * International & Multi-Key Character Composition
 */

#define SunXK_Compose                0x0000ff20  /* Same as XK_Multi_key */
#define SunXK_AltGraph               0x0000ff7e  /* Same as XK_Mode_switch */

/*
 * Cursor Control
 */

#define SunXK_PageUp                 0x0000ff55  /* Same as XK_Prior */
#define SunXK_PageDown               0x0000ff56  /* Same as XK_Next */

/*
 * Open Look Functions
 */

#define SunXK_Undo                   0x0000ff65  /* Same as XK_Undo */
#define SunXK_Again                  0x0000ff66  /* Same as XK_Redo */
#define SunXK_Find                   0x0000ff68  /* Same as XK_Find */
#define SunXK_Stop                   0x0000ff69  /* Same as XK_Cancel */
#define SunXK_Props                  0x1005ff70
#define SunXK_Front                  0x1005ff71
#define SunXK_Copy                   0x1005ff72
#define SunXK_Open                   0x1005ff73
#define SunXK_Paste                  0x1005ff74
#define SunXK_Cut                    0x1005ff75

#define SunXK_PowerSwitch            0x1005ff76
#define SunXK_AudioLowerVolume       0x1005ff77
#define SunXK_AudioMute              0x1005ff78
#define SunXK_AudioRaiseVolume       0x1005ff79
#define SunXK_VideoDegauss           0x1005ff7a
#define SunXK_VideoLowerBrightness   0x1005ff7b
#define SunXK_VideoRaiseBrightness   0x1005ff7c
#define SunXK_PowerSwitchShift       0x1005ff7d
