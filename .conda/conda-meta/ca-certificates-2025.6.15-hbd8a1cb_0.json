{"build": "hbd8a1cb_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["__unix"], "extracted_package_dir": "/data1/home/<USER>/anaconda3/pkgs/ca-certificates-2025.6.15-hbd8a1cb_0", "files": ["ssl/cacert.pem", "ssl/cert.pem"], "fn": "ca-certificates-2025.6.15-hbd8a1cb_0.conda", "license": "ISC", "link": {"source": "/data1/home/<USER>/anaconda3/pkgs/ca-certificates-2025.6.15-hbd8a1cb_0", "type": 1}, "md5": "72525f07d72806e3b639ad4504c30ce5", "name": "ca-certificates", "noarch": "generic", "package_tarball_full_path": "/data1/home/<USER>/anaconda3/pkgs/ca-certificates-2025.6.15-hbd8a1cb_0.conda", "package_type": "noarch_generic", "paths_data": {"paths": [{"_path": "ssl/cacert.pem", "path_type": "hardlink", "sha256": "b1cdd2d665758ef49d08f40ea13e1a826e5f0412e9e0940c921ed1021464cdc2", "sha256_in_prefix": "b1cdd2d665758ef49d08f40ea13e1a826e5f0412e9e0940c921ed1021464cdc2", "size_in_bytes": 281225}, {"_path": "ssl/cert.pem", "path_type": "softlink", "sha256": "b1cdd2d665758ef49d08f40ea13e1a826e5f0412e9e0940c921ed1021464cdc2", "size_in_bytes": 281225}], "paths_version": 1}, "requested_spec": "None", "sha256": "7cfec9804c84844ea544d98bda1d9121672b66ff7149141b8415ca42dfcd44f6", "size": 151069, "subdir": "noarch", "timestamp": 1749990087000, "url": "https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda", "version": "2025.6.15"}