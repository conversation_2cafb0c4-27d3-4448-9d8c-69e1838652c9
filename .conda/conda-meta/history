==> 2025-06-16 15:13:37 <==
# cmd: /data1/home/<USER>/anaconda3/bin/conda create -p ./.conda python=3.11
# conda version: 24.11.3
+defaults/linux-64::_libgcc_mutex-0.1-main
+defaults/linux-64::_openmp_mutex-5.1-1_gnu
+defaults/linux-64::bzip2-1.0.8-h5eee18b_6
+defaults/linux-64::ca-certificates-2025.2.25-h06a4308_0
+defaults/linux-64::expat-2.7.1-h6a678d5_0
+defaults/linux-64::ld_impl_linux-64-2.40-h12ee557_0
+defaults/linux-64::libffi-3.4.4-h6a678d5_1
+defaults/linux-64::libgcc-ng-11.2.0-h1234567_1
+defaults/linux-64::libgomp-11.2.0-h1234567_1
+defaults/linux-64::libstdcxx-ng-11.2.0-h1234567_1
+defaults/linux-64::libuuid-1.41.5-h5eee18b_0
+defaults/linux-64::libxcb-1.17.0-h9b100fa_0
+defaults/linux-64::ncurses-6.4-h6a678d5_0
+defaults/linux-64::openssl-3.0.16-h5eee18b_0
+defaults/linux-64::pthread-stubs-0.3-h0ce48e5_1
+defaults/linux-64::python-3.11.13-h1a3bd86_0
+defaults/linux-64::readline-8.2-h5eee18b_0
+defaults/linux-64::setuptools-78.1.1-py311h06a4308_0
+defaults/linux-64::sqlite-3.45.3-h5eee18b_0
+defaults/linux-64::tk-8.6.14-h993c535_1
+defaults/linux-64::wheel-0.45.1-py311h06a4308_0
+defaults/linux-64::xorg-libx11-1.8.12-h9b100fa_1
+defaults/linux-64::xorg-libxau-1.0.12-h9b100fa_0
+defaults/linux-64::xorg-libxdmcp-1.1.5-h9b100fa_0
+defaults/linux-64::xorg-xorgproto-2024.1-h5eee18b_1
+defaults/linux-64::xz-5.6.4-h5eee18b_1
+defaults/linux-64::zlib-1.2.13-h5eee18b_1
+defaults/noarch::pip-25.1-pyhc872135_2
+defaults/noarch::tzdata-2025b-h04d1e81_0
# update specs: ['python=3.11']
==> 2025-06-16 15:16:17 <==
# cmd: /data1/home/<USER>/anaconda3/bin/conda install tree -c conda-forge
# conda version: 24.11.3
-defaults/linux-64::ca-certificates-2025.2.25-h06a4308_0
-defaults/linux-64::libgcc-ng-11.2.0-h1234567_1
-defaults/linux-64::libgomp-11.2.0-h1234567_1
-defaults/linux-64::openssl-3.0.16-h5eee18b_0
+conda-forge/linux-64::libgcc-15.1.0-h767d61c_2
+conda-forge/linux-64::libgcc-ng-15.1.0-h69a702a_2
+conda-forge/linux-64::libgomp-15.1.0-h767d61c_2
+conda-forge/linux-64::openssl-3.5.0-h7b32b05_1
+conda-forge/linux-64::tree-2.2.1-hb9d3cd8_0
+conda-forge/noarch::ca-certificates-2025.6.15-hbd8a1cb_0
# update specs: ['tree']
