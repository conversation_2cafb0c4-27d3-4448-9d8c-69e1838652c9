# tcl pkg-config source file

prefix=/data1/home/<USER>/EQACL/finetune/.conda
exec_prefix=/data1/home/<USER>/EQACL/finetune/.conda
libdir=/data1/home/<USER>/EQACL/finetune/.conda/lib
includedir=${prefix}/include
libfile=libtcl8.6.so

Name: Tool Command Language
Description: Tcl is a powerful, easy-to-learn dynamic programming language, suitable for a wide range of uses.
URL: https://www.tcl-lang.org/
Version: 8.6.14
Requires.private: zlib >= 1.2.3
Libs: -L${libdir} -ltcl8.6 -ltclstub8.6
Libs.private: -ldl -lz  -lpthread -lm
Cflags: -I${includedir}
