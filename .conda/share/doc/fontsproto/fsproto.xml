<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE book PUBLIC "-//OASIS//DTD DocBook XML V4.3//EN"
                   "http://www.oasis-open.org/docbook/xml/4.3/docbookx.dtd"
[
<!ENTITY % defs SYSTEM "defs.ent"> %defs;
]>


<book id="fsproto">

<bookinfo>
   <title>The X Font Service Protocol</title>
   <subtitle>X Consortium Standard</subtitle>
   <releaseinfo>X Version 11, Release &fullrelvers;</releaseinfo>
   <releaseinfo>Version 2.0</releaseinfo>
   <authorgroup>
      <author>
         <firstname>Jim</firstname><surname>Fulton</surname>
         <affiliation><orgname>Network Computing Devices, Inc.</orgname></affiliation>
      </author>
   </authorgroup>
   <edition>Revised May 2, 1994</edition>
   <copyright><year>1991</year><holder>Network Computing Devices, Inc.</holder></copyright>

<legalnotice>
<para>
Permission to use, copy, modify, distribute, and sell this
documentation for any purpose is hereby granted without fee,
provided that the above copyright notice and this permission
notice appear in all copies.  Network Computing Devices, Inc.
makes no representations about the suitability for any purpose
of the information in this document.  This documentation is
provided &ldquo;as is&rdquo; without express or implied warranty.
</para>
</legalnotice>

<legalnotice>
<para role="multiLicensing">Copyright © 1994 X Consortium</para>
<para>
Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the &ldquo;Software&rdquo;), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:
</para>
<para>
The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.
</para>
<para>
THE SOFTWARE IS PROVIDED &ldquo;AS IS&rdquo;, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE
X CONSORTIUM BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN
AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
</para>
<para>
Except as contained in this notice, the name of the X Consortium shall not be
used in advertising or otherwise to promote the sale, use or other dealings
in this Software without prior written authorization from the X Consortium.
</para>
</legalnotice>
</bookinfo>

<chapter id='Introduction'>
<title>Introduction</title>
<para>
The management of fonts in large, heterogeneous environments is one of the
hardest aspects of using the X Window System
<footnote id='footnote1'><para>
<firstterm><trademark>X Window System</trademark></firstterm>
is a trademark of The Open Group.
</para></footnote>
.  Multiple formats and the lack of
a consistent mechanism for exporting font data to all displays on a network
prevent the transparent use of applications across different display platforms.
The X Font Service protocol is designed to address this and other issues, with
specific emphasis on the needs of the core X protocol.  Upward-compatible
changes (typically in the form of new requests) are expected as consensus is
reached on new features (particularly outline font support).
</para>
<para>
Currently, most X displays use network file protocols such as
<acronym>NFS</acronym> and <acronym>TFTP</acronym> to
obtain raw font data which they parse directly.  Since a common binary format
for this data doesn't exist, displays must be able to interpret a variety of
formats if they are to be used with different application hosts.  This leads to
wasted code and data space and a loss of interoperability as displays are used
in unforeseen environments.
</para>
<para>
By moving the interpretation of font data out of the X server into a separate
service on the network, these problems can be greatly reduced.  In addition,
new technologies, such as dynamically generating bitmaps from scaled or outline
fonts, can be provided to all displays transparently.  For horizontal text,
caching techniques and increased processor power can potentially make
rasterization more efficient on large, centralized hosts than on individual
displays.
</para>
<para>
Each font server provides sets of fonts that may be listed and queried for
header, property, glyph extents, and bitmap information.  This data is
transmitted over the network using a binary format (with variations to support
different bit- and byte-orders) designed to minimize the amount of processing
required by the display.  Since the font server, rather than the display, is
responsible for parsing the raw font data, new formats can be used by all
displays by modifying a single font server.
</para>
<para>
From the user's point of view, font servers are simply a new type of name in
the X font path.  Network name services allow descriptive names (such as
DEPARTMENT-FONTS or APPLICATION-FONTS) to be translated into proper network
addresses.  X displays send requests to and read replies from the font server
rather than reading directly from files.  Since the X Font Service protocol is
designed to allow subsets of the font data to be requested, displays may easily
implement a variety of strategies for fine-grained demand-loading of glyphs.
</para>
</chapter>

<chapter id='Architectural_Model'>
<title>Architectural Model</title>
<!-- .XS -->
<!-- (SN Architectural Model -->
<!-- .XE -->
<para>
In this document, the words <firstterm>client</firstterm> and
<firstterm>server</firstterm> refer to the consumer and
provider of a font, respectively, unless otherwise indicated.  It is important
to note that in this context, the X server is also a font client.
</para>
<para>
The X Font Service protocol does not require any changes to the core X protocol
or to any applications.  To the user, font servers are simply additional types
of font path elements.  As such, X servers may connect to multiple font
servers, as shown in Figure 2.1.  Although the font protocol is geared towards
the X Window System, it may be also used by other consumers of font data (such
as printer drivers).
</para>

<figure id="figure2.1" label="2.1"><title>Connecting to a Font Server</title>
<literallayout class="monospaced">
 ┌────────┐              ┌───────────────┐
 │   X1   ├──────────────┤               │
 │ Server │              │  Font Server  │
 └────────┘      ┌───────┤      1        │
                 │       └───────────────┘
 ┌────────┐      │
 │   X2   ├──────┘       ┌───────────────┐
 │ Server ├──────────────┤               │
 └────────┘              │  Font Server  │
                 ┌───────┤      2        │
┌─────────┐      │       └───────────────┘
│  other  │      │
│ clients ├──────┘
└─────────┘
</literallayout>
</figure>

<para>
Clients communicate with the font server using the request/reply/event model
over any mutually-understood virtual stream connection (such as
<acronym>TCP/IP</acronym>, DECnet,
<footnote id='footnote2'><para>
<firstterm><trademark>DECnet</trademark></firstterm> is a trademark
of Digital Equipment Corporation.
</para></footnote>
etc.).  Font servers are responsible for providing data in the bit and byte
orders requested by the client.  The set of requests and events provided in the
first version of the X Font Service protocol is limited to supporting the needs
of the bitmap-oriented core X Window System protocol.  Extensions are expected
as new needs evolve.
</para>
<para>
A font server reads raw font data from a variety of sources (possibly
including other font servers) and converts it into a common format that is
transmitted to the client using the protocol described in
<link linkend='Protocol'>Section 4</link>.  New font
formats are handled by adding new converters to a font server, as shown in
Figure 2.2.
</para>

<figure id="figure2.2" label="2.2"><title>Where Font Data Comes From</title>
<literallayout class="monospaced">
                ┌────────────┐
                │   client   │
                │ (X server) │
                └─────┬──────┘
                      │
                   network
                      │
┌─────────────────────┴──────────────────────┐
│                                            │
│                font server 1               │
│                                            │
├─────┬─────┬─────┬─────┬────┬─────┬───┬─────┤
│ bdf │ snf │ pcf │ atm │ f3 │ dwf │ │ │ ... │
└─────┴─────┴─────┴─────┴────┴─────┴─│─┴─────┘
                                     │
                                  network
                                     │
                               ┌─────┴────┐
                               │   font   │
                               │ server 2 │
                               └──────────┘
</literallayout>
</figure>

<para>
The server may choose to provide named sets of fonts called
<firstterm>catalogues</firstterm>.
Clients may specify which of the sets should be used in listing or opening a
font.
</para>

<para>
An event mechanism similar to that used in the X protocol is provided for
asynchronous notification of clients by the server.
</para>

<para>
Clients may provide authorization data for the server to be used in determining
(according to the server's licensing policy) whether or not access should be
granted to particular fonts.  This is particularly useful for clients whose
authorization changes over time (such as an X server that can verify the
identity of the user).
</para>
<para>
Implementations that wish to provide additional requests or events may use the
extension mechanism.  Adding to the core font service protocol (with the
accompanying change in the major or minor version numbers) is reserved to the X
Consortium.
</para>
</chapter>

<chapter id='Font_Server_Naming'>
<title>Font Server Naming</title>
<!-- .XS -->
<!-- (SN Font Server Naming -->
<!-- .XE -->
<para>
Font clients that expose font server names to the user are encouraged to
provide ways of naming font servers symbolically (e.g. DEPARTMENT-FONTS).
However, for environments that lack appropriate name services
transport-specific names are necessary.  Since these names do occur in the
protocol, clients and servers should support at least the applicable formats
described below.  Formats for additional transports may be registered with the
X Consortium.
</para>

<section id='TCPIP_Names'>
<title>TCP/IP Names</title>
<!-- .XS -->
<!-- (SN TCP/IP Names -->
<!-- .XE -->
<para>
The following syntax should be used for TCP/IP names:

<literallayout class="monospaced">
    <replaceable>TCP name</replaceable>  ::=  <literal>tcp/</literal> <replaceable>hostname</replaceable> <literal>:</literal> <replaceable>ipportnumber</replaceable> <optional><literal>/</literal> <replaceable>cataloguelist</replaceable></optional>
</literallayout>

where <replaceable>hostname</replaceable> is either symbolic (such as
<systemitem class="systemname">expo.lcs.mit.edu</systemitem>) or numeric
decimal (such as <systemitem class="ipaddress">***********</systemitem>).
The <replaceable>ipportnumber</replaceable> is the port on which the
font server is listening for connections.
The <replaceable>cataloguelist</replaceable> string at
the end is optional and specifies a plus-separated list of catalogues
that may be requested.  For example:
<literallayout class="monospaced">
     tcp/expo.lcs.mit.edu:8012/available+special
     tcp/***********:7890
</literallayout>
</para>
</section>

<section id='DECnet_Names'>
<title>DECnet Names</title>
<!-- .XS -->
<!-- (SN DECnet Names -->
<!-- .XE -->
<para>
The following syntax should be used for DECnet names:

<literallayout class="monospaced">
    <replaceable>DECnet name</replaceable>  ::=  <literal>decnet/</literal> <replaceable>nodename</replaceable> <literal>::font$</literal> <replaceable>objname</replaceable> <optional><literal>/</literal> <replaceable>cataloguelist</replaceable></optional>
</literallayout>

where <replaceable>nodename</replaceable> is either symbolic (such as
<systemitem class="systemname">SRVNOD</systemitem>) or the
numeric decimal form of the DECnet address (such as
<systemitem class="ipaddress">44.70</systemitem>).
The <replaceable>objname</replaceable> is normal, case-insensitive DECnet
object name.  The <replaceable>cataloguelist</replaceable> string
at the end is
optional and specifies a plus-separated list of catalogues that may be
requested.  For example:

<literallayout class="monospaced">
     DECNET/SRVNOD::FONT$DEFAULT/AVAILABLE
     decnet/44.70::font$other
</literallayout>
</para>
</section>
</chapter>

<chapter id='Protocol'>
<title>Protocol</title>
<!-- .XS -->
<!-- (SN Protocol -->
<!-- .XE -->
<para>
The protocol described below uses the request/reply/error model and is
specified using the same conventions outlined in
<olink targetdoc='x11protocol' targetptr='Syntactic_Conventions'>Section 2
of the core X Window System protocol</olink>
<xref linkend="References:x11proto"/>:
</para>
<itemizedlist>
  <listitem>
    <para>
<!-- .IP \(bu 5 -->
Data type names are spelled in upper case with no word separators,
as in:  <link linkend="Data_Types:FONTID"><type>FONTID</type></link>
    </para>
  </listitem>
  <listitem>
    <para>
<!-- .IP \(bu 5 -->
Alternate values are capitalized with no word separators,
as in:  <constant>MaxWidth</constant>
    </para>
  </listitem>
  <listitem>
    <para>
<!-- .IP \(bu 5 -->
Structure element declarations are in lower case with hyphens
as word separators, as in:  <structfield>byte-order-msb</structfield>
    </para>
    <note>
      <para>
Structure element names are referred to in
upper case (e.g. <structfield>BYTE-ORDER-MSB</structfield>) when used in
descriptions to set them off from the surrounding
text.  When this document is typeset they will be
printed in lower case in a distinct font.
      </para>
    </note>
  </listitem>
  <listitem>
    <para>
Type declarations have the form <quote><type>name</type>: type</quote>,
as in:  <type>CARD8</type>: 8-bit byte
    </para>
  </listitem>
  <listitem>
    <para>
Comma-separated lists of alternate values are enclosed in
braces, as in:  { <constant>Min</constant>, <constant>MaxWidth</constant>,
<constant>Max</constant> }
    </para>
  </listitem>
  <listitem>
    <para>
Comma-separated lists of structure elements are enclosed in
brackets, as in:  [ <structfield>byte1</structfield>: <type>CARD8</type>,
<structfield>byte2</structfield>: <type>CARD8</type> ]
    </para>
  </listitem>
</itemizedlist>

<para>
A type with a prefix <quote>LISTof</quote> represents a counted list of
elements of that type, as in:  <type>LISTofCARD8</type>
</para>

<section id='Data_Types'>
<title>Data Types</title>
<!-- .XS -->
<!-- (SN Data Types -->
<!-- .XE -->
<para>
The following data types are used in the core X Font Server protocol:
</para>

<section id="Data_Types:ACCESSCONTEXT">
  <title><type>ACCESSCONTEXT</type></title>
  <indexterm zone="Data_Types:ACCESSCONTEXT" significance="preferred"><primary>ACCESSCONTEXT</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry><type>ACCESSCONTEXT</type>:</entry><entry><link linkend="Data_Types:ID"><type>ID</type></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
<para>
This value is specified in the CreateAC request as the identifier
to be used when referring to a particular AccessContext resource
within the server.  These resources are used by the server to
store client-specified authorization information.  This
information may be used by the server to determine whether or not
the client should be granted access to particular font data.
</para>
<para>
In order to preserve the integrity of font licensing being performed by
the font server, care must be taken by a client to properly represent the
identity of the true user of the font.  Some font clients will in fact
be servers (for example, X servers) requesting fonts for their own clients.
Other font clients may be doing work on behalf of a number of different
users over time (for example, print spoolers).
</para>
<para>
<type>AccessContext</type>s
must be created (with
<link linkend="Requests:CreateAC"><function>CreateAC</function></link>)
and switched among (with
<link linkend="Requests:SetAuthorization"><function>SetAuthorization</function></link>)
to represent all of these <quote>font users</quote> properly.
    </para>
</section>

<section id="Data_Types:ALTERNATESERVER">
  <title><type>ALTERNATESERVER</type></title>
  <indexterm zone="Data_Types:ALTERNATESERVER" significance="preferred"><primary>ALTERNATESERVER</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry morerows="1"><type>ALTERNATESERVER</type>:</entry>
          <entry> [ <structfield>name</structfield>:</entry><entry>  <link linkend="Data_Types:STRING8"><type>STRING8</type></link>,</entry></row>
          <row><entry>&emsp;<structfield>subset</structfield>:</entry><entry>  <link linkend="Data_Types:BOOL"><type>BOOL</type></link> ]</entry></row>
        </tbody>
      </tgroup>
    </informaltable>

    <para>
This structure specifies the <structfield>NAME</structfield>,
encoded in <acronym>ISO</acronym> 8859-1 according
to <link linkend='Font_Server_Naming'>Section 3</link>,
of another font server that may be useful as a
substitute for this font server.
The <structfield>SUBSET</structfield> field indicates
whether or not the alternate server is likely to only contain a
subset of the fonts available from this font server.  This
information is returned during the initial connection setup and
may be used by the client to find a backup server in case of
failure.
    </para>
</section>

<section id="Data_Types:AUTH">
  <title><type>AUTH</type></title>
  <indexterm zone="Data_Types:AUTH" significance="preferred"><primary>AUTH</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry morerows="1"><type>AUTH</type>:</entry><entry>[ <structfield>name</structfield>:</entry><entry><link linkend="Data_Types:STRING8"><type>STRING8</type></link>,</entry></row>
          <row><entry>&emsp;<structfield>data</structfield>:</entry><entry><link linkend="Data_Types:BYTE"><type>LISTofBYTE</type></link> ]</entry></row>
        </tbody>
      </tgroup>
    </informaltable>

<para>
This structure specifies the name of an authorization protocol and
initial data for that protocol.  It is used in the authorization
negotiation in the initial connection setup and in the CreateAC
request.
</para>
</section>

<section id="Data_Types:BITMAPFORMAT">
  <title><type>BITMAPFORMAT</type></title>
  <indexterm zone="Data_Types:BITMAPFORMAT" significance="preferred"><primary>BITMAPFORMAT</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry><type>BITMAPFORMAT</type>:</entry><entry><type>CARD32</type></entry></row>
        </tbody>
      </tgroup>
    </informaltable>

    <para>
   <type>CARD32</type> containing the following fields defined by the
   sets of values given further below
    </para>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
        <row><entry>[</entry></row>
          <row><entry>&emsp;<structfield>byte-order-msb</structfield>:</entry><entry>      1 bit,</entry></row>
          <row><entry>&emsp;<structfield>bit-order-msb</structfield>:</entry><entry>       1 bit,</entry></row>
          <row><entry>&emsp;<structfield>image-rect</structfield>:</entry><entry>          2 bits</entry><entry>{ <constant>Min</constant>,
                          <constant>MaxWidth</constant>,
                          <constant>Max</constant> },</entry></row>
          <row><entry>&emsp;<structfield>zero-pad</structfield>:</entry><entry>            4 bits,</entry></row>
          <row><entry>&emsp;<structfield>scanline-pad</structfield>:</entry><entry>        2 bits</entry><entry>{ <constant>ScanlinePad8</constant>,
                          <constant>ScanlinePad16</constant>,
                          <constant>ScanlinePad32</constant>,
                          <constant>ScanlinePad64</constant> },</entry></row>
          <row><entry>&emsp;<structfield>zero-pad</structfield>:</entry><entry>            2 bits,</entry></row>
          <row><entry>&emsp;<structfield>scanline-unit</structfield>:</entry><entry>       2 bits</entry><entry>{ <constant>ScanlineUnit8</constant>,
                          <constant>ScanlineUnit16</constant>,
                          <constant>ScanlineUnit32</constant>,
                          <constant>ScanlineUnit64</constant> },</entry></row>
          <row><entry>&emsp;<structfield>zero-pad</structfield>:</entry><entry>            2 bits,</entry></row>
          <row><entry>&emsp;<structfield>zero-pad</structfield>:</entry><entry>            16 bits,</entry></row>
          <row><entry>]</entry></row>
        </tbody>
      </tgroup>
    </informaltable>

<para>
This structure specifies how glyph images are transmitted in
response to
<link linkend="Requests:QueryXBitmaps8"><function>QueryXBitmaps8</function></link>
and
<link linkend="Requests:QueryXBitmaps16"><function>QueryXBitmaps16</function></link>
requests.
</para>
<para>
If the <structfield>BYTE-ORDER-MSB</structfield> bit
<literal>(1 &lt;&lt; 0)</literal> is set, the Most Significant
Byte of each scanline unit is returned first.  Otherwise, the
Least Significant Byte is returned first.
</para>
<para>
If the <structfield>BIT-ORDER-MSB</structfield> bit
<literal>(1 &lt;&lt; 1)</literal> is set, the left-most bit in
each glyph scanline unit is stored in the Most Significant Bit of
each transmitted scanline unit.  Otherwise, the left-most bit is
stored in the Least Significant Bit.
</para>
<para>
The <structfield>IMAGE-RECT</structfield> field specifies a rectangle of
pixels within the
glyph image.  It contains one of the following alternate values:

    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><constant>ImageRectMin</constant></entry><entry><literal>(0 &lt;&lt; 2)</literal></entry></row>
          <row><entry><constant>ImageRectMaxWidth</constant></entry><entry><literal>(1 &lt;&lt; 2)</literal></entry></row>
          <row><entry><constant>ImageRectMax</constant></entry><entry><literal>(2 &lt;&lt; 2)</literal></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
</para>
<para>
For a glyph with extents <link linkend="Data_Types:XCHARINFO"><type>XCHARINFO</type></link> in a font with header
information <link linkend="Data_Types:XFONTINFO"><type>XFONTINFO</type></link>, the <structfield>IMAGE-RECT</structfield>
values have the following meanings:
<variablelist>
  <varlistentry id="Constant:ImageRectMin">
    <term><constant>ImageRectMin</constant></term>
    <listitem>
      <indexterm zone="Constant:ImageRectMin" significance="preferred"><primary>ImageRectMin</primary></indexterm>
      <para>
This refers to the minimal bounding rectangle
surrounding the inked pixels in the glyph.  This is the
most compact representation.  The edges of the rectangle
are:
<literallayout class="monospaced">
         left:     <structfield>XCHARINFO.LBEARING</structfield>
         right:    <structfield>XCHARINFO.RBEARING</structfield>
         top:      <structfield>XCHARINFO.ASCENT</structfield>
         bottom:   <structfield>XCHARINFO.DESCENT</structfield>
</literallayout>
      </para>
    </listitem>
  </varlistentry>
  <varlistentry id="Constant:ImageRectMaxWidth">
    <term><constant>ImageRectMaxWidth</constant></term>
    <listitem>
      <indexterm zone="Constant:ImageRectMaxWidth" significance="preferred"><primary>ImageRectMaxWidth</primary></indexterm>
      <para>
This refers to the scanlines between the
glyph's ascent and descent, padded on the left to the minimum
left-bearing (or 0, whichever is less) and on the right to
the maximum right-bearing (or logical-width, whichever is
greater).  All glyph images share a common horizontal
origin.  This is a combination of <constant>ImageRectMax</constant> in the
horizontal direction and <constant>ImageRectMin</constant> in the vertical
direction.  The edges of the rectangle are:

<literallayout class="monospaced">
left:         min (<structfield>XFONTINFO.MIN-BOUNDS.LBEARING</structfield>, 0)
right:        max (<structfield>XFONTINFO.MAX-BOUNDS.RBEARING</structfield>,
                   <structfield>XFONTINFO.MAX-BOUNDS.WIDTH</structfield>)
top:               <structfield>XCHARINFO.ASCENT</structfield>
bottom:            <structfield>XCHARINFO.DESCENT</structfield>
</literallayout>
      </para>
    </listitem>
  </varlistentry>
  <varlistentry id="Constant:ImageRectMax">
    <term><constant>ImageRectMax</constant></term>
    <listitem>
      <indexterm zone="Constant:ImageRectMax" significance="preferred"><primary>ImageRectMax</primary></indexterm>
      <para>
This refers to all scanlines, from the maximum
ascent (or the font ascent, whichever is greater) to the
maximum descent (or the font descent, whichever is greater),
padded to the same horizontal extents as <constant>MaxWidth</constant>.
All glyph images have the same sized bitmap and share a
common origin.  This is the least compact representation,
but may be the easiest or most efficient (particularly for
character cell fonts) for some clients to use.  The edges of
the rectangle are:

<literallayout class="monospaced">
left:         min (<structfield>XFONTINFO.MIN-BOUNDS.LBEARING</structfield>, 0)
right:        max (<structfield>XFONTINFO.MAX-BOUNDS.RBEARING</structfield>,
                   <structfield>XFONTINFO.MAX-BOUNDS.WIDTH</structfield>)
top:          max (<structfield>XFONTINFO.FONT-ASCENT</structfield>,
                   <structfield>XFONTINFO.MAX-BOUNDS.ASCENT</structfield>)
bottom:       max (<structfield>XFONTINFO.FONT-DESCENT</structfield>,
                   <structfield>XFONTINFO.MAX-BOUNDS.DESCENT</structfield>)
</literallayout>
      </para>
    </listitem>
  </varlistentry>
</variablelist>
</para>
<para>
The <structfield>SCANLINE-PAD</structfield> field specifies the number of
bits (8, 16, 32,
or 64) to which each glyph scanline is padded before transmitting.
It contains one of the following alternate values:
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><constant>ScanlinePad8</constant></entry><entry><literal>(0 &lt;&lt; 8)</literal></entry></row>
          <row><entry><constant>ScanlinePad16</constant></entry><entry><literal>(1 &lt;&lt; 8)</literal></entry></row>
          <row><entry><constant>ScanlinePad32</constant></entry><entry><literal>(2 &lt;&lt; 8)</literal></entry></row>
          <row><entry><constant>ScanlinePad64</constant></entry><entry><literal>(3 &lt;&lt; 8)</literal></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
</para>
<para>
The <structfield>SCANLINE-UNIT</structfield> field specifies the number of
bits (8, 16, 32, or 64) that should be treated as a unit for swapping.
This value must be less than or equal to the number of bits specified by the
<structfield>SCANLINE-PAD</structfield>.  It contains one of the following
alternate values:

    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><constant>ScanlineUnit8</constant></entry><entry><literal>(0 &lt;&lt; 12)</literal></entry></row>
          <row><entry><constant>ScanlineUnit16</constant></entry><entry><literal>(1 &lt;&lt; 12)</literal></entry></row>
          <row><entry><constant>ScanlineUnit32</constant></entry><entry><literal>(2 &lt;&lt; 12)</literal></entry></row>
          <row><entry><constant>ScanlineUnit64</constant></entry><entry><literal>(3 &lt;&lt; 12)</literal></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
</para>
<para>
<link linkend="Data_Types:BITMAPFORMAT"><type>BITMAPFORMAT</type></link>s are byte-swapped as <type>CARD32</type>s.
All unspecified bits must be zero.
</para>
<para>
Use of an invalid <link linkend="Data_Types:BITMAPFORMAT"><type>BITMAPFORMAT</type></link> causes a
<link linkend="Errors:Format"><errorname>Format</errorname></link> error to be returned.
</para>
</section>

<section id="Data_Types:BITMAPFORMATMASK">
  <title><type>BITMAPFORMATMASK</type></title>
  <indexterm zone="Data_Types:BITMAPFORMATMASK" significance="preferred"><primary>BITMAPFORMATMASK</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry><type>BITMAPFORMATMASK</type>:</entry><entry>     <type>CARD32</type> mask</entry></row>
        </tbody>
      </tgroup>
    </informaltable>
<para>
This is a mask of bits representing the fields in a <link linkend="Data_Types:BITMAPFORMAT"><type>BITMAPFORMAT</type></link>:
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><constant>ByteOrderMask</constant></entry><entry><literal>(1 &lt;&lt; 0)</literal></entry></row>
          <row><entry><constant>BitOrderMask</constant></entry><entry><literal>(1 &lt;&lt; 1)</literal></entry></row>
          <row><entry><constant>ImageRectMask</constant></entry><entry><literal>(1 &lt;&lt; 2)</literal></entry></row>
          <row><entry><constant>ScanlinePadMask</constant></entry><entry><literal>(1 &lt;&lt; 3)</literal></entry></row>
          <row><entry><constant>ScanlineUnitMask</constant></entry><entry><literal>(1 &lt;&lt; 4)</literal></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
</para>
<para>
Unspecified bits are required to be zero or else a
<link linkend="Errors:Format"><errorname>Format</errorname></link> error is returned.
</para>
</section>

<section id="Data_Types:BOOL">
  <title><type>BOOL</type></title>
  <indexterm zone="Data_Types:BOOL" significance="preferred"><primary>BOOL</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry><type>BOOL</type>:</entry><entry>  <type>CARD8</type></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
<para>
This is a boolean value containing one of the following alternate
values:
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><constant>False</constant></entry><entry><literal>0</literal></entry></row>
          <row><entry><constant>True</constant></entry><entry><literal>1</literal></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
</para>
</section>

<section id="Data_Types:BYTE">
  <title><type>BYTE</type></title>
  <indexterm zone="Data_Types:BYTE" significance="preferred"><primary>BYTE</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry><type>BYTE</type>:</entry><entry>  8-bit value</entry></row>
        </tbody>
      </tgroup>
    </informaltable>

<para>
This is an unsigned byte of data whose encoding
is determined by the context in which it is used.
</para>

</section>

<section id="Data_Types:CARDn">
  <title><type>CARD8</type>, <type>CARD16</type>, <type>CARD32</type></title>
  <indexterm zone="Data_Types:CARDn" significance="preferred"><primary>CARD8</primary></indexterm>
  <indexterm zone="Data_Types:CARDn" significance="preferred"><primary>CARD16</primary></indexterm>
  <indexterm zone="Data_Types:CARDn" significance="preferred"><primary>CARD32</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry><type>CARD8</type>:</entry><entry>  8-bit unsigned integer</entry></row>
          <row><entry><type>CARD16</type>:</entry><entry>  16-bit unsigned integer</entry></row>
          <row><entry><type>CARD32</type>:</entry><entry>  32-bit unsigned integer</entry></row>
        </tbody>
      </tgroup>
    </informaltable>

<para>
These are unsigned numbers.  The latter two are byte-swapped when
the server and client have different byte orders.
</para>

</section>

<section id="Data_Types:CHAR2B">
  <title><type>CHAR2B</type></title>
  <indexterm zone="Data_Types:CHAR2B" significance="preferred"><primary>CHAR2B</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry><type>CHAR2B</type>:</entry><entry>[ <structfield>byte1</structfield>, <structfield>byte2</structfield>:</entry><entry><type>CARD8</type> ]</entry></row>
        </tbody>
      </tgroup>
    </informaltable>
<para>
This structure specifies an individual character code within
either a 2-dimensional matrix (using <structfield>BYTE1</structfield>
and <structfield>BYTE2</structfield> as the row and column indices,
respectively) or a vector (using <structfield>BYTE1</structfield> and
<structfield>BYTE2</structfield> as most- and least-significant bytes,
respectively).  This data type is treated as a pair of 8-bit values and
is never byte-swapped.  Therefore, the client should always transmit
<structfield>BYTE1</structfield> first.
</para>

</section>

<section id="Data_Types:EVENTMASK">
  <title><type>EVENTMASK</type></title>
  <indexterm zone="Data_Types:EVENTMASK" significance="preferred"><primary>EVENTMASK</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry><type>EVENTMASK</type>:</entry><entry>  <type>CARD32</type> mask</entry></row>
        </tbody>
      </tgroup>
    </informaltable>

<para>
This is a mask of bits indicating which of an extension's (or the
core's) maskable events the client would like to receive.  Each
bit indicates one or more events, and a bit value of one indicates
interest in a corresponding set of events.  The following bits are
defined for event masks specified for the core protocol (i.e. an
<parameter>EXTENSION-OPCODE</parameter> of zero in
<link linkend="Requests:SetEventMask"><function>SetEventMask</function></link>
and
<link linkend="Requests:GetEventMask"><function>GetEventMask</function></link>
requests):

    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><constant>CatalogueListChangeMask</constant></entry><entry><literal>(1 &lt;&lt; 0)</literal></entry></row>
          <row><entry><constant>FontListChangeMask</constant></entry><entry><literal>(1 &lt;&lt; 1)</literal></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
</para>

<para>
If
<constant>CatalogueListChangeMask</constant>
is set, client is interested in
receiving
<link linkend="Events:CatalogueListNotify"><function>CatalogueListNotify</function></link>
events.  If
<constant>FontListChangeMask</constant>
is set, the client is interested in
receiving
<link linkend="Events:FontListNotify"><function>FontListNotify</function></link>
events.
</para>
<para>
Extensions that provide additional events may define their own
event masks.  These event masks have their own scope and may use
the same bit values as the core or other extensions.
    </para>
    <para>
All unused bits must be set to zero.  In
<link linkend="Requests:SetEventMask"><function>SetEventMask</function></link>
requests, if
any bits are set that are not defined for the extension (or core)
for which this <type>EVENTMASK</type> is intended (according to the
<parameter>EXTENSION-OPCODE</parameter> given in the
<link linkend="Requests:SetEventMask"><function>SetEventMask</function></link>
request), an
<link linkend="Errors:EventMask"><errorname>EventMask</errorname></link>
error is generated.
    </para>
    <para>
This value is swapped as a <type>CARD32</type>.
    </para>

</section>

<section id="Data_Types:FONTID">
  <title><type>FONTID</type></title>
  <indexterm zone="Data_Types:FONTID" significance="preferred"><primary>FONTID</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry><type>FONTID</type>:</entry><entry>     <link linkend="Data_Types:ID"><type>ID</type></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>

<para>
This is specified by the client in the request
<link linkend="Requests:OpenBitmapFont"><function>OpenBitmapFont</function></link>
as the identifier to be used when referring to a particular open
font.
</para>

</section>

<section id="Data_Types:ID">
  <title><type>ID</type></title>
  <indexterm zone="Data_Types:ID" significance="preferred"><primary>ID</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry><type>ID</type>:</entry><entry>  <type>CARD32</type></entry></row>
        </tbody>
      </tgroup>
    </informaltable>

<para>
This is a 32-bit value in which the top 3 bits must be clear, and
at least 1 other bit must be set (yielding a range of 1 through
2<superscript>29</superscript>-1).
It is specified by the client to represent objects in
the server.  Identifiers are scoped according to their type are
private to the client; thus, the same identifier may be used for
both a <link linkend="Data_Types:FONTID"><type>FONTID</type></link> and an <link linkend="Data_Types:ACCESSCONTEXT"><type>ACCESSCONTEXT</type></link>
as well as by multiple clients.
</para>
<para>
An <type>ID</type> of zero is referred to as <constant>None</constant>.
</para>
</section>

<section id="Data_Types:INTn">
  <title><type>INT8</type>, <type>INT16</type>, <type>INT32</type></title>
  <indexterm zone="Data_Types:INTn" significance="preferred"><primary>INT8</primary></indexterm>
  <indexterm zone="Data_Types:INTn" significance="preferred"><primary>INT16</primary></indexterm>
  <indexterm zone="Data_Types:INTn" significance="preferred"><primary>INT32</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry><type>INT8</type>:</entry><entry>  8-bit signed integer</entry></row>
          <row><entry><type>INT16</type>:</entry><entry>  16-bit signed integer</entry></row>
          <row><entry><type>INT32</type>:</entry><entry>  32-bit signed integer</entry></row>
        </tbody>
      </tgroup>
    </informaltable>

<para>
These are signed numbers.  The latter two are byte-swapped when
the client and server have different byte orders.
</para>
</section>

<section id="Data_Types:OFFSET32">
  <title><type>OFFSET32</type></title>
  <indexterm zone="Data_Types:OFFSET32" significance="preferred"><primary>OFFSET32</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry morerows='1'><type>OFFSET32</type>:</entry>
          <entry>[ <structfield>position</structfield>:</entry><entry><type>CARD32</type>,</entry></row>
          <row><entry>&emsp;<structfield>length</structfield>:</entry><entry><type>CARD32</type> ]</entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This structure indicates a position and length within a block of
data.
    </para>
</section>

<section id="Data_Types:PROPINFO">
  <title><type>PROPINFO</type></title>
  <indexterm zone="Data_Types:PROPINFO" significance="preferred"><primary>PROPINFO</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry morerows='1'><type>PROPINFO</type>:</entry>
          <entry>[ <structfield>offsets</structfield>:</entry><entry><link linkend="Data_Types:PROPOFFSET"><type>LISTofPROPOFFSET</type></link>,</entry></row>
          <row><entry>&emsp;<structfield>data</structfield>:</entry><entry><link linkend="Data_Types:BYTE"><type>LISTofBYTE</type></link> ]</entry></row>
        </tbody>
      </tgroup>
    </informaltable>

    <para>
This structure describes the list of properties provided by a
font.  Strings for all of the properties names and values are
stored within the data block and are located using a table of
offsets and lengths.
    </para>
    <para>
This structure is padded to 32-bit alignment.
    </para>

</section>

<section id="Data_Types:PROPOFFSET">
  <title><type>PROPOFFSET</type></title>
  <indexterm zone="Data_Types:PROPOFFSET" significance="preferred"><primary>PROPOFFSET</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry morerows='3'><type>PROPOFFSET</type>:</entry>
          <entry>[ <structfield>name</structfield>:</entry><entry><link linkend="Data_Types:OFFSET32"><type>OFFSET32</type></link>,</entry></row>
          <row><entry>&emsp;<structfield>value</structfield>:</entry><entry><link linkend="Data_Types:OFFSET32"><type>OFFSET32</type></link>,</entry></row>
          <row><entry>&emsp;<structfield>type</structfield>:</entry><entry><type>CARD8</type>,</entry></row>
          <row><entry>&emsp;<structfield>zero-pad3</structfield>:</entry><entry><link linkend="Data_Types:BYTE"><type>BYTE</type></link>, <link linkend="Data_Types:BYTE"><type>BYTE</type></link>, <link linkend="Data_Types:BYTE"><type>BYTE</type></link> ]</entry></row>
        </tbody>
      </tgroup>
    </informaltable>

    <para>
This structure specifies the position, length, and type of
of data for a property.
    </para>
    <para>
The <structfield>NAME</structfield> field specifies the position and length
(which must be
greater than zero) of the property name relative to the beginning
of the <structfield>PROPINFO.DATA</structfield> block for this font.
The interpretation of
the position and length of the <structfield>VALUE</structfield> field is
determined by the <structfield>TYPE</structfield> field, which contains
one of the following alternate values:

     <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><constant>String</constant></entry><entry><literal>0</literal></entry></row>
          <row><entry><constant>Unsigned</constant></entry><entry><literal>1</literal></entry></row>
          <row><entry><constant>Signed</constant></entry><entry><literal>2</literal></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
which have the following meanings:
<variablelist>
  <varlistentry id="Constant:String">
    <term><constant>String</constant></term>
    <listitem>
      <indexterm zone="Constant:String" significance="preferred"><primary>String</primary></indexterm>
      <para>
This property contains a counted string of bytes.  The
data is stored in the <structfield>PROPINFO.DATA</structfield>
block beginning at
relative byte VALUE.POSITION (beginning with zero), extending
for VALUE.LENGTH (at least zero) bytes.
      </para>
    </listitem>
  </varlistentry>
  <varlistentry id="Constant:Unsigned">
    <term><constant>Unsigned</constant></term>
    <listitem>
      <indexterm zone="Constant:Unsigned" significance="preferred"><primary>Unsigned</primary></indexterm>
    <para>
This property contains a unsigned, 32-bit number stored
as a <type>CARD32</type> in VALUE.POSITION (VALUE.LENGTH is zero).
    </para>
    </listitem>
  </varlistentry>
  <varlistentry id="Constant:Signed">
    <term><constant>Signed</constant></term>
    <listitem>
      <indexterm zone="Constant:Signed" significance="preferred"><primary>Signed</primary></indexterm>
      <para>
This property contains a signed, 32-bit number stored as
an <type>INT32</type> in VALUE.POSITION (VALUE.LENGTH is zero).
      </para>
    </listitem>
  </varlistentry>
</variablelist>
This structure is zero-padded to 32-bit alignment.
</para>

</section>

<section id="Data_Types:RANGE">
  <title><type>RANGE</type></title>
  <indexterm zone="Data_Types:RANGE" significance="preferred"><primary>RANGE</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry><type>RANGE</type>:</entry>
          <entry>[ <structfield>min-char</structfield>, <structfield>max-char</structfield>:</entry><entry><link linkend="Data_Types:CHAR2B"><type>CHAR2B</type></link> ]</entry></row>
        </tbody>
      </tgroup>
    </informaltable>

  <para>
This structure specifies a range of character codes.  A single
character is represented by <structfield>MIN-CHAR</structfield> equals
<structfield>MAX-CHAR</structfield>.  If the linear interpretation of
<structfield>MAX-CHAR</structfield> is less than that of
<structfield>MIN-CHAR</structfield>, or if
<structfield>MIN-CHAR</structfield> is less than the font's
<structfield>XFONTINFO.CHAR-RANGE.MIN-CHAR</structfield>, or if
<structfield>MAX-CHAR</structfield> is greater than the
font's <structfield>XFONTINFO.CHAR-RANGE.MAX-CHAR</structfield>,
the range is invalid.
  </para>

</section>

<section id="Data_Types:RESOLUTION">
  <title><type>RESOLUTION</type></title>
  <indexterm zone="Data_Types:RESOLUTION" significance="preferred"><primary>RESOLUTION</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry morerows='2'><type>RESOLUTION</type>:</entry>
          <entry>[ <structfield>x-resolution</structfield>:</entry><entry><type>CARD16</type>,</entry></row>
          <row><entry>&emsp;<structfield>y-resolution</structfield>:</entry><entry><type>CARD16</type>,</entry></row>
          <row><entry>&emsp;<structfield>decipoint-size</structfield>:</entry><entry><type>CARD16</type> ]</entry></row>
        </tbody>
      </tgroup>
    </informaltable>

  <para>
This structure specifies resolution and point size to be used in
resolving partially-specified scaled font names.  The
<structfield>X-RESOLUTION</structfield> and
<structfield>Y-RESOLUTION</structfield> are measured in
pixels-per-inch and must be greater than zero.
The <structfield>DECIPOINT-SIZE</structfield> is the preferred font
size, measured in tenths of a point, and must be greater than zero.
  </para>

</section>

<section id="Data_Types:STRING8">
  <title><type>STRING8</type></title>
  <indexterm zone="Data_Types:STRING8" significance="preferred"><primary>STRING8</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry><type>STRING8</type>:</entry><entry>          <type>LISTofCARD8</type></entry></row>
        </tbody>
      </tgroup>
    </informaltable>

  <para>
This is a counted list of 1-byte character codes, typically
encoded in <acronym>ISO</acronym> 8859-1.  A character code
<quote><literal>c</literal></quote> is equivalent to a
<link linkend="Data_Types:CHAR2B"><type>CHAR2B</type></link> structure whose <structfield>BYTE1</structfield>
is zero and whose <structfield>BYTE2</structfield> is
<quote><literal>c</literal></quote>.
  </para>

</section>

<section id="Data_Types:TIMESTAMP">
  <title><type>TIMESTAMP</type></title>
  <indexterm zone="Data_Types:TIMESTAMP" significance="preferred"><primary>TIMESTAMP</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry><type>TIMESTAMP</type>:</entry><entry>     <type>CARD32</type></entry></row>
        </tbody>
      </tgroup>
    </informaltable>

  <para>
This is the number of milliseconds that have passed since a
server-dependent origin.  It is provided in errors and events and is
permitted to wrap.
  </para>
</section>

<section id="Data_Types:XCHARINFO">
  <title><type>XCHARINFO</type></title>
  <indexterm zone="Data_Types:XCHARINFO" significance="preferred"><primary>XCHARINFO</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry morerows='3'><type>XCHARINFO</type>:</entry>
          <entry>[ <structfield>lbearing</structfield>, <structfield>rbearing</structfield>:</entry><entry><type>INT16</type>,</entry></row>
          <row><entry>&emsp;<structfield>width</structfield>:</entry><entry><type>INT16</type>,</entry></row>
          <row><entry>&emsp;<structfield>ascent</structfield>, <structfield>descent</structfield>:</entry><entry><type>INT16</type>,</entry></row>
          <row><entry>&emsp;<structfield>attributes</structfield>:</entry><entry><type>CARD16</type> ]</entry></row>
        </tbody>
      </tgroup>
    </informaltable>

  <para>
This structure specifies the ink extents and horizontal escapement
(also known as the set- or logical width) of an individual
character.  The first five values represent directed distances in
a coordinate system whose origin is aligned with the lower-left
edge of the left-most pixel of the glyph baseline (i.e. the
baseline falls between two pixels as shown in Figure 3-1 of the
<citetitle>Bitmap Distribution Format 2.1</citetitle> Consortium standard
<xref linkend="References:bdf-spec"/>).
  </para>
  <para>
The <structfield>LBEARING</structfield> field specifies the
directed distance measured to the
right from the origin to the left edge of the left-most inked
pixel in the glyph.
  </para>
  <para>
The <structfield>RBEARING</structfield> field specifies the
directed distance (measured to
the right) from the origin to the right edge of the right-most
inked pixel in the glyph.
  </para>
  <para>
The <structfield>WIDTH</structfield> field specifies the
directed distance (measured to the
right) from the origin to the position where the next character
should appear (called the <firstterm>escapement point</firstterm>). This
distance includes any whitespace used for intercharacter padding and is
also referred to as the <firstterm>logical width</firstterm> or
<firstterm>horizontal escapement</firstterm>.
<indexterm zone="Data_Types:XCHARINFO" significance="preferred"><primary>horizontal escapement</primary></indexterm>
  </para>
  <para>
The <structfield>ASCENT</structfield> field specifies the
directed distance (measured up)
from the baseline to the top edge of the top-most inked pixel
in the glyph.
  </para>
  <para>
The <structfield>DESCENT</structfield> field specifies the
directed distance (measured
down) from the baseline to the bottom edge of the bottom-most
inked pixel.
  </para>
  <para>
The <structfield>ATTRIBUTES</structfield> field specifies
glyph-specific information that
is passed through the application.  If this value is not being
used, it should be zero.
  </para>
  <para>
The ink bounding box of a glyph is defined to be the smallest
rectangle that encloses all of the inked pixels.  This box has
a width of
<structfield>RBEARING</structfield> &minus; <structfield>LBEARING</structfield>
pixels and a height of
<structfield>ASCENT</structfield> + <structfield>DESCENT</structfield> pixels.
  </para>
</section>

<section id="Data_Types:XFONTINFO">
  <title><type>XFONTINFO</type></title>
  <indexterm zone="Data_Types:XFONTINFO" significance="preferred"><primary>XFONTINFO</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry morerows='8'><type>XFONTINFO</type>:</entry>
          <entry>[ <structfield>flags</structfield>:</entry><entry><type>CARD32</type>,</entry></row>
          <row><entry>&emsp;<structfield>drawing-direction</structfield>:</entry><entry>{ <constant>LeftToRight</constant>, <constant>RightToLeft</constant> }</entry></row>
          <row><entry>&emsp;<structfield>char-range</structfield>:</entry><entry><link linkend="Data_Types:RANGE"><type>RANGE</type></link>,</entry></row>
          <row><entry>&emsp;<structfield>default-char</structfield>:</entry><entry><link linkend="Data_Types:CHAR2B"><type>CHAR2B</type></link>,</entry></row>
          <row><entry>&emsp;<structfield>min-bounds</structfield>:</entry><entry><link linkend="Data_Types:XCHARINFO"><type>XCHARINFO</type></link>,</entry></row>
          <row><entry>&emsp;<structfield>max-bounds</structfield>:</entry><entry><link linkend="Data_Types:XCHARINFO"><type>XCHARINFO</type></link>,</entry></row>
          <row><entry>&emsp;<structfield>font-ascent</structfield>:</entry><entry><type>INT16</type>,</entry></row>
          <row><entry>&emsp;<structfield>font-descent</structfield>:</entry><entry><type>INT16</type>,</entry></row>
          <row><entry>&emsp;<structfield>properties</structfield>:</entry><entry><link linkend="Data_Types:PROPINFO"><type>PROPINFO</type></link> ]</entry></row>
        </tbody>
      </tgroup>
    </informaltable>
  <para>
This structure specifies attributes related to the font as a
whole.
  </para>
  <para>
The <structfield>FLAGS</structfield> field is a bit mask containing zero
or more of the following boolean values (unspecified bits must be zero):

    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><constant>AllCharactersExist</constant></entry><entry><literal>(1 &lt;&lt; 0)</literal></entry></row>
          <row><entry><constant>InkInside</constant></entry><entry><literal>(1 &lt;&lt; 1)</literal></entry></row>
          <row><entry><constant>HorizontalOverlap</constant></entry><entry><literal>(1 &lt;&lt; 2)</literal></entry></row>
        </tbody>
      </tgroup>
    </informaltable>

which have the following meanings:
<variablelist>
  <varlistentry id="Constant:AllCharactersExist">
    <term><constant>AllCharactersExist</constant></term>
    <listitem>
      <indexterm zone="Constant:AllCharactersExist" significance="preferred"><primary>AllCharactersExist</primary></indexterm>
    <para>
If this bit is set, all of the characters in the range given by
<structfield>CHAR-RANGE</structfield> have glyphs encoded in
the font.  If this bit is clear, some of the characters
may not have encoded glyphs.
    </para>
    </listitem>
  </varlistentry>
  <varlistentry id="Constant:InkInside">
    <term><constant>InkInside</constant></term>
    <listitem>
      <indexterm zone="Constant:InkInside" significance="preferred"><primary>InkInside</primary></indexterm>
    <para>
If this bit is set, the inked pixels of each glyph
fall within the rectangle described by the font's ascent,
descent, origin, and the glyph's escapement point.  If
this bit is clear, there may be glyphs whose ink extends
outside this rectangle.
    </para>
    </listitem>
  </varlistentry>
  <varlistentry id="Constant:HorizontalOverlap">
    <term><constant>HorizontalOverlap</constant></term>
    <listitem>
      <indexterm zone="Constant:HorizontalOverlap" significance="preferred"><primary>HorizontalOverlap</primary></indexterm>
    <para>
If this bit is set, the two ink bounding
boxes (smallest rectangle enclosing the inked pixels) of
some pairs of glyphs in the font may overlap when displayed
side-by-side (i.e. the second character is imaged at the
escapement point of the first) on a common baseline.  If
this bit is clear, there are no pairs of glyphs whose ink
bounding boxes overlap.
    </para>
    </listitem>
  </varlistentry>
</variablelist>
</para>
<para id="Data_Types:XFONTINFO.DRAWING-DIRECTION">
  <indexterm zone="Data_Types:XFONTINFO.DRAWING-DIRECTION" significance="preferred"><primary>LeftToRight</primary></indexterm>
  <indexterm zone="Data_Types:XFONTINFO.DRAWING-DIRECTION" significance="preferred"><primary>RightToLeft</primary></indexterm>
The <structfield>DRAWING-DIRECTION</structfield> field contains a hint
indicating whether most of the character metrics have a positive (or
<quote><constant>LeftToRight</constant></quote>) logical width or a
negative (<quote><constant>RightToLeft</constant></quote>) logical width.  It
contains the following alternate values:
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><constant>LeftToRight</constant></entry><entry><literal>0</literal></entry></row>
          <row><entry><constant>RightToLeft</constant></entry><entry><literal>1</literal></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
</para>
<para>
The <structfield>CHAR-RANGE.MIN-CHAR</structfield>
and <structfield>CHAR-RANGE.MAX-CHAR</structfield> fields specify the
first and last character codes that have glyphs encoded in this font.
All fonts must have at least one encoded glyph (in which case the
<structfield>MIN-CHAR</structfield> and <structfield>MAX-CHAR</structfield>
are equal), but are not required to have glyphs
encoded at all positions between the first and last characters.
</para>
<para>
The <structfield>DEFAULT-CHAR</structfield> field specifies
the character code of the glyph
that the client should substitute for unencoded characters.  Requests
for extents or bitmaps for an unencoded character generate zero-filled
metrics and a zero-length glyph bitmap, respectively.
</para>
<para>
The <structfield>MIN-BOUNDS</structfield> and
<structfield>MAX-BOUNDS</structfield> fields contain the minimum and maximum
values of each of the extents field of all encoded characters in the
font (i.e. non-existent characters are ignored).
</para>
<para>
The <structfield>FONT-ASCENT</structfield> and
<structfield>FONT-DESCENT</structfield> fields specify the font designer's
logical height of the font, above and below the baseline,
respectively.  The sum of the two values is often used as the
vertical line spacing of the font.  Individual glyphs are permitted
to have ascents and descents that are greater than these values.
</para>
<para>
The <structfield>PROPERTIES</structfield> field contains the
property data associated with this font.
</para>
<para>
This structure is padded to 32-bit alignment.
</para>
</section>
</section>

<section id='Requests'>
<title>Requests</title>
<!-- .XS -->
<!-- (SN Requests -->
<!-- .XE -->
<para>
This section describes the requests that may be sent by the client and the
replies or errors that are generated in response.  Versions of the protocol
with the same major version are required to be upward-compatible.
</para>
<para>
Every request on a given connection is implicitly assigned a sequence number,
starting with 1, that is used in replies, error, and events.  Servers are
required to generate replies and errors in the order in which the corresponding
requests are received.  Servers are permitted to add or remove fonts to the
list visible to the client between any two requests, but requests must be
processed atomically.  Each request packet is at least 4 bytes long and
contains the following fields:
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>major-opcode</parameter>:</entry><entry><type>CARD8</type></entry></row>
          <row><entry><parameter>minor-opcode</parameter>:</entry><entry><type>CARD8</type></entry></row>
          <row><entry><parameter>length</parameter>:</entry><entry><type>CARD16</type></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
</para>
<para>

The <parameter>MAJOR-OPCODE</parameter> specifies which core request or
extension package this packet represents.  If the
<parameter>MAJOR-OPCODE</parameter> corresponds to a core request, the
<parameter>MINOR-OPCODE</parameter> contains 8 bits of request-specific data.
Otherwise, the <parameter>MINOR-OPCODE</parameter> specifies which extension
request this packet represents.  The <parameter>LENGTH</parameter> field
specifies the number of 4-byte units contained within the packet
and must be at least one.  If this field contains a value greater than one it
is followed by (<parameter>LENGTH</parameter> - 1) * 4 bytes
of request-specific data.  Unless
otherwise specified, unused bytes are not required to be zero.
</para>
<para>
If a request packet contains too little or too much data, the server returns
a <link linkend="Errors:Length"><errorname>Length</errorname></link> error.
If the server runs out of internal
resources (such as memory) while processing a request, it returns an
<link linkend="Errors:Alloc"><errorname>Alloc</errorname></link> error.
If a server is deficient (and therefore non-compliant) and is unable to
process a request, it may return an
<link linkend="Errors:Implementation"><errorname>Implementation</errorname></link> error.
If a client uses an extension request without previously having issued a
<link linkend="Requests:QueryExtension"><function>QueryExtension</function></link>
request for that extension, the server responds with a
<link linkend="Errors:Request"><errorname>Request</errorname></link>
error.  If the server encounters a request with an unknown
<parameter>MAJOR-OPCODE</parameter> or <parameter>MINOR-OPCODE</parameter>,
it responds with a
<link linkend="Errors:Request"><errorname>Request</errorname></link>
error.
At most one error is generated per request.  If more than one error condition
is encountered in processing a requests, the choice of which error is returned
is server-dependent.
</para>
<para>
Core requests have <parameter>MAJOR-OPCODE</parameter> values between 0 and
127, inclusive.  Extension requests have <parameter>MAJOR-OPCODE</parameter>
values between 128 and 255, inclusive, that are assigned by by the server.
All <parameter>MINOR-OPCODE</parameter> values in extension requests are
between 0 and 255, inclusive.
</para>
<para>
Each reply is at least 8 bytes long and contains the following fields:
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>type</parameter>:</entry><entry><type>CARD8</type></entry><entry>value of 0</entry></row>
          <row><entry><parameter>data-or-unused</parameter>:</entry><entry><type>CARD8</type></entry></row>
          <row><entry><parameter>sequence-number</parameter>:</entry><entry><type>CARD16</type></entry></row>
          <row><entry><parameter>length</parameter>:</entry><entry><type>CARD32</type></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
</para>
<para>
The <parameter>TYPE</parameter> field has a value of zero.
The <parameter>DATA-OR-UNUSED</parameter> field may be used to
encode one byte of reply-specific data (see
<link linkend="Encoding::Requests">Section 5.2 on request encoding</link>).
The least-significant 16 bits of the sequence number of the request that
generated the reply are stored in the <parameter>SEQUENCE-NUMBER</parameter>
field.  The <parameter>LENGTH</parameter> field specifies the number of
4-byte units in this reply packet, including the fields described above,
and must be at least two.  If <parameter>LENGTH</parameter> is greater
than two, the fields described above are followed by
(<parameter>LENGTH</parameter> - 2) * 4 bytes of additional data.
</para>
<para>
Requests that have replies are described using the following syntax:
  <blockquote><para>
    <emphasis role="bold"><function>RequestName</function></emphasis>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>arg1</parameter>:</entry><entry><type>type1</type></entry></row>
          <row><entry><parameter>arg2</parameter>:</entry><entry><type>type2</type></entry></row>
          <row><entry> ...</entry></row>
          <row><entry><parameter>argN</parameter>:</entry><entry><type>typeN</type></entry></row>
          <row><entry> ▶</entry></row>
          <row><entry><parameter>result1</parameter>:</entry><entry><type>type1</type></entry></row>
          <row><entry><parameter>result2</parameter>:</entry><entry><type>type2</type></entry></row>
          <row><entry> ...</entry></row>
          <row rowsep="1"><entry><parameter>resultM</parameter>:</entry><entry><type>typeM</type></entry></row>
          <row><entry>Errors:</entry><entry><errorname>kind1</errorname>, <errorname>kind2</errorname> ..., <errorname>kindK</errorname></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    Description
  </para></blockquote>
</para>
<para>
If a request does not generate a reply, the ▶ and result lines are
omitted.  If a request may generate multiple replies, the ▶ is replaced by
a ▶+.  In the authorization data exchanges in the initial connection setup
and the CreateAC request, ◀ indicates data sent by the client in response
to data sent by the server.
</para>
<para>
The protocol begins with the establishment of a connection over a
mutually-understood virtual stream:
</para>

<section id="Requests:open_connection">
    <title>open connection</title>
    <indexterm zone="Requests:open_connection" significance="preferred"><primary>open connection</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='2.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>byte-order</parameter>:</entry><entry><link linkend="Data_Types:BYTE"><type>BYTE</type></link></entry></row>
          <row><entry><parameter>client-major-protocol-version</parameter>:</entry><entry><type>CARD16</type></entry></row>
          <row><entry><parameter>client-minor-protocol-version</parameter>:</entry><entry><type>CARD16</type></entry></row>
          <row><entry><parameter>authorization-protocols</parameter>:</entry><entry><link linkend="Data_Types:AUTH"><type>LISTofAUTH</type></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
<para>
The initial byte of the connection specifies the
<parameter>BYTE-ORDER</parameter> in
which subsequent 16-bit and 32-bit numeric values are to be
transmitted.  The octal value <literal>102</literal>
(<acronym>ASCII</acronym> uppercase <quote><literal>B</literal></quote>)
indicates that the most-significant byte is to be transmitted
first; the octal value <literal>154</literal>
(<acronym>ASCII</acronym> lowercase <quote><literal>l</literal></quote>)
indicates that the least-significant byte is to be transmitted first.
If any other value is encountered the server closes the
connection without any response.
</para>
  <para>
The <parameter>CLIENT-MAJOR-PROTOCOL-VERSION</parameter> and
<parameter>CLIENT-MINOR-PROTOCOL-VERSION</parameter> specify
which version of the
font service protocol the client would like to use.  If the
client can support multiple versions, the highest version
should be given.  This version of the protocol has a
major version of 2 and a minor version of 0.
  </para>
  <para>
The <parameter>AUTHORIZATION-PROTOCOLS</parameter>
contains a list of protocol names and
optional initial data for which the client can provide
information.  The server may use this to determine which
protocol to use or as part of the initial exchange of
authorization data.
  </para>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='2.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry>▶</entry></row>
          <row><entry><parameter>status</parameter>:</entry><entry>{ <constant>Success</constant>, <constant>Continue</constant>,
                                  <constant>Busy</constant>, <constant>Denied</constant> }</entry></row>
          <row><entry><parameter>server-major-protocol-version</parameter>:</entry><entry><type>CARD16</type></entry></row>
          <row><entry><parameter>server-minor-protocol-version</parameter>:</entry><entry><type>CARD16</type></entry></row>
          <row><entry><parameter>alternate-servers-hint</parameter>:</entry><entry><link linkend="Data_Types:ALTERNATESERVER"><type>LISTofALTERNATESERVER</type></link></entry></row>
          <row><entry><parameter>authorization-index</parameter>:</entry><entry><type>CARD8</type></entry></row>
          <row><entry><parameter>authorization-data</parameter>:</entry><entry><link linkend="Data_Types:BYTE"><type>LISTofBYTE</type></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
  <para>
The <parameter>SERVER-MAJOR-PROTOCOL-VERSION</parameter> and
<parameter>SERVER-MINOR-PROTOCOL-VERSION</parameter> specify
the version of the font
service protocol that the server expects from the client.  If
the server supports the version specified by the client, this
version number should be returned.  If the client has
requested a higher version than is supported by the server,
the server's highest version should be returned.  Otherwise,
if the client has requested a lower version than is supported
by the server, the server's lowest version should be returned.
It is the client's responsibility to decide whether or not it
can match this version of the protocol.
  </para>
  <para>
The <parameter>ALTERNATE-SERVERS-HINT</parameter>
is a list of other font servers
that may have related sets of fonts (determined by means
outside this protocol, typically by the system administrator).
Clients may choose to contact these font servers if the
connection is rejected or lost.
  </para>
  <para>
The <parameter>STATUS</parameter> field indicates whether the server accepted,
rejected, or would like more information about the connection.
It has one of the following alternate values:
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='2.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><constant>Success</constant></entry><entry><literal>0</literal></entry></row>
          <row><entry><constant>Continue</constant></entry><entry><literal>1</literal></entry></row>
          <row><entry><constant>Busy</constant></entry><entry><literal>2</literal></entry></row>
          <row><entry><constant>Denied</constant></entry><entry><literal>3</literal></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
  </para>
  <para>
If <parameter>STATUS</parameter> is <constant>Denied</constant>,
the server has rejected the client's authorization information.
If <parameter>STATUS</parameter> is <constant>Busy</constant>, the server has
simply decided that it cannot provide fonts to this client at
this time (it may be able to at a later time).  In both cases,
<parameter>AUTHORIZATION-INDEX</parameter> is set to zero,
no authorization-data is
returned, and the server closes the connection after sending
the data described so far.
  </para>
  <para>
Otherwise the <parameter>AUTHORIZATION-INDEX</parameter> is set to the index
(beginning with 1) into the <parameter>AUTHORIZATION-PROTOCOLS</parameter>
list of the protocol that the server will use for this connection.  If
the server does not want to use any of the given protocols,
this value is set to zero.  The <parameter>AUTHORIZATION-DATA</parameter>
field is used to send back authorization protocol-dependent data to the
client (such as a challenge, authentication of the server,
etc.).
  </para>
<para>
If <parameter>STATUS</parameter> is <constant>Success</constant>,
the following section of protocol is omitted.  Otherwise, if
<parameter>STATUS</parameter> is <constant>Continue</constant>,
the server expects
more authorization data from the client (i.e. the connection
setup is not finished, so no requests or events may be sent):
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry>◀</entry></row>
          <row><entry><parameter>more-authorization-data</parameter>:</entry><entry><link linkend="Data_Types:STRING8"><type>STRING8</type></link></entry></row>
          <row><entry>▶</entry></row>
          <row><entry><parameter>status</parameter>:</entry><entry>{ <constant>Success</constant>, <constant>Continue</constant>, <constant>Busy</constant>, <constant>Denied</constant> }</entry></row>
          <row><entry><parameter>more-authorization-data</parameter>:</entry><entry><link linkend="Data_Types:BYTE"><type>LISTofBYTE</type></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
</para>
<para>
The values in <parameter>STATUS</parameter> have the same meanings as described
above.  This section of protocol is repeated until the server
either accepts (sets <parameter>STATUS</parameter> to
<constant>Success</constant>) or rejects (sets <parameter>STATUS</parameter>
to <constant>Denied</constant> or <constant>Busy</constant>) the connection.
</para>
<para>
Once the connection has been accepted and <parameter>STATUS</parameter>
is <constant>Success</constant>,
an implicit AccessContext is created for the authorization
data and the protocol continues with the following data sent
from the server:
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry>▶</entry></row>
          <row><entry><parameter>remaining-length</parameter>:</entry><entry><type>CARD32</type></entry></row>
          <row><entry><parameter>maximum-request-length</parameter>:</entry><entry><type>CARD16</type></entry></row>
          <row><entry><parameter>release-number</parameter>:</entry><entry><type>CARD32</type></entry></row>
          <row><entry><parameter>vendor</parameter>:</entry><entry><link linkend="Data_Types:STRING8"><type>STRING8</type></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
</para>
<para>
The <parameter>REMAINING-LENGTH</parameter> specifies the length in 4-byte
units of the remaining data to be transmitted to the client.  The
<parameter>MAXIMUM-REQUEST-LENGTH</parameter> specifies the largest request
size in 4-byte units that is accepted by the server and must have a
value of at least 4096.  Requests with a length field larger
than this value are ignored and a
<link linkend="Errors:Length"><errorname>Length</errorname></link>
error is returned.
The <parameter>VENDOR</parameter> string specifies the name of the
manufacturer of the font server.  The
<parameter>RELEASE-NUMBER</parameter> specifies the particular
release of the server in a manufacturer-dependent manner.
</para>
</section>
<section><title />
<para>
After the connection is established and the setup information has been
exchanged, the client may issue any of requests described below:
</para>
</section>
<section id="Requests:NoOp">
    <title><function>NoOp</function></title>
    <indexterm zone="Requests:NoOp" significance="preferred"><primary>NoOp</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry>Errors:</entry><entry><link linkend="Errors:Alloc"><errorname>Alloc</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request does nothing.  It is typically used in response
to a
<link linkend="Events:KeepAlive"><function>KeepAlive</function></link>
event.
    </para>
</section>

<section id="Requests:ListExtensions">
    <title><function>ListExtensions</function></title>
    <indexterm zone="Requests:ListExtensions" significance="preferred"><primary>ListExtensions</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry>▶</entry></row>
          <row rowsep='1'><entry><parameter>names</parameter>:</entry><entry><link linkend="Data_Types:STRING8"><type>LISTofSTRING8</type></link></entry></row>
          <row><entry>Errors:</entry><entry><link linkend="Errors:Alloc"><errorname>Alloc</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>

  <para>
This request returns the names of the extension packages
that are supported by the server.  Extension names are
case-sensitive and are encoded in <acronym>ISO</acronym> 8859-1.
  </para>

</section>

<section id="Requests:QueryExtension">
    <title><function>QueryExtension</function></title>
    <indexterm zone="Requests:QueryExtension" significance="preferred"><primary>QueryExtension</primary></indexterm>

    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>name</parameter>:</entry><entry><link linkend="Data_Types:STRING8"><type>STRING8</type></link></entry></row>
          <row><entry>▶</entry></row>
          <row><entry><parameter>present</parameter>:</entry><entry><link linkend="Data_Types:BOOL"><type>BOOL</type></link></entry></row>
          <row><entry><parameter>major-version</parameter>:</entry><entry><type>CARD16</type></entry></row>
          <row><entry><parameter>minor-version</parameter>:</entry><entry><type>CARD16</type></entry></row>
          <row><entry><parameter>major-opcode</parameter>:</entry><entry><type>CARD8</type></entry></row>
          <row><entry><parameter>first-event</parameter>:</entry><entry><type>CARD8</type></entry></row>
          <row><entry><parameter>number-events</parameter>:</entry><entry><type>CARD8</type></entry></row>
          <row><entry><parameter>first-error</parameter>:</entry><entry><type>CARD8</type></entry></row>
          <row rowsep='1'><entry><parameter>number-errors</parameter>:</entry><entry><type>CARD8</type></entry></row>
          <row><entry>Errors:</entry><entry><link linkend="Errors:Alloc"><errorname>Alloc</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
  <para>
This request determines whether or not the extension package specified by
<parameter>NAME</parameter> (encoded in <acronym>ISO</acronym> 8859-1) is
supported by the server and that there is sufficient number of major opcode,
event, and error codes available.  If so, then <parameter>PRESENT</parameter>
is set to <constant>True</constant>, <parameter>MAJOR-VERSION</parameter>
and <parameter>MINOR-VERSION</parameter> are set to the
respective major and minor version numbers of the protocol
that the server would prefer; <parameter>MAJOR-OPCODE</parameter> is set to
the value to use in extension requests; <parameter>FIRST-EVENT</parameter>
is set to the value of the first extension-specific event code or zero if the
extension does not have any events; <parameter>NUMBER-EVENTS</parameter> is
set to the number of new events that the event defines;
<parameter>FIRST-ERROR</parameter>
is set to the value of the first extension-specific error code
or zero if the extension does not define any new errors; and
<parameter>NUMBER-ERRORS</parameter> is set to the number of
new errors the extension defines.
  </para>
  <para>
Otherwise, <parameter>PRESENT</parameter> is set to
<constant>False</constant> and the remaining fields are
set to zero.
  </para>
  <para>
The server is free to return different values to different
clients.  Therefore, clients must use this request before
issuing any of the requests in the named extension package or
using the
<link linkend="Requests:SetEventMask"><function>SetEventMask</function></link> request to express interest in any of
this extension's events.  Otherwise, a
<link linkend="Errors:Request"><errorname>Request</errorname></link>
error is returned.
  </para>
</section>

<section id="Requests:ListCatalogues">
    <title><function>ListCatalogues</function></title>
    <indexterm zone="Requests:ListCatalogues" significance="preferred"><primary>ListCatalogues</primary></indexterm>

    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>pattern</parameter>:</entry><entry><link linkend="Data_Types:STRING8"><type>STRING8</type></link></entry></row>
          <row><entry><parameter>max-names</parameter>:</entry><entry><type>CARD32</type></entry></row>
          <row><entry>▶+</entry></row>
          <row><entry><parameter>replies-following-hint</parameter>:</entry><entry><type>CARD32</type></entry></row>
          <row rowsep='1'><entry><parameter>names</parameter>:</entry><entry><link linkend="Data_Types:STRING8"><type>LISTofSTRING8</type></link></entry></row>
          <row><entry>Errors:</entry><entry><link linkend="Errors:Alloc"><errorname>Alloc</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request returns a list of at most <parameter>MAX-NAMES</parameter> names
of collections (called catalogues) of fonts that match
the specified <parameter>PATTERN</parameter>.  In the pattern (which is encoded
in <acronym>ISO</acronym> 8859-1), the
<quote><literal>?</literal></quote> character (octal <literal>77</literal>)
matches any single character; the
<quote><literal>*</literal></quote> character (octal <literal>52</literal>)
matches any series of zero or more characters; and alphabetic
characters match either upper- or lowercase.  The
returned <parameter>NAMES</parameter> are encoded in
<acronym>ISO</acronym> 8859-1 and may contain
mixed character cases.
    </para>
    <para>
If <parameter>PATTERN</parameter> is of zero length or
<parameter>MAX-NAMES</parameter> is equal to zero,
one reply containing a zero-length list of names is returned.
This may be used to synchronize the client with the server.
    </para>
    <para>
Servers are free to add or remove catalogues to the set returned by
<function>ListCatalogues</function>
between any two requests.  This request is not
cumulative; repeated uses are processed in isolation and do
result in an iteration through the list.
    </para>
    <para>
To reduce the amount of buffering needed by the server, the
list of names may be split across several reply packets, so
long as the names arrive in the same order that they would
have appeared had they been in a single packet.  The
<parameter>REPLIES-FOLLOWING-HINT</parameter> field in all but the last reply
contains a positive value that specifies the number of
replies that are likely, but not required, to follow.  In the
last reply, which may contain zero or more names, this field
is set to zero.
    </para>
</section>

<section id="Requests:SetCatalogues">
    <title><function>SetCatalogues</function></title>
    <indexterm zone="Requests:SetCatalogues" significance="preferred"><primary>SetCatalogues</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row rowsep='1'><entry><parameter>names</parameter>:</entry><entry><link linkend="Data_Types:STRING8"><type>LISTofSTRING8</type></link></entry></row>
          <row><entry>Errors:</entry><entry><link linkend="Errors:Alloc"><errorname>Alloc</errorname></link>,
<link linkend="Errors:Name"><errorname>Name</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request sets the list of catalogues whose fonts should be
visible to the client.  The union of the fonts provided by
each of the named catalogues forms the set of fonts whose
names match patterns in
<link linkend="Requests:ListFonts"><function>ListFonts</function></link>,
<link linkend="Requests:ListFontsWithXInfo"><function>ListFontsWithXInfo</function></link>,
and
<link linkend="Requests:OpenBitmapFont"><function>OpenBitmapFont</function></link>
requests.  The catalogue names are
case-insensitive and are encoded in <acronym>ISO</acronym> 8859-1.  A zero-length
list resets the client's catalogue list to the
server-dependent default.
    </para>
    <para>
If any of the catalogue names are invalid, a
<link linkend="Errors:Name"><errorname>Name</errorname></link>
error is returned and the request is ignored.
    </para>
</section>

<section id="Requests:GetCatalogues">
    <title><function>GetCatalogues</function></title>
    <indexterm zone="Requests:GetCatalogues" significance="preferred"><primary>GetCatalogues</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry>▶</entry></row>
          <row rowsep='1'><entry><parameter>names</parameter>:</entry><entry><link linkend="Data_Types:STRING8"><type>LISTofSTRING8</type></link></entry></row>
          <row><entry>Errors:</entry><entry><link linkend="Errors:Alloc"><errorname>Alloc</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request returns the current list of catalogue names
(encoded in <acronym>ISO</acronym> 8859-1) associated with the client.  These
catalogues determine the set of fonts that are visible
to
<link linkend="Requests:ListFonts"><function>ListFonts</function></link>,
<link linkend="Requests:ListFontsWithXInfo"><function>ListFontsWithXInfo</function></link>,
and
<link linkend="Requests:OpenBitmapFont"><function>OpenBitmapFont</function></link>.
A zero-length list indicates the server's default set of
fonts.  Catalogue names are case-insensitive and may be
returned in mixed case.
    </para>
</section>

<section id="Requests:SetEventMask">
    <title><function>SetEventMask</function></title>
    <indexterm zone="Requests:SetEventMask" significance="preferred"><primary>SetEventMask</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>extension-opcode</parameter>:</entry><entry><type>CARD8</type></entry></row>
          <row rowsep='1'><entry><parameter>event-mask</parameter>:</entry><entry><link linkend="Data_Types:EVENTMASK"><type>EVENTMASK</type></link></entry></row>
          <row><entry>Errors:</entry><entry><link linkend="Errors:EventMask"><errorname>EventMask</errorname></link>,
<link linkend="Errors:Request"><errorname>Request</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request specifies the set of maskable events that the
extension indicated by <parameter>EXTENSION-OPCODE</parameter>
(or zero for the core)
should generate for the client.  Event masks are limited in
scope to the extension (or core) for which they are defined,
so expressing interest in events from one or more extensions
requires multiple uses of this request.
    </para>
    <para>
The default event mask if
<function>SetEventMask</function>
has not been called
is zero, indicating no interest in any maskable events.
Some events are not maskable and cannot be blocked.
    </para>
    <para>
If <parameter>EXTENSION-OPCODE</parameter> is not a valid extension
opcode previously returned by
<link linkend="Requests:QueryExtension"><function>QueryExtension</function></link>
or zero, a
<link linkend="Errors:Request"><errorname>Request</errorname></link>
error is
returned.  If <parameter>EVENT-MASK</parameter> contains any bits that do not
correspond to valid events for the specified extension (or
core), an
<link linkend="Errors:EventMask"><errorname>EventMask</errorname></link>
error is returned and the request is
ignored.
    </para>
</section>

<section id="Requests:GetEventMask">
    <title><function>GetEventMask</function></title>
    <indexterm zone="Requests:GetEventMask" significance="preferred"><primary>GetEventMask</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>extension-opcode</parameter>:</entry><entry><type>CARD8</type></entry></row>
          <row><entry>▶</entry></row>
          <row rowsep='1'><entry><parameter>event-mask</parameter>:</entry><entry><link linkend="Data_Types:EVENTMASK"><type>EVENTMASK</type></link></entry></row>
          <row><entry>Errors:</entry><entry><link linkend="Errors:Request"><errorname>Request</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request returns the set of maskable core events the
extension indicated by <parameter>EXTENSION-OPCODE</parameter>
(or the core if zero)
should generate for the client.  Non-maskable events are
always sent to the client.
    </para>
    <para>
If <parameter>EXTENSION-OPCODE</parameter> is not a valid extension opcode
previously returned by
<link linkend="Requests:QueryExtension"><function>QueryExtension</function></link>
or zero, a
<link linkend="Errors:Request"><errorname>Request</errorname></link>
error is returned.
    </para>
</section>

<section id="Requests:CreateAC">
    <title><function>CreateAC</function></title>
    <indexterm zone="Requests:CreateAC" significance="preferred"><primary>CreateAC</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>ac</parameter>:</entry><entry><link linkend="Data_Types:ACCESSCONTEXT"><type>ACCESSCONTEXT</type></link></entry></row>
          <row><entry><parameter>authorization-protocols</parameter>:</entry><entry><link linkend="Data_Types:AUTH"><type>LISTofAUTH</type></link></entry></row>
          <row><entry>▶</entry></row>
          <row><entry><parameter>status</parameter>:</entry><entry>{ <constant>Success</constant>, <constant>Continue</constant>, <constant>Denied</constant> }</entry></row>
          <row><entry><parameter>authorization-index</parameter>:</entry><entry><type>CARD8</type></entry></row>
          <row rowsep='1'><entry><parameter>authorization-data</parameter>:</entry><entry><link linkend="Data_Types:BYTE"><type>LISTofBYTE</type></link></entry></row>
          <row><entry>Errors:</entry><entry><link linkend="Errors:IDChoice"><errorname>IDChoice</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request creates a new
<type>AccessContext</type>
object within the
server containing the specified authorization data.  When
this
<type>AccessContext</type>
is selected by the client using the
<link linkend="Requests:SetAuthorization"><function>SetAuthorization</function></link>
request, the data may be used by the server
to determine whether or not the client should be granted
access to particular font information.
    </para>
    <para>
If <parameter>STATUS</parameter> is <constant>Denied</constant>, the server
rejects the client's authorization information and does not associate
<parameter>AC</parameter> with any valid <type>AccessContext</type>.
In this case, <parameter>AUTHORIZATION-INDEX</parameter> is set
to zero, and zero bytes of <parameter>AUTHORIZATION-DATA</parameter>
is returned.
    </para>
    <para>
Otherwise, <parameter>AUTHORIZATION-INDEX</parameter> is set to the index
(beginning with 1) into the <parameter>AUTHORIZATION-PROTOCOLS</parameter>
list of the protocol
that the server will use for this connection.  If the server
does not want to use any of the given protocols, this value is
set to zero.  The <parameter>AUTHORIZATION-DATA</parameter> field is used
to send back authorization protocol-dependent data to the client (such
as a challenge, authentication of the server, etc.).
    </para>
    <para>
If <parameter>STATUS</parameter> is <constant>Continue</constant>,
the client is expected to continue
the request by sending the following protocol and receiving
the indicated response from the server.  This continues
until <parameter>STATUS</parameter> is set to either
<constant>Success</constant> or <constant>Denied</constant>.
    </para>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry>     ◀</entry></row>
          <row><entry><parameter>     more-authorization-data</parameter>:</entry><entry><link linkend="Data_Types:STRING8"><type>STRING8</type></link></entry></row>
          <row><entry>     ▶</entry></row>
          <row><entry><parameter>status</parameter>:</entry><entry>{ <constant>Success</constant>, <constant>Continue</constant>, <constant>Denied</constant> }</entry></row>
          <row><entry><parameter>    more-authorization-data</parameter>:</entry><entry><link linkend="Data_Types:BYTE"><type>LISTofBYTE</type></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
Once the connection has been accepted and <parameter>STATUS</parameter>
is <constant>Success</constant>, the request is complete.
    </para>
    <para>
If <parameter>AC</parameter> is not in the range
[1..2<superscript>29</superscript>-1] or is already associated
with an access context, an <link linkend="Errors:IDChoice"><errorname>IDChoice</errorname></link> error is returned.
    </para>
</section>

<section id="Requests:FreeAC">
    <title><function>FreeAC</function></title>
    <indexterm zone="Requests:FreeAC" significance="preferred"><primary>FreeAC</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>ac</parameter>:</entry><entry><link linkend="Data_Types:ACCESSCONTEXT"><type>ACCESSCONTEXT</type></link></entry></row>
          <row rowsep='1'><entry>Errors:</entry><entry><link linkend="Errors:AccessContext"><errorname>AccessContext</errorname></link>,
<link linkend="Errors:Alloc"><errorname>Alloc</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request indicates that the specified <parameter>AC</parameter> should
no longer be associated with a valid access context.
If <parameter>AC</parameter> is also the current
<type>AccessContext</type>
(as set by the
<link linkend="Requests:SetAuthorization"><function>SetAuthorization</function></link>
request), an implicit
<link linkend="Requests:SetAuthorization"><function>SetAuthorization</function></link>
of <constant>None</constant> is done to
restore the
<type>AccessContext</type>
established for the initial
connection setup.  Operations on fonts that were opened under
<parameter>AC</parameter> are not affected.  The client may reuse the
value of <parameter>AC</parameter> in a subsequent
<link linkend="Requests:CreateAC"><function>CreateAC</function></link>
request.
    </para>
    <para>
If <parameter>AC</parameter> isn't associated with any valid authorization
previously created by
<link linkend="Requests:CreateAC"><function>CreateAC</function></link>, an
<link linkend="Errors:AccessContext"><errorname>AccessContext</errorname></link>
error is returned.
    </para>
</section>

<section id="Requests:SetAuthorization">
    <title><function>SetAuthorization</function></title>
    <indexterm zone="Requests:SetAuthorization" significance="preferred"><primary>SetAuthorization</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>ac</parameter>:</entry><entry><link linkend="Data_Types:ACCESSCONTEXT"><type>ACCESSCONTEXT</type></link></entry></row>
          <row rowsep='1'><entry>Errors:</entry><entry><link linkend="Errors:AccessContext"><errorname>AccessContext</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request sets the
<type>AccessContext</type>
to be used for subsequent
requests (except for
<link linkend="Requests:QueryXInfo"><function>QueryXInfo</function></link>,
<link linkend="Requests:QueryXExtents8"><function>QueryXExtents8</function></link>,
<link linkend="Requests:QueryXExtents16"><function>QueryXExtents16</function></link>,
<link linkend="Requests:QueryXBitmaps8"><function>QueryXBitmaps8</function></link>,
<link linkend="Requests:QueryXBitmaps16"><function>QueryXBitmaps16</function></link>
and
<link linkend="Requests:CloseFont"><function>CloseFont</function></link>
which are done under the
<type>AccessContext</type>
of the
corresponding
<link linkend="Requests:OpenBitmapFont"><function>OpenBitmapFont</function></link>
).
An <parameter>AC</parameter> of <constant>None</constant> restores the
<type>AccessContext</type>
established for the initial connection setup.
    </para>
    <para>
If <parameter>AC</parameter> is neither <constant>None</constant>
nor a value associated with a valid <type>AccessContext</type>
previously created by
<link linkend="Requests:CreateAC"><function>CreateAC</function></link>,
an
<link linkend="Errors:AccessContext"><errorname>AccessContext</errorname></link>
error is returned.
    </para>
</section>

<section id="Requests:SetResolution">
    <title><function>SetResolution</function></title>
    <indexterm zone="Requests:SetResolution" significance="preferred"><primary>SetResolution</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row rowsep='1'><entry><parameter>resolutions</parameter>:</entry><entry><link linkend="Data_Types:RESOLUTION"><type>LISTofRESOLUTION</type></link></entry></row>
          <row><entry>Errors:</entry><entry><link linkend="Errors:Resolution"><errorname>Resolution</errorname></link>,
<link linkend="Errors:Alloc"><errorname>Alloc</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request provides a hint as to the resolution and
preferred point size of the drawing surfaces for which the
client will be requesting fonts.  The server may use this
information to set the RESOLUTION_X and RESOLUTION_Y fields
of scalable <acronym>XLFD</acronym> font names, to order sets of names based on
their resolutions, and to choose the server-dependent
instance that is used when a partially-specified scalable
fontname is opened.
    </para>
    <para>
If a zero-length list of <link linkend="Data_Types:RESOLUTION"><type>RESOLUTION</type></link>s is given, the
server-dependent default value is restored.  Otherwise, if
elements of all of the specified <link linkend="Data_Types:RESOLUTION"><type>RESOLUTION</type></link>s are non-zero, the
default resolutions for this client are changed.
    </para>
    <para>
If a <link linkend="Data_Types:RESOLUTION"><type>RESOLUTION</type></link> entry contains a zero,
a <link linkend="Errors:Resolution"><errorname>Resolution</errorname></link> error is
returned and the default resolutions are not changed.
    </para>
</section>

<section id="Requests:GetResolution">
    <title><function>GetResolution</function></title>
    <indexterm zone="Requests:GetResolution" significance="preferred"><primary>GetResolution</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry>▶</entry></row>
          <row rowsep='1'><entry><parameter>resolutions</parameter>:</entry><entry><link linkend="Data_Types:RESOLUTION"><type>LISTofRESOLUTION</type></link></entry></row>
          <row><entry>Errors:</entry><entry><link linkend="Errors:Alloc"><errorname>Alloc</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request returns the current list of default resolutions.
If a client has not performed a
<link linkend="Requests:SetResolution"><function>SetResolution</function></link>,
a server-dependent default value is returned.
    </para>
</section>

<section id="Requests:ListFonts">
    <title><function>ListFonts</function></title>
    <indexterm zone="Requests:ListFonts" significance="preferred"><primary>ListFonts</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>pattern</parameter>:</entry><entry><link linkend="Data_Types:STRING8"><type>STRING8</type></link></entry></row>
          <row><entry><parameter>max-names</parameter>:</entry><entry><type>CARD32</type></entry></row>
          <row><entry>▶+</entry></row>
          <row><entry><parameter>replies-following-hint</parameter>:</entry><entry><type>CARD32</type></entry></row>
          <row rowsep='1'><entry><parameter>names</parameter>:</entry><entry><link linkend="Data_Types:STRING8"><type>LISTofSTRING8</type></link></entry></row>
          <row><entry>Errors:</entry><entry><link linkend="Errors:Alloc"><errorname>Alloc</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request returns a list of at most <parameter>MAX-NAMES</parameter>
font names that match the specified <parameter>PATTERN</parameter>,
according to matching rules
of the <olink targetdoc='xlfd' targetptr='xlfd'><citetitle>X Logical
Font Description Conventions</citetitle></olink>
<xref linkend="References:xlfd-spec"/>.
In the pattern (which is encoded in <acronym>ISO</acronym> 8859-1) the
<quote><literal>?</literal></quote> character (octal <literal>77</literal>)
matches any single character; the
<quote><literal>*</literal></quote> character (octal <literal>52</literal>)
matches any series of zero or more characters; and
alphabetic characters match either upper- or lowercase.  The
returned <parameter>NAMES</parameter> are encoded in
<acronym>ISO</acronym> 8859-1 and may contain mixed
character cases.  Font names are not required to be in <acronym>XLFD</acronym>
format.
    </para>
    <para>
If <parameter>PATTERN</parameter> is of zero length or
<parameter>MAX-NAMES</parameter> is equal to zero,
one reply containing a zero-length list of names is returned.
This may be used to synchronize the client with the server.
    </para>
    <para>
Servers are free to add or remove fonts to the set returned by
<function>ListFonts</function>
between any two requests.  This request is not
cumulative; repeated uses are processed in isolation and do
result in an iteration through the list.
    </para>
    <para>
To reduce the amount of buffering needed by the server, the
list of names may be split across several reply packets, so
long as the names arrive in the same order that they would
have appeared had they been in a single packet.  The
<parameter>REPLIES-FOLLOWING-HINT</parameter> field in all but the last reply
contains a positive value that specifies the number of
replies that are likely, but not required, to follow.  In the
last reply, which may contain zero or more names, this field
is set to zero.
    </para>
</section>

<section id="Requests:ListFontsWithXInfo">
    <title><function>ListFontsWithXInfo</function></title>
    <indexterm zone="Requests:ListFontsWithXInfo" significance="preferred"><primary>ListFontsWithXInfo</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>pattern</parameter>:</entry><entry><link linkend="Data_Types:STRING8"><type>STRING8</type></link></entry></row>
          <row><entry><parameter>pattern</parameter>:</entry><entry><link linkend="Data_Types:STRING8"><type>STRING8</type></link></entry></row>
          <row><entry><parameter>pattern</parameter>:</entry><entry><link linkend="Data_Types:STRING8"><type>STRING8</type></link></entry></row>
          <row><entry><parameter>max-names</parameter>:</entry><entry><type>CARD32</type></entry></row>
          <row><entry>▶+</entry></row>
          <row><entry><parameter>replies-following-hint</parameter>:</entry><entry><type>CARD32</type></entry></row>
          <row><entry><parameter>info</parameter>:</entry><entry><link linkend="Data_Types:XFONTINFO"><type>XFONTINFO</type></link></entry></row>
          <row rowsep='1'><entry><parameter>name</parameter>:</entry><entry><link linkend="Data_Types:STRING8"><type>STRING8</type></link></entry></row>
          <row><entry>Errors:</entry><entry><link linkend="Errors:Alloc"><errorname>Alloc</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request is similar to
<link linkend="Requests:ListFonts"><function>ListFonts</function></link>
except that a separate
reply containing the name, header, and property data is
generated for each matching font name.  Following these
replies, if any, a final reply containing a zero-length
<parameter>NAME</parameter> and no <parameter>INFO</parameter> is sent.
    </para>
    <para>
The <parameter>REPLIES-FOLLOWING-HINT</parameter> field in all but the
last reply contains a positive value that specifies the number of replies
that are likely, but not required, to follow.  In the last
reply, this field is set to zero.
    </para>
    <para>
If <parameter>PATTERN</parameter> is of zero length or if
<parameter>MAX-NAMES</parameter> is equal to zero, only the final reply
containing a zero-length <parameter>NAME</parameter> and no
<parameter>INFO</parameter> is returned. This may be used to synchronize the
client with the server.
    </para>
</section>

<section id="Requests:OpenBitmapFont">
    <title><function>OpenBitmapFont</function></title>
    <indexterm zone="Requests:OpenBitmapFont" significance="preferred"><primary>OpenBitmapFont</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>fontid</parameter>:</entry><entry><link linkend="Data_Types:FONTID"><type>FONTID</type></link></entry></row>
          <row><entry><parameter>pattern</parameter>:</entry><entry><link linkend="Data_Types:STRING8"><type>STRING8</type></link></entry></row>
          <row><entry><parameter>format-mask</parameter>:</entry><entry><link linkend="Data_Types:BITMAPFORMATMASK"><type>BITMAPFORMATMASK</type></link></entry></row>
          <row><entry><parameter>format-hint</parameter>:</entry><entry><link linkend="Data_Types:BITMAPFORMAT"><type>BITMAPFORMAT</type></link></entry></row>
          <row><entry>▶</entry></row>
          <row><entry><parameter>otherid</parameter>:</entry><entry><link linkend="Data_Types:FONTID"><type>FONTID</type></link> or <constant>None</constant></entry></row>
          <row><entry><parameter>otherid-valid</parameter>:</entry><entry><link linkend="Data_Types:BOOL"><type>BOOL</type></link></entry></row>
          <row rowsep='1'><entry><parameter>cachable</parameter>:</entry><entry><link linkend="Data_Types:BOOL"><type>BOOL</type></link></entry></row>
          <row><entry>Errors:</entry><entry>
<link linkend="Errors:IDChoice"><errorname>IDChoice</errorname></link>,
<link linkend="Errors:Name"><errorname>Name</errorname></link>,
<link linkend="Errors:Format"><errorname>Format</errorname></link>,
<link linkend="Errors:AccessContext"><errorname>AccessContext</errorname></link>,
<link linkend="Errors:Alloc"><errorname>Alloc</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request looks for a server-dependent choice of the
font names that match the specified <parameter>PATTERN</parameter>
according to the rules described for
<link linkend="Requests:ListFonts"><function>ListFonts</function></link>.
If no matches are found, a
<link linkend="Errors:Name"><errorname>Name</errorname></link>
error is returned.  Otherwise, the server attempts to
open the font associated with the chosen name.
    </para>
    <para>
Permission to access the font is determined by the server
according the licensing policy used for this font.  The server
may use the client's current
<type>AccessContext</type>
(as set by the most
recent
<link linkend="Requests:SetAuthorization"><function>SetAuthorization</function></link>
request or the original connection
setup) to determine any client-specific sets of permissions.
After the font has been opened, the client is allowed to
specify a new
<type>AccessContext</type>
with
<link linkend="Requests:SetAuthorization"><function>SetAuthorization</function></link>
or release
the
<type>AccessContext</type>
using
<link linkend="Requests:FreeAC"><function>FreeAC</function></link>
.  Subsequent
<link linkend="Requests:QueryXInfo"><function>QueryXInfo</function></link>,
<link linkend="Requests:QueryXExtents8"><function>QueryXExtents8</function></link>,
<link linkend="Requests:QueryXExtents16"><function>QueryXExtents16</function></link>,
<link linkend="Requests:QueryXBitmaps8"><function>QueryXBitmaps8</function></link>,
<link linkend="Requests:QueryXBitmaps16"><function>QueryXBitmaps16</function></link>
and
<link linkend="Requests:CloseFont"><function>CloseFont</function></link>
requests on this <link linkend="Data_Types:FONTID"><type>FONTID</type></link> are
performed according to permissions granted at the time of the
<function>OpenBitmapFont</function>
request.
    </para>
    <para>
If the server is willing and able to detect that the client
has already opened the font successfully (possibly under a
different name), the <parameter>OTHERID</parameter> field
may be set to one of the
identifiers previously used to open the font.  The
<parameter>OTHERID-VALID</parameter> field indicates whether or not
<parameter>OTHERID</parameter> is still associated with an open font:
if it is <constant>True</constant>, the client may use
<parameter>OTHERID</parameter> as an alternative to
<parameter>FONTID</parameter>. Otherwise, if
<parameter>OTHERID-VALID</parameter> is <constant>False</constant>,
<parameter>OTHERID</parameter> is no longer
open but has not been reused by a subsequent
<function>OpenBitmapFont</function>
request.
    </para>
    <para>
If <parameter>OTHERID</parameter> is set to <constant>None</constant>,
then <parameter>OTHERID-VALID</parameter> should be set
to <constant>False</constant>.
    </para>
    <para>
The <parameter>FORMAT-MASK</parameter> indicates which fields in
<parameter>FORMAT-HINT</parameter>
the client is likely to use in subsequent
<function>GetXBitmaps8</function>
and
<function>GetXBitmaps16</function>
requests.  Servers may wish to use
this information to precompute certain values.
    </para>
    <para>
If <parameter>CACHABLE</parameter> is set to <constant>True</constant>,
the client may cache the font
(so that redundant opens of the same font may be avoided)
and use it with all
<type>AccessContext</type>s
during the life of the
client without violating the font's licensing policy.  This
flag is typically set whenever a font is unlicensed or is
licensed on a per-display basis.  If <parameter>CACHABLE</parameter>
is <constant>False</constant>, the
client should reopen the font for each
<type>AccessContext</type>.
    </para>
    <para>
The server is permitted to add to or remove from the set of
fonts returned by
<link linkend="Requests:ListFonts"><function>ListFonts</function></link>
between any two requests, though
mechanisms outside the protocol.  Therefore, it is possible
for this request (which is atomic) to return a different font
than would result from separate a
<link linkend="Requests:ListFonts"><function>ListFonts</function></link>
followed by an
<function>OpenBitmapFont</function>
with a non-wildcarded font name.
    </para>
    <para>
If <parameter>FONTID</parameter> is not in the range
[1..2<superscript>29</superscript>-1] or if it is already
associated with an open font, an
<link linkend="Errors:IDChoice"><errorname>IDChoice</errorname></link>
error is returned.
If no font is available that matches the specified
<parameter>PATTERN</parameter>, a
<link linkend="Errors:Name"><errorname>Name</errorname></link>
error is returned.  If the font is present but the client
is not permitted access, an
<link linkend="Errors:AccessContext"><errorname>AccessContext</errorname></link>
error is returned.
If <parameter>FORMAT-MASK</parameter> has any unspecified bits set or if any
of the fields in <parameter>FORMAT-HINT</parameter> indicated by
<parameter>FORMAT-MASK</parameter> are invalid, a
<link linkend="Errors:Format"><errorname>Format</errorname></link>
error is returned.
    </para>
</section>

<section id="Requests:QueryXInfo">
    <title><function>QueryXInfo</function></title>
    <indexterm zone="Requests:QueryXInfo" significance="preferred"><primary>QueryXInfo</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>fontid</parameter>:</entry><entry><link linkend="Data_Types:FONTID"><type>FONTID</type></link></entry></row>
          <row><entry>▶</entry></row>
          <row rowsep='1'><entry><parameter>info</parameter>:</entry><entry><link linkend="Data_Types:XFONTINFO"><type>XFONTINFO</type></link></entry></row>
          <row><entry>Errors:</entry><entry><link linkend="Errors:Font"><errorname>Font</errorname></link>,
<link linkend="Errors:Alloc"><errorname>Alloc</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request returns the font header and property information
for the open font associated with <parameter>FONTID</parameter>.
    </para>
    <para>
If <parameter>FONTID</parameter> is not associated with any open fonts, a
<link linkend="Errors:Font"><errorname>Font</errorname></link>
error
is returned.
    </para>
</section>

<section id="Requests:QueryXExtents8">
    <title><function>QueryXExtents8</function></title>
    <indexterm zone="Requests:QueryXExtents8" significance="preferred"><primary>QueryXExtents8</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>fontid</parameter>:</entry><entry><link linkend="Data_Types:FONTID"><type>FONTID</type></link></entry></row>
          <row><entry><parameter>range</parameter>:</entry><entry><link linkend="Data_Types:BOOL"><type>BOOL</type></link></entry></row>
          <row><entry><parameter>chars</parameter>:</entry><entry><link linkend="Data_Types:STRING8"><type>STRING8</type></link></entry></row>
          <row><entry>▶</entry></row>
          <row rowsep='1'><entry><parameter>extents</parameter>:</entry><entry><link linkend="Data_Types:XCHARINFO"><type>LISTofXCHARINFO</type></link></entry></row>
          <row><entry>Errors:</entry><entry><link linkend="Errors:Font"><errorname>Font</errorname></link>,
<link linkend="Errors:Range"><errorname>Range</errorname></link>,
<link linkend="Errors:Alloc"><errorname>Alloc</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request is equivalent to
<link linkend="Requests:QueryXExtents16"><function>QueryXExtents16</function></link>
except that it
uses 1-byte character codes.
    </para>
</section>

<section id="Requests:QueryXExtents16">
    <title><function>QueryXExtents16</function></title>
    <indexterm zone="Requests:QueryXExtents16" significance="preferred"><primary>QueryXExtents16</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>fontid</parameter>:</entry><entry><link linkend="Data_Types:FONTID"><type>FONTID</type></link></entry></row>
          <row><entry><parameter>range</parameter>:</entry><entry><link linkend="Data_Types:BOOL"><type>BOOL</type></link></entry></row>
          <row><entry><parameter>chars</parameter>:</entry><entry><link linkend="Data_Types:CHAR2B"><type>LISTofCHAR2B</type></link></entry></row>
          <row><entry>▶</entry></row>
          <row rowsep='1'><entry><parameter>extents</parameter>:</entry><entry><link linkend="Data_Types:XCHARINFO"><type>LISTofXCHARINFO</type></link></entry></row>
          <row><entry>Errors:</entry><entry><link linkend="Errors:Font"><errorname>Font</errorname></link>,
<link linkend="Errors:Range"><errorname>Range</errorname></link>,
<link linkend="Errors:Alloc"><errorname>Alloc</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request returns a list of glyph extents from the open
font associated with FONTID for the series of characters
specified by <parameter>RANGE</parameter> and <parameter>CHARS</parameter>.
    </para>
    <para>
If <parameter>RANGE</parameter> is <constant>True</constant>,
each succeeding pair of elements in <parameter>CHARS</parameter> is
treated as a range of characters for which extents should be
returned.  If <parameter>CHARS</parameter> contains an odd number of elements,
the font's <structfield>XFONTINFO.CHAR-RANGE.MAX-CHAR</structfield>
is implicitly appended to
the list.  If <parameter>CHARS</parameter> contains no elements, the list is
implicitly replaced with the font's
<structfield>XFONTINFO.CHAR-RANGE.</structfield>  If
any of the resulting character ranges are invalid, a
<link linkend="Errors:Range"><errorname>Range</errorname></link>
error is returned.  Otherwise, the character ranges are
concatenated in the order given by <parameter>CHARS</parameter> to produce
a set of character codes for which extents are returned.
    </para>
    <para>
If <parameter>RANGE</parameter> is <constant>False</constant>,
then <parameter>CHARS</parameter> specifies the set of character
codes for which extents are returned.  If <parameter>CHARS</parameter> is of
zero length, then a zero-length list of extents is returned.
    </para>
    <para>
The extents for each character code in the resulting set (which
may contain duplicates) are returned in the order in
which the character codes appear in the set.
At least one metric for each character shall be non-zero
unless the character is not encoded in the font, in which case
all-zero metrics are returned.
A blank, zero-width character can be encoded
with non-zero but equal left and right bearings.
    </para>
    <para>
If <parameter>FONTID</parameter> is not associated with any open fonts, a
<link linkend="Errors:Font"><errorname>Font</errorname></link>
error is
returned.  If <parameter>RANGE</parameter> is <constant>True</constant>
and <parameter>CHARS</parameter> contains any invalid ranges, a
<link linkend="Errors:Range"><errorname>Range</errorname></link>
error is returned.
    </para>
</section>

<section id="Requests:QueryXBitmaps8">
    <title><function>QueryXBitmaps8</function></title>
    <indexterm zone="Requests:QueryXBitmaps8" significance="preferred"><primary>QueryXBitmaps8</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>fontid</parameter>:</entry><entry><link linkend="Data_Types:FONTID"><type>FONTID</type></link></entry></row>
          <row><entry><parameter>range</parameter>:</entry><entry><link linkend="Data_Types:BOOL"><type>BOOL</type></link></entry></row>
          <row><entry><parameter>chars</parameter>:</entry><entry><link linkend="Data_Types:STRING8"><type>STRING8</type></link></entry></row>
          <row><entry><parameter>format</parameter>:</entry><entry><link linkend="Data_Types:BITMAPFORMAT"><type>BITMAPFORMAT</type></link></entry></row>
          <row><entry>▶+</entry></row>
          <row><entry><parameter>replies-following-hint</parameter>:</entry><entry><type>CARD32</type></entry></row>
          <row><entry><parameter>offsets</parameter>:</entry><entry><link linkend="Data_Types:OFFSET32"><type>LISTofOFFSET32</type></link></entry></row>
          <row><entry><parameter>bitmaps</parameter>:</entry><entry><link linkend="Data_Types:BYTE"><type>LISTofBYTE</type></link></entry></row>
          <row rowsep='1'><entry>Errors:</entry><entry><link linkend="Errors:Font"><errorname>Font</errorname></link>,
<link linkend="Errors:Range"><errorname>Range</errorname></link>,
<link linkend="Errors:Format"><errorname>Format</errorname></link>,
<link linkend="Errors:Alloc"><errorname>Alloc</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request is equivalent to
<link linkend="Requests:QueryXBitmaps16"><function>QueryXBitmaps16</function></link>
except that it
uses 1-byte character codes.
    </para>
</section>

<section id="Requests:QueryXBitmaps16">
    <title><function>QueryXBitmaps16</function></title>
    <indexterm zone="Requests:QueryXBitmaps16" significance="preferred"><primary>QueryXBitmaps16</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>fontid</parameter>:</entry><entry><link linkend="Data_Types:FONTID"><type>FONTID</type></link></entry></row>
          <row><entry><parameter>range</parameter>:</entry><entry><link linkend="Data_Types:BOOL"><type>BOOL</type></link></entry></row>
          <row><entry><parameter>chars</parameter>:</entry><entry><link linkend="Data_Types:CHAR2B"><type>LISTofCHAR2B</type></link></entry></row>
          <row><entry><parameter>format</parameter>:</entry><entry><link linkend="Data_Types:BITMAPFORMAT"><type>BITMAPFORMAT</type></link></entry></row>
          <row><entry>▶+</entry></row>
          <row><entry><parameter>replies-following-hint</parameter>:</entry><entry><type>CARD32</type></entry></row>
          <row><entry><parameter>offsets</parameter>:</entry><entry><link linkend="Data_Types:OFFSET32"><type>LISTofOFFSET32</type></link></entry></row>
          <row rowsep='1'><entry><parameter>bitmaps</parameter>:</entry><entry><link linkend="Data_Types:BYTE"><type>LISTofBYTE</type></link></entry></row>
          <row><entry>Errors:</entry><entry><link linkend="Errors:Font"><errorname>Font</errorname></link>,
          <link linkend="Errors:Range"><errorname>Range</errorname></link>,
          <link linkend="Errors:Format"><errorname>Format</errorname></link>,
          <link linkend="Errors:Alloc"><errorname>Alloc</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request returns a list of glyph bitmaps from the open font associated
with <parameter>FONTID</parameter> for the series of characters
specified by <parameter>RANGE</parameter> and <parameter>CHARS</parameter>.
    </para>
    <para>
If <parameter>RANGE</parameter> is <constant>True</constant>, each succeeding
pair of elements in <parameter>CHARS</parameter> is
treated as a range of characters for which bitmaps should be
returned.  If <parameter>CHARS</parameter> contains an odd number of elements,
the font's <structfield>XFONTINFO.CHAR-RANGE.MAX-CHAR</structfield>
is implicitly appended to
the list.  If <parameter>CHARS</parameter> contains no elements, the list is
implicitly replaced with the font's
<structfield>XFONTINFO.CHAR-RANGE.</structfield>  If
any of the resulting character ranges are invalid, a <link linkend="Errors:Range"><errorname>Range</errorname></link>
error is returned.  Otherwise, the character ranges are
concatenated in the order given by <parameter>CHARS</parameter> to produce
a set of character codes for which bitmaps are returned.
    </para>
    <para>
If <parameter>RANGE</parameter> is <constant>False</constant>,
then <parameter>CHARS</parameter> specifies the set of character
codes for which bitmaps are returned.  If <parameter>CHARS</parameter>
is of zero length, then a single reply containing a zero-length list of
offsets and bitmaps is returned.
    </para>
    <para>
If any of the resulting character ranges are invalid, a
<link linkend="Errors:Range"><errorname>Range</errorname></link>
error is returned.  Otherwise, the resulting character ranges
are concatenated in the order given by <parameter>CHARS</parameter>
to produce a set of character codes for which bitmaps are returned.
    </para>
    <para>
The server is free to return the glyph bitmaps in multiple
replies to reduce the amount of buffering that is necessary.
In this situation, the set of characters obtained above is
partitioned into an implementation-dependent number of
ordered, non-overlapping subsets containing runs of one or
more consecutive characters.  The global ordering of
characters must be maintained such that concatenating the
subsets in order that they were produced yields the original
set.  A reply is generated for each subset, in the order that
it was produced.
    </para>
    <para>
For each character in a subset, an image of that character's
glyph is described by a rectangle of bits corresponding to the
pixels specified by FORMAT.IMAGE-RECT.  Within the image, set
and clear bits represent inked and non-inked pixels,
respectively.
    </para>
    <para>
Each scanline of a glyph image, from top to bottom, is zero-padded
on the right to a multiple of the number of bits specified by
FORMAT.SCANLINE-PAD.  The scanline is then divided from left
to right into a sequence of FORMAT.SCANLINE-UNIT bits.  The
bits of each unit are then arranged such that the left-most
pixel is stored in the most- or least-significant bit,
according to FORMAT.BIT-ORDER-MSB.  The bytes of each unit are
then arranged such that the most- or least-significant byte,
according to FORMAT.BYTE-ORDER-MSB, is transmitted first.
Finally, the units are arranged such that the left-most is
transmitted first and the right-most is transmitted last.
    </para>
    <para>
The individual images within a subset are then concatenated in
a server-dependent order to form the <parameter>BITMAPS</parameter> data
of the reply.  If a glyph image is duplicated within a reply, the
server is free to return fewer (but at least one) copies of
the image.  If a character is not encoded within the font, a
zero-length bitmap is substituted for this character.  Each
glyph image must begin at a bit position that is a multiple of
the FORMAT.SCANLINE-UNIT.
    </para>
    <para>
The <parameter>OFFSETS</parameter> array in a reply contains one entry
for each character in the subset being returned, in the order that the
characters appear in the subset.  Each entry specifies the
starting location in bytes and size in bytes of the
corresponding glyph image in the <parameter>BITMAPS</parameter> data of that
reply (i.e. an offset may not refer to data in another reply).
    </para>
    <para>
The <parameter>REPLIES-FOLLOWING-HINT</parameter> field in all but the
last reply contains a positive value that specifies the number of replies
that are likely, but not required, to follow.  In the last
reply, which may contain data for zero or more characters,
this field is set to zero.
    </para>
    <para>
If <parameter>FONTID</parameter> is not associated with any open fonts,
a <link linkend="Errors:Font"><errorname>Font</errorname></link>
error is returned.  If <parameter>RANGE</parameter> is
<constant>True</constant> and <parameter>CHARS</parameter> contains any
invalid ranges, a
<link linkend="Errors:Range"><errorname>Range</errorname></link> error
is returned.  If <parameter>FORMAT</parameter> is invalid, a
<link linkend="Errors:Format"><errorname>Format</errorname></link> error
is returned.
    </para>
</section>

<section id="Requests:CloseFont">
    <title><function>CloseFont</function></title>
    <indexterm zone="Requests:CloseFont" significance="preferred"><primary>CloseFont</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row rowsep='1'><entry><parameter>fontid</parameter>:</entry><entry><link linkend="Data_Types:FONTID"><type>FONTID</type></link></entry></row>
          <row><entry>Errors:</entry><entry><link linkend="Errors:Font"><errorname>Font</errorname></link>, <link linkend="Errors:Alloc"><errorname>Alloc</errorname></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This request indicates that the specified <parameter>FONTID</parameter>
should no longer be associated with an open font.  The server is free to
release any client-specific storage or licenses allocated for
the font.  The client may reuse the value of <parameter>FONTID</parameter>
in a subsequent
<link linkend="Requests:OpenBitmapFont"><function>OpenBitmapFont</function></link>
request.
    </para>
    <para>
If <parameter>FONTID</parameter> is not associated with any open fonts, a
<link linkend="Errors:Font"><errorname>Font</errorname></link>
error is returned.
    </para>
</section>

<section id="Requests:close_connection">
    <title>close connection</title>
    <indexterm zone="Requests:close_connection" significance="preferred"><primary>close connection</primary></indexterm>

    <para>
When a connection is closed, a
<link linkend="Requests:CloseFont"><function>CloseFont</function></link>
is done on all fonts
that are open on the connection.  In addition, the server is
free to release any storage or licenses allocated on behalf of
the client that made the connection.
    </para>
</section>
</section>

<section id='Errors'>
<title>Errors</title>
<!-- .XS -->
<!-- (SN Errors -->
<!-- .XE -->
<para>
All errors are at least 16 bytes long and contain the following fields:
</para>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>type</parameter>:</entry><entry><type>CARD8</type></entry><entry>value of 1</entry></row>
          <row><entry><parameter>error-code</parameter>:</entry><entry><type>CARD8</type></entry></row>
          <row><entry><parameter>sequence-number</parameter>:</entry><entry><type>CARD16</type></entry></row>
          <row><entry><parameter>length</parameter>:</entry><entry><type>CARD32</type></entry></row>
          <row><entry><parameter>timestamp</parameter>:</entry><entry><link linkend="Data_Types:TIMESTAMP"><type>TIMESTAMP</type></link></entry></row>
          <row><entry><parameter>major-opcode</parameter>:</entry><entry><type>CARD8</type></entry></row>
          <row><entry><parameter>minor-opcode</parameter>:</entry><entry><type>CARD8</type></entry></row>
          <row><entry><parameter>data-or-unused</parameter>:</entry><entry><type>CARD16</type></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
<para>
The TYPE field has a value of one.  The ERROR-CODE field specifies which error
occurred.  Core errors codes are in the range 0 through 127, extension error
codes are in the range 128 through 255.  The SEQUENCE-NUMBER field contains the
least significant 16 bits of the sequence number of the request that caused the
error.  The LENGTH field specifies the length of the error packet in 4-byte
units and must have a value of at least 4.  The TIMESTAMP specifies the server
time when the error occurred.  The MAJOR-OPCODE and MINOR-OPCODE (zero for core
requests) fields specify the type of request that generated the error.  The
DATA-OR-UNUSED field may be used for 16 bits of error-specific information.  If
LENGTH is greater than four, these fields are followed by (LENGTH - 4) * 4
bytes of extra data.
</para>
<para>
The following errors are defined for the core protocol:
</para>

<section id="Errors:Request">
    <title><errorname>Request</errorname></title>
    <indexterm zone="Errors:Request" significance="preferred"><primary>Error Codes</primary><secondary><errorname>Request</errorname></secondary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>data-or-unused</parameter>:</entry><entry><type>CARD16</type></entry><entry>unused</entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This error is generated by any request that has an unknown
combination of major and minor request numbers, or by any
extension request that is issued before a
<link linkend="Requests:QueryExtension"><function>QueryExtension</function></link>
of that extension.
    </para>
</section>

<section id="Errors:Format">
    <title><errorname>Format</errorname></title>
    <indexterm zone="Errors:Format" significance="preferred"><primary>Error Codes</primary><secondary><errorname>Format</errorname></secondary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>data-or-unused</parameter>:</entry><entry><type>CARD16</type></entry><entry>unused</entry></row>
          <row><entry><parameter>format</parameter>:</entry><entry><link linkend="Data_Types:BITMAPFORMAT"><type>BITMAPFORMAT</type></link></entry><entry>bad format value</entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This error is generated by the use of an invalid <link linkend="Data_Types:BITMAPFORMAT"><type>BITMAPFORMAT</type></link>
in the
<link linkend="Requests:OpenBitmapFont"><function>OpenBitmapFont</function></link>,
<link linkend="Requests:QueryXBitmaps8"><function>QueryXBitmaps8</function></link>, and
<link linkend="Requests:QueryXBitmaps16"><function>QueryXBitmaps16</function></link>
requests.
The value that caused the error is included as extra data.
    </para>
</section>

<section id="Errors:Font">
    <title><errorname>Font</errorname></title>
    <indexterm zone="Errors:Font" significance="preferred"><primary>Error Codes</primary><secondary><errorname>Font</errorname></secondary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>data-or-unused</parameter>:</entry><entry><type>CARD16</type></entry><entry>unused</entry></row>
          <row><entry><parameter>fontid</parameter>:</entry><entry><link linkend="Data_Types:FONTID"><type>FONTID</type></link></entry><entry>bad font identifier</entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This error is generated by an invalid <link linkend="Data_Types:FONTID"><type>FONTID</type></link> in the
<link linkend="Requests:QueryXInfo"><function>QueryXInfo</function></link>,
<link linkend="Requests:QueryXExtents8"><function>QueryXExtents8</function></link>,
<link linkend="Requests:QueryXExtents16"><function>QueryXExtents16</function></link>,
<link linkend="Requests:QueryXBitmaps8"><function>QueryXBitmaps8</function></link>,
<link linkend="Requests:QueryXBitmaps16"><function>QueryXBitmaps16</function></link>
and
<link linkend="Requests:CloseFont"><function>CloseFont</function></link>
requests.  The value that caused
the error is included as extra data.
    </para>
</section>

<section id="Errors:Range">
    <title><errorname>Range</errorname></title>
    <indexterm zone="Errors:Range" significance="preferred"><primary>Error Codes</primary><secondary><errorname>Range</errorname></secondary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>data-or-unused</parameter>:</entry><entry><type>CARD16</type></entry><entry>unused</entry></row>
          <row><entry><parameter>range</parameter>:</entry><entry><link linkend="Data_Types:RANGE"><type>RANGE</type></link></entry><entry>bad range</entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This error is generated by an invalid <link linkend="Data_Types:RANGE"><type>RANGE</type></link> in the
<link linkend="Requests:QueryXExtents8"><function>QueryXExtents8</function></link>,
<link linkend="Requests:QueryXExtents16"><function>QueryXExtents16</function></link>,
<link linkend="Requests:QueryXBitmaps8"><function>QueryXBitmaps8</function></link>
and
<link linkend="Requests:QueryXBitmaps16"><function>QueryXBitmaps16</function></link>
requests.  The
value that caused the error is included as extra data.
    </para>
</section>

<section id="Errors:EventMask">
    <title><errorname>EventMask</errorname></title>
    <indexterm zone="Errors:EventMask" significance="preferred"><primary>Error Codes</primary><secondary><errorname>EventMask</errorname></secondary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>data-or-unused</parameter>:</entry><entry><type>CARD16</type></entry><entry>unused</entry></row>
          <row><entry><parameter>event-mask</parameter>:</entry><entry><link linkend="Data_Types:EVENTMASK"><type>EVENTMASK</type></link></entry><entry>bad event mask</entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This error is generated by an invalid <link linkend="Data_Types:EVENTMASK"><type>EVENTMASK</type></link> in the
<link linkend="Requests:SetEventMask"><function>SetEventMask</function></link>
request.  The value that caused the error is
included as extra data.
    </para>
</section>

<section id="Errors:AccessContext">
    <title><errorname>AccessContext</errorname></title>
    <indexterm zone="Errors:AccessContext" significance="preferred"><primary>Error Codes</primary><secondary><errorname>AccessContext</errorname></secondary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>data-or-unused</parameter>:</entry><entry><type>CARD16</type></entry><entry>unused</entry></row>
          <row><entry><parameter>ac</parameter>:</entry><entry><link linkend="Data_Types:ACCESSCONTEXT"><type>ACCESSCONTEXT</type></link></entry><entry>unaccepted <type>AccessContext</type></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This error is generated by an invalid <link linkend="Data_Types:ACCESSCONTEXT"><type>ACCESSCONTEXT</type></link> in the
<link linkend="Requests:FreeAC"><function>FreeAC</function></link>
or
<link linkend="Requests:SetAuthorization"><function>SetAuthorization</function></link>
request or by an
<link linkend="Requests:OpenBitmapFont"><function>OpenBitmapFont</function></link>
request performed without sufficient authorization.  In the
first two cases, the <link linkend="Data_Types:ACCESSCONTEXT"><type>ACCESSCONTEXT</type></link> of the errant request is
returned as extra data.  In the third case, the current
<link linkend="Data_Types:ACCESSCONTEXT"><type>ACCESSCONTEXT</type></link> is returned as extra data.
    </para>
</section>

<section id="Errors:IDChoice">
    <title><errorname>IDChoice</errorname></title>
    <indexterm zone="Errors:IDChoice" significance="preferred"><primary>Error Codes</primary><secondary><errorname>IDChoice</errorname></secondary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>data-or-unused</parameter>:</entry><entry><type>CARD16</type></entry><entry>unused</entry></row>
          <row><entry><parameter>id</parameter>:</entry><entry><link linkend="Data_Types:ID"><type>ID</type></link></entry><entry>bad identifier</entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This error is generated by an invalid or already associated
<link linkend="Data_Types:ACCESSCONTEXT"><type>ACCESSCONTEXT</type></link> identifier in a
<link linkend="Requests:CreateAC"><function>CreateAC</function></link>
request or <link linkend="Data_Types:FONTID"><type>FONTID</type></link> identifier
in an
<link linkend="Requests:OpenBitmapFont"><function>OpenBitmapFont</function></link>
request.  The value that caused the error
is included as extra data.
    </para>
</section>

<section id="Errors:Name">
    <title><errorname>Name</errorname></title>
    <indexterm zone="Errors:Name" significance="preferred"><primary>Error Codes</primary><secondary><errorname>Name</errorname></secondary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>data-or-unused</parameter>:</entry><entry><type>CARD16</type></entry><entry>unused</entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This error is generated by a font name pattern that matches
no fonts in an
<link linkend="Requests:OpenBitmapFont"><function>OpenBitmapFont</function></link>
request or no catalogue names in a
<link linkend="Requests:SetCatalogues"><function>SetCatalogues</function></link>
request.
    </para>
</section>

<section id="Errors:Resolution">
    <title><errorname>Resolution</errorname></title>
    <indexterm zone="Errors:Resolution" significance="preferred"><primary>Error Codes</primary><secondary><errorname>Resolution</errorname></secondary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>data-or-unused</parameter>:</entry><entry><type>CARD16</type></entry><entry>X value of errant resolution</entry></row>
          <row><entry><parameter>y-resolution</parameter>:</entry><entry><type>CARD16</type></entry><entry>Y value of errant resolution</entry></row>
          <row><entry><parameter>point-size</parameter>:</entry><entry><type>CARD16</type></entry><entry>point size of errant resolution</entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This error is generated in response to an invalid <link linkend="Data_Types:RESOLUTION"><type>RESOLUTION</type></link>
structure in a
<link linkend="Requests:SetResolution"><function>SetResolution</function></link>
request.  The value that caused the
error is included in the DATA-OR-UNUSED field and as extra data.
    </para>
</section>

<section id="Errors:Alloc">
    <title><errorname>Alloc</errorname></title>
    <indexterm zone="Errors:Length" significance="preferred"><primary>Error Codes</primary><secondary><errorname>Alloc</errorname></secondary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>data-or-unused</parameter>:</entry><entry><type>CARD16</type></entry><entry>unused</entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This error is generated by any request for which the server
lacks sufficient resources (especially memory).
    </para>
</section>

<section id="Errors:Length">
    <title><errorname>Length</errorname></title>
    <indexterm zone="Errors:Length" significance="preferred"><primary>Error Codes</primary><secondary><errorname>Length</errorname></secondary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>data-or-unused</parameter>:</entry><entry><type>CARD16</type></entry><entry>unused</entry></row>
          <row><entry><parameter>length</parameter>:</entry><entry><type>CARD32</type></entry><entry>bad length value</entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This error is generated by any request that has a length field
greater than (MAXIMUM-REQUEST-LENGTH * 4) bytes.  The value that
caused the error is included as extra data.
    </para>
</section>

<section id="Errors:Implementation">
    <title><errorname>Implementation</errorname></title>
    <indexterm zone="Errors:Implementation" significance="preferred"><primary>Error Codes</primary><secondary><errorname>Implementation</errorname></secondary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>data-or-unused</parameter>:</entry><entry><type>CARD16</type></entry><entry>unused</entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This error may be generated in response to any request that
the server is unable to process because it is deficient.  Use
of this error is highly discouraged and indicates lack of
conformance to the protocol.
    </para>
</section>
<section id="Errors:Extensions">
  <title>Extensions</title>
    <para>
Additional errors may be defined by extensions.
    </para>
</section>
</section>

<section id='Events'>
<title>Events</title>
<!-- .XS -->
<!-- (SN Events -->
<!-- .XE -->
<para>
Events may be generated in response to requests or at the server's discretion
after the initial connection setup information has been exchanged.  Each event
is at least 12 bytes long and contains the following fields:
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='3' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <colspec colname='c3' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>type</parameter>:</entry><entry><type>CARD8</type></entry><entry>value of 2</entry></row>
          <row><entry><parameter>event-code</parameter>:</entry><entry><type>CARD8</type></entry></row>
          <row><entry><parameter>sequence-number</parameter>:</entry><entry><type>CARD16</type></entry></row>
          <row><entry><parameter>length</parameter>:</entry><entry><type>CARD32</type></entry></row>
          <row><entry><parameter>timestamp</parameter>:</entry><entry><link linkend="Data_Types:TIMESTAMP"><type>TIMESTAMP</type></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
</para>
<para>
The TYPE field contains the value 2.  The EVENT-CODE field specifies the number
of the event and is in the range 0-127 for core events or the range 128-255 for
extensions.  The SEQUENCE-NUMBER field specifies the least significant 16 bits
of the sequence number of the last request to have been processed by the
server.  The LENGTH field specifies the number of 4-byte units in this event
packet and must always have a value of at least 3.  The <link linkend="Data_Types:TIMESTAMP"><type>TIMESTAMP</type></link> field
specifies the server time when the event occurred.  If LENGTH is greater than
three, these fields are followed by (LENGTH - 3) * 4 bytes of additional data.
</para>
<para>
Events are described using the following syntax:
  <blockquote><para>
    <emphasis role="bold"><function>EventName</function></emphasis>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>arg1</parameter>:</entry><entry><type>type1</type></entry></row>
          <row><entry> ...</entry></row>
          <row><entry><parameter>argN</parameter>:</entry><entry><type>typeN</type></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    Description
  </para></blockquote>
</para>
<para>
If an event does not provide any extra arguments, the
<parameter>arg1</parameter>...<parameter>argN</parameter>
lines are omitted from the description.
</para>
<para>
The core X Font Service protocol defines the following events:
</para>

<section id="Events:KeepAlive">
    <title><function>KeepAlive</function></title>
    <indexterm zone="Events:KeepAlive" significance="preferred"><primary>KeepAlive</primary></indexterm>
    <para>
This unsolicited, nonmaskable event may be sent by the
server to verify that the connection has not been broken
(for transports that do not provide this information).
Clients should acknowledge receipt of this request
by sending any request (such as
<link linkend="Requests:NoOp"><function>NoOp</function></link>
).
    </para>
</section>

<section id="Events:CatalogueListNotify">
    <title><function>CatalogueListNotify</function></title>
    <indexterm zone="Events:CatalogueListNotify" significance="preferred"><primary>CatalogueListNotify</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>added</parameter>:</entry><entry><link linkend="Data_Types:BOOL"><type>BOOL</type></link></entry></row>
          <row><entry><parameter>deleted</parameter>:</entry><entry><link linkend="Data_Types:BOOL"><type>BOOL</type></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This event is sent to clients that have included
<function><constant>CatalogueListChangeMask</constant></function>
in their core event mask
whenever the list of catalogues that are available has
changed.  The ADDED field is <constant>True</constant> if new catalogues have
been added to the server, otherwise it is <constant>False</constant>.  The
DELETED field is <constant>True</constant> if any existing catalogues have
been removed from the server, otherwise it is <constant>False</constant>.
    </para>
</section>

<section id="Events:FontListNotify">
    <title><function>FontListNotify</function></title>
    <indexterm zone="Events:FontListNotify" significance="preferred"><primary>FontListNotify</primary></indexterm>
    <informaltable frame='none'>
      <?dbfo keep-together="always" ?>
      <tgroup cols='2' align='left' colsep='0' rowsep='0'>
        <colspec colname='c1' colwidth='1.0*'/>
        <colspec colname='c2' colwidth='1.0*'/>
        <tbody>
          <row><entry><parameter>added</parameter>:</entry><entry><link linkend="Data_Types:BOOL"><type>BOOL</type></link></entry></row>
          <row><entry><parameter>deleted</parameter>:</entry><entry><link linkend="Data_Types:BOOL"><type>BOOL</type></link></entry></row>
        </tbody>
      </tgroup>
    </informaltable>
    <para>
This event is sent to clients that have included
<function><constant>FontListChangeMask</constant></function>
in their event mask whenever the
list of fonts that are provided by the currently selected
catalogues has changed.  The ADDED field is <constant>True</constant> if new
fonts have been added to any of the catalogues currently
used by the client, otherwise it is <constant>False</constant>.  The DELETED
field is <constant>True</constant> if any existing fonts have been removed
from any of catalogues used by the client, otherwise it
is <constant>False</constant>.
    </para>
</section>
<section id="Events:Extensions">
  <title>Extensions</title>
    <para>
Additional events may be defined by extensions.
    </para>
</section>
</section>
</chapter>

<chapter id='Protocol_Encoding'>
<title>Protocol Encoding</title>
<!-- .XS -->
<!-- (SN Protocol Encoding -->
<!-- .XE -->
<para>
Numbers that are prefixed with <quote><literal>#x</literal></quote>
are in hexadecimal (base 16).  All other
numbers are in decimal.  Requests, replies, errors, events, and compound types
are described using the syntax:
</para>
<!-- .RS -->
<literallayout class="monospaced">

    Name
     <emphasis remap='I'>count</emphasis>          <emphasis remap='I'>contents</emphasis>     <emphasis remap='I'>name</emphasis>
     ...
     <emphasis remap='I'>count</emphasis>          <emphasis remap='I'>contents</emphasis>     <emphasis remap='I'>name</emphasis>
</literallayout>

<!-- .RE -->
<para>
where COUNT is the number of bytes in the data stream occupied by this
field, CONTENTS is the name of the type as given in
<link linkend='Data_Types'>Section 4</link> or the value if
this field contains a constant, and NAME is a description of this field.
</para>
<para>
Objects containing counted lists use a lowercase single-letter variable (whose
scope is limited to the request, reply, event, or error in which it is found)
to represent the number of objects in the list.  These variables, and any
expressions in which they are used, should be treated as unsigned integers.
Multiple copies of an object are indicated by CONTENTS prefix
<quote>LISTof</quote>.
</para>
<para>
Unused bytes (whose value is undefined) will have a blank CONTENTS field and a
NAME field of <quote>unused</quote>.  Zeroed bytes (whose value must be zero)
will have a blank CONTENTS field and a NAME field of <quote>zero</quote>.
The expression pad(e) refers to the number of bytes
needed to round a value <quote>e</quote> up to the closed
multiple of four:
</para>
<!-- .RS -->
<literallayout class="monospaced">

     pad(e) = (4 - (e mod 4)) mod 4
</literallayout>

<section id='Encoding::Data_Types'>
<title>Data Types</title>
<!-- .XS -->
<!-- (SN Data Types -->
<!-- .XE -->
<literallayout class="monospaced">
<link linkend="Data_Types:ACCESSCONTEXT"><type>ACCESSCONTEXT</type></link>
4     <type>CARD32</type>                 access context

      with at least one of the following bits set:

        #x1fffffff

      but none of the following bits set:

        #xe0000000        zero


<link linkend="Data_Types:ALTERNATESERVER"><type>ALTERNATESERVER</type></link>
1     <type>BOOL</type>                   subset
1     n                      length of name
n     <type>STRING8</type>                name
p                            unused, p=pad(n+2)

<link linkend="Data_Types:AUTH"><type>AUTH</type></link>
2     n                      length of name
2     d                      length of data
n     <type>STRING8</type>                name
p                            unused, p=pad(n)
d     <type>STRING8</type>                data
q                            unused, q=pad(d)


<link linkend="Data_Types:BITMAPFORMAT"><type>BITMAPFORMAT</type></link>
4     <type>CARD32</type>                 value, union of the following bits:

        #x00000001        ByteOrderMSB
        #x00000002        BitOrderMSB
        #x00000000        <constant>ImageRectMin</constant>
        #x00000004        <constant>ImageRectMaxWidth</constant>
        #x00000008        <constant>ImageRectMax</constant>
        #x00000000        <constant>ScanlinePad8</constant>
        #x00000100        <constant>ScanlinePad16</constant>
        #x00000200        <constant>ScanlinePad32</constant>
        #x00000300        <constant>ScanlinePad64</constant>
        #x00000000        <constant>ScanlineUnit8</constant>
        #x00001000        <constant>ScanlineUnit16</constant>
        #x00002000        <constant>ScanlineUnit32</constant>
        #x00003000        <constant>ScanlineUnit64</constant>

      except for the following bits which must be zero:

        #xffffccf0        zero

      and the following of which at most one bit may be set:

        #x0000000c        at most one bit can be set


<link linkend="Data_Types:BITMAPFORMATMASK"><type>BITMAPFORMATMASK</type></link>
4     <type>CARD32</type>                 value, mask of the following bits:

        #x00000001        <constant>ByteOrderMask</constant>
        #x00000002        <constant>BitOrderMask</constant>
        #x00000004        <constant>ImageRectMask</constant>
        #x00000008        <constant>ScanlinePadMask</constant>
        #x00000010        <constant>ScanlineUnitMask</constant>

      except for the following bits which must be zero:

        #xffffffe0        zero

<link linkend="Data_Types:BOOL"><type>BOOL</type></link>
1     <type>BOOL</type>                   boolean, one of the following values:
        0                 <constant>False</constant>
        1                 <constant>True</constant>

<link linkend="Data_Types:BYTE"><type>BYTE</type></link>
1     <type>BYTE</type>                   unsigned byte of data

<type>CARD8</type>
1     <type>CARD8</type>                  8-bit unsigned integer

<type>CARD16</type>
2     <type>CARD16</type>                 16-bit unsigned integer

<type>CARD32</type>
4     <type>CARD32</type>                 32-bit unsigned integer

<link linkend="Data_Types:CHAR2B"><type>CHAR2B</type></link>
1     <type>CARD8</type>                  byte1
1     <type>CARD8</type>                  byte2

<link linkend="Data_Types:EVENTMASK"><type>EVENTMASK</type></link>
4     <type>CARD32</type>                 event mask

      for core events, this is union of the following bits:

        #00000001         <constant>CatalogueListChangeMask</constant>
        #00000002         <constant>FontListChangeMask</constant>

      but none of the following bits set:

        #fffffffc

      extensions define their own sets of bits

<link linkend="Data_Types:FONTID"><type>FONTID</type></link>
4     <type>CARD32</type>                 font identifier

      with at least one of the following bits set:

        #x1fffffff

      but none of the following bits set:

        #xe0000000        zero

<type>INT8</type>
1     <type>INT8</type>                   8-bit signed integer

<type>INT16</type>
2     <type>INT16</type>                  16-bit signed integer

<type>INT32</type>
4     <type>INT32</type>                  32-bit signed integer

<link linkend="Data_Types:OFFSET32"><type>OFFSET32</type></link>
4     <type>CARD32</type>                 position (or integer value)
4     <type>CARD32</type>                 length

<link linkend="Data_Types:PROPINFO"><type>PROPINFO</type></link>
4     n                      number of <type>PROPOFFSET</type> components
4     m                      number of bytes of property data
20*n  <type>PROPOFFSET</type>             property offsets into data block
m     <type>LISTofBYTE</type>             property data block

<link linkend="Data_Types:PROPOFFSET"><type>PROPOFFSET</type></link>
8     <type>OFFSET32</type>               name in data block
8     <type>OFFSET32</type>               value in data block
1     <type>CARD8</type>                  type, one of the following values:
        0                 <constant>String</constant>
        1                 <constant>Unsigned</constant>
        2                 <constant>Signed</constant>
        3                 zero

<link linkend="Data_Types:RANGE"><type>RANGE</type></link>
2     <type>CHAR2B</type>                 minimum character code
2     <type>CHAR2B</type>                 maximum character code

<link linkend="Data_Types:RESOLUTION"><type>RESOLUTION</type></link>
2     <type>CARD16</type>                 x resolution in pixels per inch
2     <type>CARD16</type>                 y resolution in pixels per inch
2     <type>CARD16</type>                 point size in decipoints

STRNAME
1     n                      length of name
n     <type>STRING8</type>                name

<link linkend="Data_Types:STRING8"><type>STRING8</type></link>
n     <type>LISTofBYTE</type>             array of 8-bit character values

<link linkend="Data_Types:TIMESTAMP"><type>TIMESTAMP</type></link>
4     <type>CARD32</type>                 milliseconds since server time origin

<link linkend="Data_Types:XCHARINFO"><type>XCHARINFO</type></link>
2     <type>INT16</type>                  left bearing
2     <type>INT16</type>                  right bearing
2     <type>INT16</type>                  width
2     <type>INT16</type>                  ascent
2     <type>INT16</type>                  descent
2     <type>CARD16</type>                 attributes

<link linkend="Data_Types:XFONTINFO"><type>XFONTINFO</type></link>
4     <type>CARD32</type>                 flags, union of the following bits:

        #x00000001        <constant>AllCharactersExist</constant>
        #x00000002        <constant>InkInside</constant>
        #x00000004        <constant>HorizontalOverlap</constant>

      but none of the following bits set:

        #xfffffff8        zero

4     <type>RANGE</type>                  range of characters in font
1     <type>CARD8</type>                  drawing direction
        0                 <constant>LeftToRight</constant>
        1                 <constant>RightToLeft</constant>
1                            unused
2     <type>CHAR2B</type>                 default character
12    <type>XCHARINFO</type>              minimum bounds
12    <type>XCHARINFO</type>              maximum bounds
2     <type>INT16</type>                  font ascent
2     <type>INT16</type>                  font descent
n     <type>PROPINFO</type>               property data
</literallayout>
</section>

<section id='Encoding::Requests'>
<title>Requests</title>
<para><link linkend="Requests:open_connection"><emphasis role="bold">open connection</emphasis></link></para>
<literallayout class="monospaced">
1     <type>BYTE</type>                   byteorder, one of the values:
        #x42              MostSignificant Byte first
        #x6c              LeastSignificant Byte first
1     <type>CARD8</type>                  numberof auth in auth-data
2     2                      client-major-protocol-version
2     0                      client-minor-protocol-version
2     a/4 lengthof           auth-data
a     <type>LISTofAUTH</type>             auth-data
▶
2     <type>CARD16</type>                 status
        0                 <constant>Success</constant>
        1                 <constant>Continue</constant>
        2                 <constant>Busy</constant>
        3                 <constant>Denied</constant>
2     2                      major version
2     0                      version
1     <type>CARD8</type>                  numberof alternate-servers-hint
1     <type>CARD8</type>                  authorization-index
2     a/4                    lengthof alternate-servers-hint
2     (d+q)/4                lengthof authorization-data
a     <type>LISTofALTERNATESERVER</type>  alternate-servers-hint
d     <type>LISTofBYTE</type>             authorization-data
q                            unused, q=pad(d)
</literallayout>

<para>
If STATUS is <constant>Busy</constant> or <constant>Denied</constant>, the protocol stops and the connection is
closed. If STATUS is <constant>Continue</constant>, the client is expected to respond with
additional data, to which the server responds with
a new status value and more data. This dialog continues until the status
is set to <constant>Success</constant>, or until the server sets STATUS to <constant>Busy</constant> or <constant>Denied</constant>
and closes the connection:
</para>

<literallayout class="monospaced">
◀
4     1+(d+q)/4              length
d     <type>LISTofBYTE</type>             more-authorization-data
q                            unused, q=pad(d)
▶
4     2+(d+q)/4              length
2     <type>CARD16</type>                 status
        0                 <constant>Success</constant>
        1                 <constant>Continue</constant>
        2                 <constant>Busy</constant>
        3                 <constant>Denied</constant>
2                            unused
d     <type>LISTofBYTE</type>             more-authorization-data
q                            unused, q=pad(d)
</literallayout>
<para>
When STATUS is <constant>Success</constant>, the protocol resumes with the following
sent by the server:
</para>

<literallayout class="monospaced">
4     3+(v+w)/4              length of rest of data
2     <type>CARD16</type>                 maximum-request-length
2     v                      length of vendor string
4     <type>CARD32</type>                 release-number
v     <type>STRING8</type>                vendor-string
w                            unused, w=pad(v)
</literallayout>
<para>
Once the connection has been established, the client may send the
following requests:
</para>

<literallayout class="monospaced">
<link linkend="Requests:NoOp"><emphasis role="bold"><function>NoOp</function></emphasis></link>
1   0                        major-opcode
1                            unused
2   1                        length

<link linkend="Requests:ListExtensions"><emphasis role="bold"><function>ListExtensions</function></emphasis></link>
1   1                        major-opcode
1                            unused
2   1                        length
▶
1   0                        type reply
1   <type>CARD8</type>                    numberof names
2   <type>CARD16</type>                   sequence-number
4   2+(n+p)/4                length
n   LISTofSTRNAME            names
p                            unused, p=pad(n)

<link linkend="Requests:QueryExtension"><emphasis role="bold"><function>QueryExtension</function></emphasis></link>
1   2                        major-opcode
1   n                        length of name
2   1+(n+p)/4                length
n   <type>STRING8</type>                  name
p                            unused, p=pad(n)
▶
1   0                        type reply
1   <type>BOOL</type>                     present
2   <type>CARD16</type>                   sequence-number
4   5                        length
2   <type>CARD16</type>                   major-version
2   <type>CARD16</type>                   minor-version
1   <type>CARD8</type>                    major-opcode
1   <type>CARD8</type>                    first-event
1   <type>CARD8</type>                    number-events
1   <type>CARD8</type>                    first-error
1   <type>CARD8</type>                    number-errors
3                            unused

<link linkend="Requests:ListCatalogues"><emphasis role="bold"><function>ListCatalogues</function></emphasis></link>
1   3                        major-opcode
1                            unused
2   3+(n+p)/4                length
4   <type>CARD32</type>                   max-names
2   n                        length of pattern
2                            unused
n   <type>STRING8</type>                  pattern
p                            unused, p=pad(n)
▶+
1   0                        type reply
1                            unused
2   <type>CARD16</type>                   sequence-number
4   4+(n+p)/4                length
4   <type>CARD32</type>                   replies-following-hint
4   <type>CARD32</type>                   numberof catalogue-names
n   LISTofSTRNAME            catalogue-names
p                            unused, p=pad(n)

<link linkend="Requests:SetCatalogues"><emphasis role="bold"><function>SetCatalogues</function></emphasis></link>
1   4                        major-opcode
1   <type>CARD8</type>                    numberof catalogue-names
2   1+(n+p)/4                length
n   LISTofSTRNAME            catalogue-names
p                            unused, p=pad(n)

<link linkend="Requests:GetCatalogues"><emphasis role="bold"><function>GetCatalogues</function></emphasis></link>
1   5                        major-opcode
1                            unused
2   1                        length
▶
1   0                        type reply
1   <type>CARD8</type>                    numberof catalogue-names
2   <type>CARD16</type>                   sequence-number
4   2+(n+p)/4                length
n   LISTofSTRNAME            catalogue-names
p                            unused, p=pad(n)

<link linkend="Requests:SetEventMask"><emphasis role="bold"><function>SetEventMask</function></emphasis></link>
1   6                        major-opcode
1   <type>CARD8</type>                    extension-opcode
2   2                        length
4   <type>EVENTMASK</type>                event-mask

<link linkend="Requests:GetEventMask"><emphasis role="bold"><function>GetEventMask</function></emphasis></link>
1   7                        major-opcode
1   <type>CARD8</type>                    extension-opcode
2   1                        length
▶
1   0                        type reply
1                            unused
2   <type>CARD16</type>                   sequence-number
4   3                        length
4   <type>EVENTMASK</type>                event-mask

<link linkend="Requests:CreateAC"><emphasis role="bold"><function>CreateAC</function></emphasis></link>
1   8                        major-opcode
1   <type>CARD8</type>                    numberof authorization-protocols
2   2+a/4                    length
4   <type>ACCESSCONTEXT</type>            ac
a   <type>LISTofAUTH</type>               authorization-protocols
▶
1   0                        type reply
1   <type>CARD8</type>                    authorization-index
2   <type>CARD16</type>                   sequence-number
4   3+(d+q)/4                length
2   <type>CARD16</type>                   status
        0                    <constant>Success</constant>
        1                    <constant>Continue</constant>
        2                    <constant>Busy</constant>
        3                    <constant>Denied</constant>
2                            unused
d   <type>LISTofBYTE</type>               authorization-data
q                            unused, q=pad(d)
</literallayout>

<para>
If STATUS is <constant>Continue</constant>, the client is expected to respond with additional
data, to which the server
responds with a new status value and more data. This dialog continues
until the status is set to
<constant>Success</constant>, <constant>Busy</constant>, or <constant>Denied</constant> at which point the request is finished.
</para>

<literallayout class="monospaced">
◀
4   1+(d+q)/4                length
d   <type>LISTofBYTE</type>               more-authorization-data
q                            unused, q=pad(d)
▶
4   2+(d+q)/4                length
2   <type>CARD16</type>                   status
        0                    <constant>Success</constant>
        1                    <constant>Continue</constant>
        2                    <constant>Busy</constant>
        3                    <constant>Denied</constant>
2                            unused
d   <type>LISTofBYTE</type>               authorization-data
q                            unused, q=pad(d)

<link linkend="Requests:FreeAC"><emphasis role="bold"><function>FreeAC</function></emphasis></link>
1   9                        major-opcode
1                            unused
2   2                        length
4   <type>ACCESSCONTEXT</type>            ac

<link linkend="Requests:SetAuthorization"><emphasis role="bold"><function>SetAuthorization</function></emphasis></link>
1   10                       major-opcode
1                            unused
2   2                        length
4   <type>ACCESSCONTEXT</type>            ac

<link linkend="Requests:SetResolution"><emphasis role="bold"><function>SetResolution</function></emphasis></link>
1   11                       major-opcode
1   n                        number of resolutions
2   1+(6*n+p)/4              length
6*n <type>LISTofRESOLUTION</type>         resolutions
p   p=pad(6*n)

<link linkend="Requests:GetResolution"><emphasis role="bold"><function>GetResolution</function></emphasis></link>
1   12                       major-opcode
1                            unused
2   1                        length
▶
1   0                        type reply
1   n                        number of resolutions
2   <type>CARD16</type>                   sequence-number
4   2+(6*n+p)/4              length
6*n <type>LISTofRESOLUTION</type>         resolutions
p   p=pad(6*n)

<link linkend="Requests:ListFonts"><emphasis role="bold"><function>ListFonts</function></emphasis></link>
1   13                       major-opcode
1                            unused
2   3+(n+p)/4                length
4   <type>CARD32</type>                   max-names
2   n                        length of pattern
2                            unused
n   <type>STRING8</type>                  pattern
p                            unused, p=pad(n)
▶+
1   0                        type reply
1                            unused
2   <type>CARD16</type>                   sequence-number
4   4+(n+p)/4                length
4   <type>CARD32</type>                   replies-following-hint
4   <type>CARD32</type>                   numberof font-names
n   LISTofSTRNAME            font-names
p                            unused, p=pad(n)

<link linkend="Requests:ListFontsWithXInfo"><emphasis role="bold"><function>ListFontsWithXInfo</function></emphasis></link>
1   14                       major-opcode
1                            unused
2   3+(n+p)/4                length
4   <type>CARD32</type>                   max-names
2   n                        length of pattern
2                            unused
n   <type>STRING8</type>                  pattern
p                            unused, p=pad(n)
▶+ (except for last in series)
1   0                        type reply
1   n                        length of name
2   <type>CARD16</type>                   sequence-number
4   3+(n+p+f)/4              length
4   <type>CARD32</type>                   replies-hint
f   <type>XFONTINFO</type>                fontinfo
n   <type>STRING8</type>                  name
p                            unused, p=pad(n)
▶ (last in series)
1   0                        type reply
1   0                        last-reply indicator
2   <type>CARD16</type>                   sequence-number
4   2                        reply length

<link linkend="Requests:OpenBitmapFont"><emphasis role="bold"><function>OpenBitmapFont</function></emphasis></link>
1   15                       major-opcode
1                            unused
2   4+(n+p)/4                length
4   <type>FONTID</type>                   fontid
4   <type>BITMAPFORMATMASK</type>         format-mask
4   <type>BITMAPFORMAT</type>             format
n   STRNAME                  pattern
p                            unused, p=pad(n)
▶
1   0                        type reply
1   <type>BOOL</type>                     otherid-valid
2   <type>CARD16</type>                   sequence-number
4   4                        length
4   <type>FONTID</type>                   otherid
1   <type>BOOL</type>                     cachable
3                            unused

<link linkend="Requests:QueryXInfo"><emphasis role="bold"><function>QueryXInfo</function></emphasis></link>
1   16                       major-opcode
1                            unused
2   2                        length
4   <type>FONTID</type>                   fontid
▶
1   0                        type reply
1                            unused
2   <type>CARD16</type>                   sequence-number
4   2+f/4                    length
f   <type>XFONTINFO</type>                fontinfo
p                            unused, p=pad(f)

<link linkend="Requests:QueryXExtents8"><emphasis role="bold"><function>QueryXExtents8</function></emphasis></link>
1   17                       major-opcode
1   <type>BOOL</type>                     range
2   3+(n+p)/4                length
4   <type>FONTID</type>                   fontid
4   n                        number chars entries
n   <type>STRING8</type>                  chars
p                            unused, p=pad(n)
▶
1   0                        type reply
1                            unused
2   <type>CARD16</type>                   sequence-number
4   3+3*n                    length
4   n                        number of extents
12*n <type>LISTofXCHARINFO</type>         extents

<link linkend="Requests:QueryXExtents16"><emphasis role="bold"><function>QueryXExtents16</function></emphasis></link>
1   18                       major-opcode
1   <type>BOOL</type>                     range
2   3+(2*n+p)/4              length
4   <type>FONTID</type>                   fontid
4   n                        number chars entries
2*n                          <type>LISTofCHAR2B</type> chars
p                            unused, p=pad(2*n)
▶
1   0                        type reply
1                            unused
2   <type>CARD16</type>                   sequence-number
4   3+3*n                    length
4   n                        number of extents
12*n <type>LISTofXCHARINFO</type>         extents

<link linkend="Requests:QueryXBitmaps8"><emphasis role="bold"><function>QueryXBitmaps8</function></emphasis></link>
1   19                       major-opcode
1   <type>BOOL</type>                     range
2   4+(n+p)/4                length
4   <type>FONTID</type>                   fontid
4   <type>BITMAPFORMAT</type>             format
4   n                        number of chars entries
n   <type>STRING8</type>                  chars
p                            unused, p=pad(n)
▶+
1   0                        type reply
1                            unused
2   <type>CARD16</type>                   sequence-number
4   5+2*n+(m+p)/4            length
4   <type>CARD32</type>                   replies-following-hint
4   n                        number of offsets
4   m                        number of bytes of glyph images
8*n <type>LISTofOFFSET32</type>           offsets
m   <type>LISTofBYTE</type>               glyphimages
p                            unused, p=pad(m)

<link linkend="Requests:QueryXBitmaps16"><emphasis role="bold"><function>QueryXBitmaps16</function></emphasis></link>
1   20                       major-opcode
1   <type>BOOL</type>                     range
2   4+(2*n+p)/4              length
4   <type>FONTID</type>                   fontid
4   <type>BITMAPFORMAT</type>             format
4   n                        number of chars entries
2*n <type>LISTofCHAR2B</type>             chars
p                            unused, p=pad(2*n)
▶
1   0                        type reply
1                            unused
2   <type>CARD16</type>                   sequence-number
4   5+2*n+(m+p)/4            length
4   <type>CARD32</type>                   replies-following-hint
4   n                        number of offsets
4   m                        number of bytes of glyph images
8*n <type>LISTofOFFSET32</type>           offsets
m   <type>LISTofBYTE</type>               glyphimages
p                            unused, p=pad(m)

<link linkend="Requests:CloseFont"><emphasis role="bold"><function>CloseFont</function></emphasis></link>
1   21                       major-opcode
1                            unused
2   2                        length
4   <type>FONTID</type>                   fontid
</literallayout>
</section>

<section id='Encoding::Errors'>
<title>Errors</title>
<literallayout class="monospaced">

<link linkend="Errors:Request"><emphasis role="bold"><errorname>Request</errorname></emphasis></link>
1   1                        type error
1   0                        <errorname>Request</errorname>
2   <type>CARD16</type>                   sequence-number
4   4                        length
4   <type>TIMESTAMP</type>                timestamp
1   <type>CARD8</type>                    major-opcode
1   <type>CARD8</type>                    minor-opcode
2                            unused

<link linkend="Errors:Format"><emphasis role="bold"><errorname>Format</errorname></emphasis></link>
1   1                        type error
1   1                        <errorname>Format</errorname>
2   <type>CARD16</type>                   sequence-number
4   5                        length
4   <type>TIMESTAMP</type>                timestamp
1   <type>CARD8</type>                    major-opcode
1   <type>CARD8</type>                    minor-opcode
2                            unused
4   <type>BITMAPFORMAT</type>             bad-format

<link linkend="Errors:Font"><emphasis role="bold"><errorname>Font</errorname></emphasis></link>
1   1                        type error
1   2                        <errorname>Font</errorname>
2   <type>CARD16</type>                   sequence-number
4   5                        length
4   <type>TIMESTAMP</type>                timestamp
1   <type>CARD8</type>                    major-opcode
1   <type>CARD8</type>                    minor-opcode
2                            unused
4   <type>FONTID</type>                   bad-fontid

<link linkend="Errors:Range"><emphasis role="bold"><errorname>Range</errorname></emphasis></link>
1   1                        type error
1   3                        <errorname>Range</errorname>
2   <type>CARD16</type>                   sequence-number
4   5                        length
4   <type>TIMESTAMP</type>                timestamp
1   <type>CARD8</type>                    major-opcode
1   <type>CARD8</type>                    minor-opcode
2                            unused
4   <type>RANGE</type>                    bad-range

<link linkend="Errors:EventMask"><emphasis role="bold"><errorname>EventMask</errorname></emphasis></link>
1   1                        type error
1   4                        <errorname>EventMask</errorname>
2   <type>CARD16</type>                   sequence-number
4   5                        length
4   <type>TIMESTAMP</type>                timestamp
1   <type>CARD8</type>                    major-opcode
1   <type>CARD8</type>                    minor-opcode
2                            unused
4   <type>EVENTMASK</type>                event-mask

<link linkend="Errors:AccessContext"><emphasis role="bold"><errorname>AccessContext</errorname></emphasis></link>
1   1                        type error
1   5                        <errorname>AccessContext</errorname>
2   <type>CARD16</type>                   sequence-number
4   5                        length
4   <type>TIMESTAMP</type>                timestamp
1   <type>CARD8</type>                    major-opcode
1   <type>CARD8</type>                    minor-opcode
2                            unused
4   <type>ACCESSCONTEXT</type>            access context

<link linkend="Errors:IDChoice"><emphasis role="bold"><errorname>IDChoice</errorname></emphasis></link>
1   1                        type error
1   6                        <errorname>IDChoice</errorname>
2   <type>CARD16</type>                   sequence-number
4   5                        length
4   <type>TIMESTAMP</type>                timestamp
1   <type>CARD8</type>                    major-opcode
1   <type>CARD8</type>                    minor-opcode
2                            unused
4   <type>FONTID</type>                   bad-fontid

<link linkend="Errors:Name"><emphasis role="bold"><errorname>Name</errorname></emphasis></link>
1   1                        type error
1   7                        <errorname>Name</errorname>
2   <type>CARD16</type>                   sequence-number
4   4                        length
4   <type>TIMESTAMP</type>                timestamp
1   <type>CARD8</type>                    major-opcode
1   <type>CARD8</type>                    minor-opcode
2                            unused

<link linkend="Errors:Resolution"><emphasis role="bold"><errorname>Resolution</errorname></emphasis></link>
1   1                        type error
1   8                        <errorname>Resolution</errorname>
2   <type>CARD16</type>                   sequence-number
4   5                        length
4   <type>TIMESTAMP</type>                timestamp
1   <type>CARD8</type>                    major-opcode
1   <type>CARD8</type>                    minor-opcode
6   <type>RESOLUTION</type>               resolution

<link linkend="Errors:Alloc"><emphasis role="bold"><errorname>Alloc</errorname></emphasis></link>
1   1                        type error
1   9                        <errorname>Alloc</errorname>
2   <type>CARD16</type>                   sequence-number
4   4                        length
4   <type>TIMESTAMP</type>                timestamp
1   <type>CARD8</type>                    major-opcode
1   <type>CARD8</type>                    minor-opcode
2                            unused

<link linkend="Errors:Length"><emphasis role="bold"><errorname>Length</errorname></emphasis></link>
1   1                        type error
1   10                       <errorname>Length</errorname>
2   <type>CARD16</type>                   sequence-number
4   5                        length
4   <type>TIMESTAMP</type>                timestamp
1   <type>CARD8</type>                    major-opcode
1   <type>CARD8</type>                    minor-opcode
2                            unused
4   <type>CARD32</type>                   bad-length

<link linkend="Errors:Implementation"><emphasis role="bold"><errorname>Implementation</errorname></emphasis></link>
1   1                        type error
1   11                       <errorname>Implementation</errorname>
2   <type>CARD16</type>                   sequence-number
4   4                        length
4   <type>TIMESTAMP</type>                timestamp
1   <type>CARD8</type>                    major-opcode
1   <type>CARD8</type>                    minor-opcode
2                            unused

</literallayout>
</section>

<section id='Encoding::Events'>
<title>Events</title>
<literallayout class="monospaced">
<link linkend="Events:KeepAlive"><emphasis role="bold"><function>KeepAlive</function></emphasis></link>
1   2                        type event
1   0                        event KeepAlive
2   <type>CARD16</type>                   sequence-number
4   3                        length
4   <type>TIMESTAMP</type>                timestamp

<link linkend="Events:CatalogueListNotify"><emphasis role="bold"><function>CatalogueListNotify</function></emphasis></link>
1   2                        type event
1   1                        event CatalogueListNotify
2   <type>CARD16</type>                   sequence-number
4   4                        length
4   <type>TIMESTAMP</type>                timestamp
1   <type>BOOL</type>                     added
1   <type>BOOL</type>                     deleted
2                            unused

<link linkend="Events:FontListNotify"><emphasis role="bold"><function>FontListNotify</function></emphasis></link>
1   2                        type event
1   2                        event FontListNotify
2   <type>CARD16</type>                   sequence-number
4   4                        length
4   <type>TIMESTAMP</type>                timestamp
1   <type>BOOL</type>                     added
1   <type>BOOL</type>                     deleted
2                            unused

</literallayout>
</section>
</chapter>

<chapter id='Acknowledgements'>
<title>Acknowledgements</title>
<!-- .XS -->
<!-- (SN Acknowledgements -->
<!-- .XE -->
<para>
This document represents the culmination of several years of debate and
experiments done under the auspices of the
<orgname class="consortium">MIT X Consortium</orgname> font working group.
Although this was a group effort, the author remains responsible for any errors
or omissions.  The protocol presented here was primarily designed by
<personname><firstname>Jim</firstname><surname>Fulton</surname></personname>,
<personname><firstname>Keith</firstname><surname>Packard</surname></personname>,
and
<personname><firstname>Bob</firstname><surname>Scheifler</surname></personname>.
Special thanks goes to
<personname><firstname>Ned</firstname><surname>Batchelder</surname></personname>,
<personname><firstname>Jim</firstname><surname>Flowers</surname></personname>,
and
<personname><firstname>Axel</firstname><surname>Deininger</surname></personname>
for their invigorating comments
which never failed to make this a better document.
<personname><firstname>Stephen</firstname><surname>Gildea</surname></personname>
edited version 2 of this document.  Finally,
<personname><firstname>David</firstname><surname>Lemke</surname></personname>
deserves great credit for designing and coding the sample implementation.
</para>
</chapter>

<bibliography id='References'>
<title>References</title>
<para>
All of the following documents are X Consortium standards available from
the X Consortium.
</para>
<biblioentry id='References:x11proto'>
  <abbrev>1</abbrev>
  <title><olink targetdoc='x11protocol' targetptr='x11protocol'>X Window System Protocol Version 11</olink></title>
  <author><firstname>Robert W.</firstname><surname>Scheifler</surname></author>
</biblioentry>

<biblioentry id='References:bdf-spec'>
  <abbrev>2</abbrev>
  <corpauthor>Adobe Systems</corpauthor>
  <title>Bitmap Distribution Format 2.1</title>
</biblioentry>

<biblioentry id='References:xlfd-spec'>
  <abbrev>3</abbrev>
  <corpauthor>X Consortium</corpauthor>
  <title><olink targetdoc='xlfd' targetptr='xlfd'>X Logical Font
  Description Conventions, Version 1.5</olink></title>
</biblioentry>

</bibliography>

<appendix id="suggested_licensing_policies">
<title>Suggested Licensing Policies</title>
<para>
The authorization data passed by the client in the initial connection
setup information may be used by the font server to implement restrictions
on which fonts may be accessed.  Furthermore, the font server is free to
refuse new connections at any time.
</para>
<para>
Configuration or management of the license restrictions is outside the scope of
the font service protocol and is done in a server-dependent manner.  Possible
policies might include, but are not limited to, combinations of the following:

<variablelist>
  <?dbhtml list-presentation="list"?>
  <varlistentry>
    <term>No restrictions</term>
  <listitem>
    <para>
anyone may access any fonts.  The server neither refuses any connections
nor generates <link linkend="Errors:AccessContext"><errorname>AccessContext</errorname></link> errors on any
fonts.  For environments without specially-licensed fonts, this is
sufficient.
    </para>
  </listitem>
  </varlistentry>
  <varlistentry>
    <term>Per-machine</term>
  <listitem>
    <para>
only those clients connecting from a known set of
machines are permitted access.  The server could get the address
of the connection and look in a list of allowed machines.
    </para>
  </listitem>
  </varlistentry>
  <varlistentry>
    <term>Per-user</term>
  <listitem>
    <para>
only a known set of users may access the fonts.  The
server can use the authorization data (such as a Kerberos ticket
or a Secure RPC credential) to verify the identity of the user
and then look in a list of allowed users.
    </para>
  </listitem>
  </varlistentry>
  <varlistentry>
    <term>Simultaneous Use</term>
  <listitem>
    <para>
only a certain number of clients may use a given font at any one time.
Additional clients would receive <link linkend="Errors:AccessContext"><errorname>AccessContext</errorname></link>
errors if they attempt to open the font.  This is only effective if
the initial clients keep the font open for the entire time that it
is being used (even if all of the data has been transmitted and is
being cached).
    </para>
  </listitem>
  </varlistentry>
  <varlistentry>
    <term>Postage Meter</term>
  <listitem>
    <para>
a particular font may only be accessed a limited
number of times before its license must be renewed.  Each time
the font is opened, the server decrements a counter.  When the
counter reaches zero, all further attempts to open the font
return an <link linkend="Errors:AccessContext"><errorname>AccessContext</errorname></link> error.
    </para>
  </listitem>
  </varlistentry>
</variablelist>
</para>

<para>
It should be noted that chaining of font servers (obtaining font data from
other font servers) may conflict with certain license policies.
</para>
</appendix>

<appendix id="implementation_suggestions">
<title>Implementation Suggestions</title>
<para>
Font server implementations will probably wish to use techniques such as the
following to avoid limits on the number of simultaneous connections:
</para>
<itemizedlist>
  <listitem>
    <para>
The initial connection information returned by the font
server contains the names of other font servers that
may be used as substitutes.  A font server may refuse to
accept a connection, indicating that the client should
try one of the alternatives instead.
    </para>
  </listitem>
  <listitem>
    <para>
On operating systems that support processing forking, font
servers might choose to fork so that the child can continue
processing the existing connections and the parent can accept
new connections.  Such implementations are encouraged to use
shared memory so that in-memory font databases can be shared.
    </para>
  </listitem>
  <listitem>
    <para>
On operating systems that support passing stream file descriptors
between processes, cooperating font servers could collect
connections in a single process when there are few connections
and spread them among several processes as the load increases.
    </para>
  </listitem>
  <listitem>
    <para>
If a font client is unable to connect to a server (as opposed
to having the connection terminated), it should retry for an
implementation-dependent length of time (see Xlib's
handling of ECONNREFUSED in XConnDis.c).
    </para>
  </listitem>
</itemizedlist>
</appendix>
<index id="index" />
</book>
