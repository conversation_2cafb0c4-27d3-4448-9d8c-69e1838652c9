<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE book PUBLIC "-//OASIS//DTD DocBook XML V4.3//EN"
                   "http://www.oasis-open.org/docbook/xml/4.3/docbookx.dtd"
[
<!ENTITY % defs SYSTEM "defs.ent"> %defs;
]>


<book id="evi">

<bookinfo>
   <title>Extended Visual Information Extension</title>
   <subtitle>X Project Team Standard</subtitle>
   <authorgroup>
      <author>
         <firstname>Peter</firstname><surname><PERSON><PERSON><PERSON></surname>
         <affiliation><orgname>Silicon Graphics, Inc.</orgname></affiliation>
      </author>
   </authorgroup>
   <releaseinfo>X Version 11, Release &fullrelvers;</releaseinfo>
   <releaseinfo>Version 1.0</releaseinfo>
   <copyright><year>1986-1997</year><holder>The Open Group</holder></copyright>

<legalnotice>

<para>
Permission is hereby granted, free of charge, to any person obtaining a
copy of this
software and associated documentation files (the Software), to use the
Software without restriction, including, without limitation, the rights to
copy, modify, merge, publish, distribute and sublicense the Software,
to make, have made, license and distribute derivative works thereof, and
to permit persons to whom the Software is
furnished to do so, subject to the following conditions:
</para>

<para>
The above copyright notice and the following permission notice shall be
included in all copies of the Software:
</para>

<para>
THE SOFTWARE IS PROVIDED &ldquo;AS IS&rdquo;, WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, THE WARRANTIES
OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-
INFRINGEMENT.  IN NO EVENT SHALL THE OPEN GROUP BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER USEABILITIY, WHETHER IN AN ACTION OF
CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF, OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OF OTHER DEALINGS IN
THE SOFTWARE.
</para>

<para>
Except as contained in this notice, the name of The Open Group shall not
be used in advertising or otherwise to promote the use or other dealings
in this Software without prior written authorization from The Open Group.
</para>

<para>
X Window System is a trademark of The Open Group.
</para>

</legalnotice>
</bookinfo>

<chapter id="Introduction">
<title>Introduction</title>
<para>
EVI (Extended Visual Information extension) allows a client to determine
information about core X visuals beyond what the core protocol provides.
</para>
</chapter>

<chapter id="Goals">
<title>Goals</title>
<para>
As the X Window System has evolved, it has become clear that the information
returned by the core X protocol regarding Visuals is often insufficient for a
client to determine which is the most appropriate visual for its needs. This
extension allows clients to query the X server for additional visual
information, specifically as regards colormaps and framebuffer levels.
</para>

<para>
This extension is meant to address the needs of pure X clients only. It is
specifically and purposefully not designed to address the needs of X
extensions. Extensions that have an impact on visual information should provide
their own mechanisms for delivering that information. For example, the Double
Buffering Extension (DBE) provides its own mechanism for determining which
visuals support double-buffering.
</para>
</chapter>

<chapter id="Requests">
<title>Requests</title>
<para>
<function>GetVersion</function>
</para>

<informaltable frame="none">
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth="1.0*"/>
  <tbody>
    <row>
      <entry>
<emphasis remap='I'>client_major_version</emphasis>: CARD8
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>client_minor_version</emphasis>: CARD8
      </entry>
    </row>
    <row>
      <entry>
=&gt;
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>server_major_version</emphasis>: CARD8
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>server_minor_version</emphasis>: CARD8
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>



<para>
If supplied, the client_major_version and client_minor_version indicate
what version of the protocol the client wants the server to implement.
The server version numbers returned indicate the protocol this extension
actually supports. This might not equal the version sent by the client.
An implementation can (but need not) support more than one version
simultaneously. The server_major_version and the server_minor_version
are a mechanism to support future revisions of the EVI protocol that
may be necessary. In general, the major version would increment for
incompatible changes, and the minor version would increment for small
upward-compatible changes. Servers that support the protocol defined in
this document will return a server_major_version of one (1), and a
server_minor_version of zero (0).
</para>

<para>
<function>  GetVisualInfo</function>
</para>

<informaltable frame="none">
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth="1.0*"/>
  <tbody>
    <row>
      <entry>
<emphasis remap='I'>visual_list</emphasis>: LISTofVISUALID
      </entry>
    </row>
    <row>
      <entry>
=&gt;
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>per_visual_info</emphasis>: LISTofVISUALINFO
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
where:
</para>

<informaltable frame="none">
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth="1.0*"/>
  <tbody>
    <row>
      <entry>
VISUALINFO: [core_visual_id: VISUALID
      </entry>
    </row>
    <row>
      <entry>
screen: CARD8
      </entry>
    </row>
    <row>
      <entry>
level: INT8
      </entry>
    </row>
    <row>
      <entry>
transparency_type: CARD8
      </entry>
    </row>
    <row>
      <entry>
unused: CARD8
      </entry>
    </row>
    <row>
      <entry>
transparency_value: CARD32
      </entry>
    </row>
    <row>
      <entry>
min_hw_colormaps: CARD8
      </entry>
    </row>
    <row>
      <entry>
max_hw_colormaps: CARD8
      </entry>
    </row>
    <row>
      <entry>
num_colormap_conflicts: CARD16
      </entry>
    </row>
    <row>
      <entry>
colormap_conflicts: LISTofVISUALID]
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<itemizedlist>
  <listitem>
    <para>
level is 0 for normal planes, &gt; 0 for overlays, &lt; 0 for underlays.
    </para>
  </listitem>
  <listitem>
    <para>
transparency_type is 0 for none, 1 for transparent pixel, 2 for
transparent mask.
    </para>
  </listitem>
  <listitem>
    <para>
transparency_value: value to get transparent pixel if transparency
supported.
    </para>
  </listitem>
  <listitem>
    <para>
min_hw_colormaps: minimum number of hardware colormaps backing up the
visual.
    </para>
  </listitem>
  <listitem>
    <para>
max_hw_colormaps: maximum number of hardware colormaps backing up the
visual.
    </para>
    <para>
    (architectures with static colormap allocation/reallocation would have min
= max)
    </para>
  </listitem>
  <listitem>
    <para>
num_colormap_conflicts: number of elements in colormap_conflicts.
    </para>
  </listitem>
  <listitem>
    <para>
colormap_conflicts: list of visuals that may conflict with this one. For
example, if a 12-bit colormap is overloaded to support 8-bit visuals, the
8-bit visuals would conflict with the 12-bit visuals.
    </para>
  </listitem>
</itemizedlist>

</chapter>
<chapter id="Events_and_Errors">
<title>Events and Errors</title>
<para>
No new events or errors are defined by this extension.
</para>
</chapter>

<chapter id='Changes_to_existing_protocol'>
<title>Changes to existing protocol.</title>
<para>
None.
</para>
</chapter>

<chapter id="Encoding">
<title>Encoding</title>
<para>
The name of this extension is "Extended-Visual-Information".
</para>

<para>
The conventions used here are the same as those for the core X11
Protocol Encoding.
</para>

<literallayout class="monospaced">
<function>GetVersion</function>
     1     CARD8               opcode
     1     0                   EVI opcode
     2     2                   request length
     2     CARD16              client_major_version
     2     CARD16              client_minor_version
=&gt;
     1     1                   reply
     1                         unused
     2     CARD16              sequence number
     4     0                   length
     2     CARD16              server_major_version
     2     CARD16              server_minor_version
     20                        unused
</literallayout>

<literallayout class="monospaced">
<function>GetVisualInfo</function>
     1     CARD8               opcode
     1     1                   EVI opcode
     2     2+n                 request length
     4     CARD32              n_visual
     4n    CARD32              visual_ids
=&gt;
     1     1                   reply
     1                         unused
     2     CARD16              sequence number
     4     n                   length
     4     CARD32              n_info
     4     CARD32              n_conflicts
     16                        unused
     16n   LISTofVISUALINFO    items
</literallayout>

<literallayout class="monospaced">
VISUALINFO
     4     VisualID            core_visual_id
     1     INT8                screen
     1     INT8                level
     1     CARD8               transparency_type
     1     CARD8               unused
     4     CARD32              transparency_value
     1     CARD8               min_hw_colormaps
     1     CARD8               max_hw_colormaps
     2     CARD16              num_colormap_conflicts
</literallayout>
</chapter>

<chapter id="C_Language_Binding">
<title>C Language Binding</title>
<para>
<!-- .LP -->
The C functions provide direct access to the protocol and add no additional
semantics.  For complete details on the effects of these functions, refer
to the appropriate protocol request, which can be derived by deleting Xevi
at the start of the function. All functions that have return type Status
will return nonzero for success and zero for failure.
</para>

<para>
The include file for this extension is:
<function>&lt; X11/extensions/XEVI.h&gt;</function>.
</para>

<funcsynopsis id='XeviQueryVersion'>
<funcprototype>
  <funcdef>Bool <function> XeviQueryVersion</function></funcdef>
  <paramdef>Display<parameter> *display</parameter></paramdef>
  <paramdef>int<parameter> *major_version_return</parameter></paramdef>
  <paramdef>int<parameter> *minor_version_return</parameter></paramdef>
</funcprototype>
</funcsynopsis>

<variablelist>
  <varlistentry>
    <term>
      <emphasis remap='I'>display</emphasis>
    </term>
    <listitem>
      <para>
Specifies the connection to the X server.
      </para>
    </listitem>
  </varlistentry>
  <varlistentry>
    <term>
      <emphasis remap='I'>major_version_return</emphasis>
    </term>
    <listitem>
      <para>
Returns the major version supported by the server.
      </para>
    </listitem>
  </varlistentry>
  <varlistentry>
    <term>
      <emphasis remap='I'>minor_version_return</emphasis>
    </term>
    <listitem>
      <para>
Returns the minor version supported by the server.
    </para>
  </listitem>
  </varlistentry>
</variablelist>

<para>
XeviQueryVersion sets major_version_return and minor_version_return to
the major and minor EVI protocol version supported by the server.  If
the EVI library is compatible with the version returned by the server,
it returns nonzero.  If dpy does not support the EVI extension, or if
there was an error during communication with the server, or if the server
and library protocol versions are incompatible, it returns zero.  No other
Xevi functions may be called before this function. If a client violates
this rule, the effects of all subsequent Xevi calls that it makes are
undefined.
</para>

<para>
To get the extended information for any subset of visuals use
XeviGetVisualInfo.
</para>

<funcsynopsis id='XeviGetVisualInfo'>
<funcprototype>
  <funcdef>int <function> XeviGetVisualInfo</function></funcdef>
  <paramdef>Display<parameter> *display</parameter></paramdef>
  <paramdef>VisualID<parameter> *visual</parameter></paramdef>
  <paramdef>int<parameter> n_visual</parameter></paramdef>
  <paramdef>ExtendedVisualInfo<parameter> **evi_return</parameter></paramdef>
  <paramdef>int<parameter> *n_info_return</parameter></paramdef>
</funcprototype>
</funcsynopsis>

<variablelist>
  <varlistentry>
    <term>
      <emphasis remap='I'>display</emphasis>
    </term>
    <listitem>
      <para>
Specifies the connection to the X server.
      </para>
    </listitem>
  </varlistentry>
  <varlistentry>
    <term>
      <emphasis remap='I'>visual</emphasis>
    </term>
    <listitem>
      <para>
If NULL, then information for all visuals of all
screens is returned. Otherwise, a pointer to a list of visuals for which
extended visual information is desired.
      </para>
    </listitem>
  </varlistentry>
  <varlistentry>
    <term>
      <emphasis remap='I'>n_visual</emphasis>
    </term>
    <listitem>
      <para>
If 0, then information for all visuals of all screens is returned. Otherwise,
the number of elements in the array <emphasis remap='I'>visual</emphasis>.
      </para>
    </listitem>
  </varlistentry>
  <varlistentry>
    <term>
      <emphasis remap='I'>evi_return</emphasis>
    </term>
    <listitem>
      <para>
Returns a pointer to a list of <emphasis remap='I'>ExtendedVisualInfo</emphasis>. When done, the client
should free the list using <emphasis remap='I'>XFree</emphasis>.
      </para>
    </listitem>
  </varlistentry>
  <varlistentry>
    <term>
      <emphasis remap='I'>n_info_return</emphasis>
    </term>
    <listitem>
      <para>
Returns the number of elements in the list of
<emphasis remap='I'>ExtendedVisualInfo</emphasis>.
    </para>
  </listitem>
  </varlistentry>
</variablelist>

<para>
XeviGetVisualInfo returns a list of ExtendedVisualInfo structures that describe
visual information beyond that supported by the core protocol. This includes
layer information relevant for systems supporting overlays and/or underlay
planes, and information that allows applications better to determine the level
of hardware support for multiple colormaps. XeviGetVisualInfo returns Success
if successful, or an X error otherwise.
</para>

</chapter>
</book>
