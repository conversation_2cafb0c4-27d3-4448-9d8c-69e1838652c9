# SPDX-License-Identifier: 0BSD
# Author: <PERSON><PERSON>

prefix=/data1/home/<USER>/EQACL/finetune/.conda
exec_prefix=${prefix}
libdir=${exec_prefix}/lib
includedir=${prefix}/include

Name: liblzma
Description: General purpose data compression library
URL: https://tukaani.org/xz/
Version: 5.6.4
Cflags: -I${includedir}
Cflags.private: -DLZMA_API_STATIC
Libs: -L${libdir} -llzma
Libs.private: -pthread 
