<part id="sect1-9">
<preface id="acknowledgements">
<title>Acknowledgements</title>
<para>
The primary contributors to the X11 protocol are:
</para>

<itemizedlist>
<listitem><para><PERSON> (Digital HPW)</para></listitem>
<listitem><para><PERSON><PERSON><PERSON> (Digital HPW)</para></listitem>
<listitem><para><PERSON> (MIT/Project Athena, Digital)</para></listitem>
<listitem><para><PERSON> (Digital WSL)</para></listitem>
<listitem><para><PERSON> (Digital SSG)</para></listitem>
<listitem><para><PERSON> (Digital UEG)</para></listitem>
<listitem><para><PERSON> (Sun)</para></listitem>
<listitem><para><PERSON> (Digital UEG)</para></listitem>
</itemizedlist>

<para>
The implementors of initial server who provided useful
input are:
</para>

<itemizedlist>
<listitem><para><PERSON> (Digital)</para></listitem>
<listitem><para><PERSON> (Digital)</para></listitem>
<listitem><para><PERSON> (Digital)</para></listitem>
</itemizedlist>

<para>
The invited reviewers who provided useful input are:
</para>

<itemizedlist>
<listitem><para><PERSON>erenson (Berkeley)</para></listitem>
<listitem><para>Burns Fisher (Digital)</para></listitem>
<listitem><para>Dan Garfinkel (HP)</para></listitem>
<listitem><para>Leo Hourvitz (Next)</para></listitem>
<listitem><para>Brock Krizan (HP)</para></listitem>
<listitem><para>David Laidlaw (Stellar)</para></listitem>
<listitem><para>Dave Mellinger (Interleaf)</para></listitem>
<listitem><para>Ron Newman (MIT)</para></listitem>
<listitem><para>John Ousterhout (Berkeley)</para></listitem>
<listitem><para>Andrew Palay (ITC CMU)</para></listitem>
<listitem><para>Ralph Swick (MIT)</para></listitem>
<listitem><para>Craig Taylor (Sun)</para></listitem>
<listitem><para>Jeffery Vroom (Stellar)</para></listitem>
</itemizedlist>

<para>
Thanks go to Al Mento of Digital's UEG Documentation Group for
formatting this document.
</para>

<para>
This document does not attempt to provide the rationale or pragmatics required
to fully understand the protocol or to place it in perspective within a
complete system.
</para>

<para>
The protocol contains many management mechanisms that are not intended for
normal applications.
Not all mechanisms are needed to build a particular user interface.
It is important to keep in mind that the protocol is intended to
provide mechanism, not policy.
</para>

<para>
Robert W. Scheifler
</para>
<para>
X Consortium, Inc.
</para>
</preface>

<chapter id='Protocol_Formats'>
  <title>Protocol Formats</title>

  <section id="request_format">
    <title>Request Format</title>
    <indexterm zone="request_format" significance="preferred"><primary>Request</primary><secondary>format</secondary></indexterm>
    <para>
Every request contains an 8-bit <firstterm id="major_opcode">major opcode</firstterm>
<indexterm zone="major_opcode" significance="preferred"><primary>Opcode</primary><secondary>major</secondary></indexterm>
and a 16-bit <firstterm id="request_length">length field</firstterm>
<indexterm zone="request_length" significance="preferred"><primary>Request</primary><secondary>length</secondary></indexterm>
expressed in units of four bytes.
Every request consists of four bytes of a header
(containing the major opcode, the length field, and a data byte)
followed by zero or more additional bytes of data.
The length field defines the total length of the request, including the header.
The length field in a request must equal the minimum length required to contain
the request.
If the specified length is smaller or larger than the required length,
an error is generated.
Unused bytes in a request are not required to be zero.
Major opcodes 128 through 255 are reserved for 
<firstterm id="extension">extensions</firstterm>.
<indexterm zone="extension"><primary>Extension</primary></indexterm>
Extensions are intended to contain multiple requests,
so extension requests typically have an additional 
<firstterm id="minor_opcode">minor opcode</firstterm> encoded
in the second data byte in the request header.
<indexterm zone="minor_opcode" significance="preferred"><primary>Opcode</primary><secondary>minor</secondary></indexterm>
However, the placement and interpretation of this minor opcode and of all
other fields in extension requests are not defined by the core protocol.
Every request on a given connection is implicitly assigned a
<firstterm id="sequence_number">sequence number</firstterm>,
<indexterm zone="sequence_number" significance="preferred"><primary>Sequence number</primary></indexterm>
starting with one, that is used in replies, errors, and events.
    </para>
  </section>

  <section id="reply_format">
    <title>Reply Format</title>
    <indexterm zone="reply_format" significance="preferred"><primary>Reply</primary><secondary>format</secondary></indexterm>
    <para>
Every <firstterm>reply</firstterm> contains a 32-bit length field
expressed in units of four bytes.
Every reply consists of 32 bytes followed by zero or more additional bytes of
data, as specified in the length field.
Unused bytes within a reply are not guaranteed to be zero.
Every reply also contains the least significant 16 bits of the sequence number
of the corresponding request.
    </para>
  </section>

  <section id="error_format">
    <title>Error Format</title>
    <indexterm zone="error_format" significance="preferred"><primary>Error report</primary><secondary>format</secondary></indexterm>
    <para>
Error reports are 32 bytes long.
Every error includes an 8-bit error code.
<phrase id="errors:extensions">
Error codes 128 through 255 are reserved for extensions.
</phrase>
<indexterm zone="errors:extensions" significance="preferred"><primary>Error Codes</primary><secondary>extensions</secondary></indexterm>
<indexterm zone="errors:extensions" significance="preferred"><primary>Extension</primary><secondary>error codes</secondary></indexterm>
Every error also includes the major and minor opcodes of the failed request
and the least significant 16 bits of the sequence number of the request.
For the following errors (see <link linkend='Errors'>section 4</link>),
the failing resource ID is also returned:
<emphasis role='bold'>Colormap</emphasis>,
<emphasis role='bold'>Cursor</emphasis>,
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>Font</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>IDChoice</emphasis>,
<emphasis role='bold'>Pixmap</emphasis>
and
<emphasis role='bold'>Window</emphasis>.
For
<emphasis role='bold'>Atom</emphasis>
errors, the failing atom is returned.
For
<emphasis role='bold'>Value</emphasis>
errors, the failing value is returned.
Other core errors return no additional data.
Unused bytes within an error are not guaranteed to be zero.
    </para>
  </section>

  <section id="event_format">
    <title>Event Format</title>
    <indexterm zone="event_format" significance="preferred"><primary>Event</primary><secondary>format</secondary></indexterm>
    <para>
<firstterm>Events</firstterm> are 32 bytes long.
Unused bytes within an event are not guaranteed to be zero.
Every event contains an 8-bit type code.
The most significant bit in this code is set if the event was generated from a
<link linkend="requests:SendEvent"><emphasis role='bold'>SendEvent</emphasis></link>
request.
<phrase id="events:extensions">
Event codes 64 through 127 are reserved for extensions, although the core
protocol does not define a mechanism for selecting interest in such events.
</phrase>
<indexterm zone="events:extensions" significance="preferred"><primary>Event</primary><secondary>extension</secondary></indexterm>
<indexterm zone="events:extensions" significance="preferred"><primary>Extension</primary><secondary>event</secondary></indexterm>
Every core event (with the exception of
<link linkend="events:KeymapNotify"><emphasis role='bold'>KeymapNotify</emphasis></link>)
also contains the least significant 16 bits of the sequence number of the last
request issued by the client that was (or is currently being) processed by
the server.
    </para>
  </section>
</chapter>

<chapter id='Syntactic_Conventions'>
<title>Syntactic Conventions</title>
<!-- .XS -->
<!-- (SN Syntactic Conventions -->
<!-- .XE -->
<para>
<!-- .LP -->
The rest of this document uses the following syntactic conventions.
</para>
<itemizedlist>
  <listitem>
    <para>
The syntax {...} encloses a set of alternatives.
    </para>
  </listitem>
  <listitem>
    <para>
The syntax [...] encloses a set of structure components.
    </para>
  </listitem>
  <listitem>
    <para>
In general, TYPEs are in uppercase and
<emphasis role='bold'>AlternativeValues</emphasis>
are capitalized.
    </para>
  </listitem>
  <listitem>
    <para>
Requests in <link linkend='Requests'>section 9</link> are described
in the following format:
<literallayout class="monospaced">
<emphasis role='bold'>RequestName</emphasis>
     <emphasis remap='I'>arg1</emphasis>: type1
     ...
     <emphasis remap='I'>argN</emphasis>: typeN
   ▶
     result1: type1
     ...
     resultM: typeM

     Errors: kind1, ..., kindK

     Description.
</literallayout>
    </para>
    <para>
If no ▶ is present in the description,
then the request has no reply (it is asynchronous),
although errors may still be reported.
If ▶+ is used,
then one or more replies can be generated for a single request.
    </para>
  </listitem>
  <listitem>
    <para>
Events in <link linkend='Events'>section 11</link> are described
in the following format:
<literallayout class="monospaced">
<emphasis role='bold'>EventName</emphasis>
     <emphasis remap='I'>value1</emphasis>: type1
     ...
     <emphasis remap='I'>valueN</emphasis>: typeN

     Description.
</literallayout>
    </para>
  </listitem>
</itemizedlist>
</chapter>

<chapter id='Common_Types'>
<title>Common Types</title>
<!-- .XS -->
<!-- (SN Common Types -->
<!-- .XE -->
<informaltable frame="topbot">
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='2.0*'/>
  <thead>
    <row rowsep='1'>
      <entry>Name</entry>
      <entry>Value</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry id="types:LISTofFOO">LISTofFOO<indexterm zone="types:LISTofFOO" significance="preferred"><primary>Types</primary><secondary>LISTofFOO</secondary></indexterm></entry>
      <entry>

A type name of the form LISTofFOO means a counted list of elements of type
FOO.
The size of the length field may vary (it is not necessarily the same
size as a FOO), and in some cases, it may be implicit.
It is fully specified in Appendix B.
Except where explicitly noted,
zero-length lists are legal.
      </entry>
    </row>
    <row>
      <entry><para id="types:BITMASK">BITMASK<indexterm zone="types:BITMASK" significance="preferred"><primary>Types</primary><secondary>BITMASK</secondary></indexterm></para><para id="types:LISTofVALUE">LISTofVALUE<indexterm zone="types:LISTofVALUE" significance="preferred"><primary>Types</primary><secondary>LISTofVALUE</secondary></indexterm></para></entry>
      <entry>
<para>
The types BITMASK and LISTofVALUE are somewhat special.
Various requests contain arguments of the form:
</para>
<para>
<emphasis remap='I'>value-mask</emphasis>: BITMASK
</para>
<para>
<emphasis remap='I'>value-list</emphasis>: LISTofVALUE
</para>
<para>
These are used to allow the client to specify a subset of a heterogeneous
collection of optional arguments.
The value-mask specifies which arguments are to be provided;
each such argument is assigned a unique bit position.
The representation of the BITMASK will typically contain more bits than
there are defined arguments.
The unused bits in the value-mask must be zero (or the server generates a
<emphasis role='bold'>Value</emphasis>
error).
The value-list contains one value for each bit set to 1 in the mask,
from least significant to most significant bit in the mask.
Each value is represented with four bytes,
but the actual value occupies only the least significant bytes as required.
The values of the unused bytes do not matter.
</para>
      </entry>
    </row>
    <row>
      <entry id="types:OR">OR<indexterm zone="types:OR" significance="preferred"><primary>Types</primary><secondary>OR</secondary></indexterm></entry>
      <entry>
A type of the form "T1 or ... or Tn" means the union of the indicated types.
A single-element type is given as the element without enclosing braces.
      </entry>
    </row>
    <row>
      <entry id="types:WINDOW">WINDOW<indexterm zone="types:WINDOW" significance="preferred"><primary>Types</primary><secondary>WINDOW</secondary></indexterm></entry>
      <entry>32-bit value (top three bits guaranteed to be zero)</entry>
    </row>
    <row>
      <entry id="types:PIXMAP">PIXMAP<indexterm zone="types:PIXMAP" significance="preferred"><primary>Types</primary><secondary>PIXMAP</secondary></indexterm></entry>
      <entry>32-bit value (top three bits guaranteed to be zero)</entry>
    </row>
    <row>
      <entry id="types:CURSOR">CURSOR<indexterm zone="types:CURSOR" significance="preferred"><primary>Types</primary><secondary>CURSOR</secondary></indexterm></entry>
      <entry>32-bit value (top three bits guaranteed to be zero)</entry>
    </row>
    <row>
      <entry id="types:FONT">FONT<indexterm zone="types:FONT" significance="preferred"><primary>Types</primary><secondary>FONT</secondary></indexterm></entry>
      <entry>32-bit value (top three bits guaranteed to be zero)</entry>
    </row>
    <row>
      <entry id="types:GCONTEXT">GCONTEXT<indexterm zone="types:GCONTEXT" significance="preferred"><primary>Types</primary><secondary>GCONTEXT</secondary></indexterm></entry>
      <entry>32-bit value (top three bits guaranteed to be zero)</entry>
    </row>
    <row>
      <entry id="types:COLORMAP">COLORMAP<indexterm zone="types:COLORMAP" significance="preferred"><primary>Types</primary><secondary>COLORMAP</secondary></indexterm></entry>
      <entry>32-bit value (top three bits guaranteed to be zero)</entry>
    </row>
    <row>
      <entry id="types:DRAWABLE">DRAWABLE<indexterm zone="types:DRAWABLE" significance="preferred"><primary>Types</primary><secondary>DRAWABLE</secondary></indexterm></entry>
      <entry>WINDOW or PIXMAP</entry>
    </row>
    <row>
      <entry id="types:FONTABLE">FONTABLE<indexterm zone="types:FONTABLE" significance="preferred"><primary>Types</primary><secondary>FONTABLE</secondary></indexterm></entry>
      <entry>FONT or GCONTEXT</entry>
    </row>
    <row>
      <entry id="types:ATOM">ATOM<indexterm zone="types:ATOM" significance="preferred"><primary>Types</primary><secondary>ATOM</secondary></indexterm></entry>
      <entry>32-bit value (top three bits guaranteed to be zero)</entry>
    </row>
    <row>
      <entry id="types:VISUALID">VISUALID<indexterm zone="types:VISUALID" significance="preferred"><primary>Types</primary><secondary>VISUALID</secondary></indexterm></entry>
      <entry>32-bit value (top three bits guaranteed to be zero)</entry>
    </row>
    <row>
      <entry id="types:VALUE">VALUE<indexterm zone="types:VALUE" significance="preferred"><primary>Types</primary><secondary>VALUE</secondary></indexterm></entry>
      <entry>32-bit quantity (used only in LISTofVALUE)</entry>
    </row>
    <row>
      <entry id="types:BYTE">BYTE<indexterm zone="types:BYTE" significance="preferred"><primary>Types</primary><secondary>BYTE</secondary></indexterm></entry>
      <entry>8-bit value</entry>
    </row>
    <row>
      <entry id="types:INT8">INT8<indexterm zone="types:INT8" significance="preferred"><primary>Types</primary><secondary>INT8</secondary></indexterm></entry>
      <entry>8-bit signed integer</entry>
    </row>
    <row>
      <entry id="types:INT16">INT16<indexterm zone="types:INT16" significance="preferred"><primary>Types</primary><secondary>INT16</secondary></indexterm></entry>
      <entry>16-bit signed integer</entry>
    </row>
    <row>
      <entry id="types:INT32">INT32<indexterm zone="types:INT32" significance="preferred"><primary>Types</primary><secondary>INT32</secondary></indexterm></entry>
      <entry>32-bit signed integer</entry>
    </row>
    <row>
      <entry id="types:CARD8">CARD8<indexterm zone="types:CARD8" significance="preferred"><primary>Types</primary><secondary>CARD8</secondary></indexterm></entry>
      <entry>8-bit unsigned integer</entry>
    </row>
    <row>
      <entry id="types:CARD16">CARD16<indexterm zone="types:CARD16" significance="preferred"><primary>Types</primary><secondary>CARD16</secondary></indexterm></entry>
      <entry>16-bit unsigned integer</entry>
    </row>
    <row>
      <entry id="types:CARD32">CARD32<indexterm zone="types:CARD32" significance="preferred"><primary>Types</primary><secondary>CARD32</secondary></indexterm></entry>
      <entry>32-bit unsigned integer</entry>
    </row>
    <row>
      <entry id="types:TIMESTAMP">TIMESTAMP<indexterm zone="types:TIMESTAMP" significance="preferred"><primary>Types</primary><secondary>TIMESTAMP</secondary></indexterm></entry>
      <entry>CARD32</entry>
    </row>
    <row>
      <entry id="types:BITGRAVITY">BITGRAVITY<indexterm zone="types:BITGRAVITY" significance="preferred"><primary>Types</primary><secondary>BITGRAVITY</secondary></indexterm></entry>
      <entry>
{ <emphasis role='bold'>Forget</emphasis>,
<emphasis role='bold'>Static</emphasis>,
<emphasis role='bold'>NorthWest</emphasis>,
<emphasis role='bold'>North</emphasis>,
<emphasis role='bold'>NorthEast</emphasis>,
<emphasis role='bold'>West</emphasis>,
<emphasis role='bold'>Center</emphasis>,
<emphasis role='bold'>East</emphasis>,
<emphasis role='bold'>SouthWest</emphasis>,
<emphasis role='bold'>South</emphasis>,
<emphasis role='bold'>SouthEast</emphasis> }
      </entry>
    </row>
    <row>
      <entry id="types:WINGRAVITY">WINGRAVITY<indexterm zone="types:WINGRAVITY" significance="preferred"><primary>Types</primary><secondary>WINGRAVITY</secondary></indexterm></entry>
      <entry>
{ <emphasis role='bold'>Unmap</emphasis>,
<emphasis role='bold'>Static</emphasis>,
<emphasis role='bold'>NorthWest</emphasis>,
<emphasis role='bold'>North</emphasis>,
<emphasis role='bold'>NorthEast</emphasis>,
<emphasis role='bold'>West</emphasis>,
<emphasis role='bold'>Center</emphasis>,
<emphasis role='bold'>East</emphasis>,
<emphasis role='bold'>SouthWest</emphasis>,
<emphasis role='bold'>South</emphasis>,
<emphasis role='bold'>SouthEast</emphasis> }
      </entry>
    </row>
    <row>
      <entry id="types:BOOL">BOOL<indexterm zone="types:BOOL" significance="preferred"><primary>Types</primary><secondary>BOOL</secondary></indexterm></entry>
      <entry>
{ <emphasis role='bold'>True</emphasis>,
<emphasis role='bold'>False</emphasis> }
      </entry>
    </row>
    <row>
      <entry id="types:EVENT">EVENT<indexterm zone="types:EVENT" significance="preferred"><primary>Types</primary><secondary>EVENT</secondary></indexterm></entry>
      <entry>
{ <emphasis role='bold'>KeyPress</emphasis>,
<emphasis role='bold'>KeyRelease</emphasis>,
<emphasis role='bold'>OwnerGrabButton</emphasis>,
<emphasis role='bold'>ButtonPress</emphasis>,
<emphasis role='bold'>ButtonRelease</emphasis>,
<emphasis role='bold'>EnterWindow</emphasis>,
<emphasis role='bold'>LeaveWindow</emphasis>,
<emphasis role='bold'>PointerMotion</emphasis>,
<emphasis role='bold'>PointerMotionHint</emphasis>,
<emphasis role='bold'>Button1Motion</emphasis>,
<emphasis role='bold'>Button2Motion</emphasis>,
<emphasis role='bold'>Button3Motion</emphasis>,
<emphasis role='bold'>Button4Motion</emphasis>,
<emphasis role='bold'>Button5Motion</emphasis>,
<emphasis role='bold'>ButtonMotion</emphasis>,
<emphasis role='bold'>Exposure</emphasis>,
<emphasis role='bold'>VisibilityChange</emphasis>,
<emphasis role='bold'>StructureNotify</emphasis>,
<emphasis role='bold'>ResizeRedirect</emphasis>,
<emphasis role='bold'>SubstructureNotify</emphasis>,
<emphasis role='bold'>SubstructureRedirect</emphasis>,
<emphasis role='bold'>FocusChange</emphasis>,
<emphasis role='bold'>PropertyChange</emphasis>,
<emphasis role='bold'>ColormapChange</emphasis>,
<emphasis role='bold'>KeymapState</emphasis> }
      </entry>
    </row>
    <row>
      <entry id="types:POINTEREVENT">POINTEREVENT<indexterm zone="types:POINTEREVENT" significance="preferred"><primary>Types</primary><secondary>POINTEREVENT</secondary></indexterm></entry>
      <entry>
{ <emphasis role='bold'>ButtonPress</emphasis>,
<emphasis role='bold'>ButtonRelease</emphasis>,
<emphasis role='bold'>EnterWindow</emphasis>,
<emphasis role='bold'>LeaveWindow</emphasis>,
<emphasis role='bold'>PointerMotion</emphasis>,
<emphasis role='bold'>PointerMotionHint</emphasis>,
<emphasis role='bold'>Button1Motion</emphasis>,
<emphasis role='bold'>Button2Motion</emphasis>,
<emphasis role='bold'>Button3Motion</emphasis>,
<emphasis role='bold'>Button4Motion</emphasis>,
<emphasis role='bold'>Button5Motion</emphasis>,
<emphasis role='bold'>ButtonMotion</emphasis>,
<emphasis role='bold'>KeymapState</emphasis> }
      </entry>
    </row>
    <row>
      <entry id="types:DEVICEEVENT">DEVICEEVENT<indexterm zone="types:DEVICEEVENT" significance="preferred"><primary>Types</primary><secondary>DEVICEEVENT</secondary></indexterm></entry>
      <entry>
{ <emphasis role='bold'>KeyPress</emphasis>,
<emphasis role='bold'>KeyRelease</emphasis>,
<emphasis role='bold'>ButtonPress</emphasis>,
<emphasis role='bold'>ButtonRelease</emphasis>,
<emphasis role='bold'>PointerMotion</emphasis>,
<emphasis role='bold'>Button1Motion</emphasis>,
<emphasis role='bold'>Button2Motion</emphasis>,
<emphasis role='bold'>Button3Motion</emphasis>,
<emphasis role='bold'>Button4Motion</emphasis>,
<emphasis role='bold'>Button5Motion</emphasis>,
<emphasis role='bold'>ButtonMotion</emphasis> }
      </entry>
    </row>
    <row>
      <entry id="types:KEYSYM">KEYSYM<indexterm zone="types:KEYSYM" significance="preferred"><primary>Types</primary><secondary>KEYSYM</secondary></indexterm></entry>
      <entry>32-bit value (top three bits guaranteed to be zero)</entry>
    </row>
    <row>
      <entry id="types:KEYCODE">KEYCODE<indexterm zone="types:KEYCODE" significance="preferred"><primary>Types</primary><secondary>KEYCODE</secondary></indexterm></entry>
      <entry>CARD8</entry>
    </row>
    <row>
      <entry id="types:BUTTON">BUTTON<indexterm zone="types:BUTTON" significance="preferred"><primary>Types</primary><secondary>BUTTON</secondary></indexterm></entry>
      <entry>CARD8</entry>
    </row>
    <row>
      <entry id="types:KEYMASK">KEYMASK<indexterm zone="types:KEYMASK" significance="preferred"><primary>Types</primary><secondary>KEYMASK</secondary></indexterm></entry>
      <entry>
{ <emphasis role='bold'>Shift</emphasis>,
<emphasis role='bold'>Lock</emphasis>,
<emphasis role='bold'>Control</emphasis>,
<emphasis role='bold'>Mod1</emphasis>,
<emphasis role='bold'>Mod2</emphasis>,
<emphasis role='bold'>Mod3</emphasis>,
<emphasis role='bold'>Mod4</emphasis>,
<emphasis role='bold'>Mod5</emphasis> }
      </entry>
    </row>
    <row>
      <entry id="types:BUTMASK">BUTMASK<indexterm zone="types:BUTMASK" significance="preferred"><primary>Types</primary><secondary>BUTMASK</secondary></indexterm></entry>
      <entry>
{ <emphasis role='bold'>Button1</emphasis>,
<emphasis role='bold'>Button2</emphasis>,
<emphasis role='bold'>Button3</emphasis>,
<emphasis role='bold'>Button4</emphasis>,
<emphasis role='bold'>Button5</emphasis> }
      </entry>
    </row>
    <row>
      <entry id="types:KEYBUTMASK">KEYBUTMASK<indexterm zone="types:KEYBUTMASK" significance="preferred"><primary>Types</primary><secondary>KEYBUTMASK</secondary></indexterm></entry>
      <entry>KEYMASK or BUTMASK</entry>
    </row>
    <row>
      <entry id="types:STRING8">STRING8<indexterm zone="types:STRING8" significance="preferred"><primary>Types</primary><secondary>STRING8</secondary></indexterm></entry>
      <entry>LISTofCARD8</entry>
    </row>
    <row>
      <entry id="types:STRING16">STRING16<indexterm zone="types:STRING16" significance="preferred"><primary>Types</primary><secondary>STRING16</secondary></indexterm></entry>
      <entry>LISTofCHAR2B</entry>
    </row>
    <row>
      <entry id="types:CHAR2B">CHAR2B<indexterm zone="types:CHAR2B" significance="preferred"><primary>Types</primary><secondary>CHAR2B</secondary></indexterm></entry>
      <entry>[byte1, byte2: CARD8]</entry>
    </row>
    <row>
      <entry id="types:POINT">POINT<indexterm zone="types:POINT" significance="preferred"><primary>Types</primary><secondary>POINT</secondary></indexterm></entry>
      <entry>[x, y: INT16]</entry>
    </row>
    <row>
      <entry id="types:RECTANGLE">RECTANGLE<indexterm zone="types:RECTANGLE" significance="preferred"><primary>Types</primary><secondary>RECTANGLE</secondary></indexterm></entry>
      <entry>
<para>[x, y: INT16,</para>
<para>width, height: CARD16]</para>
      </entry>
    </row>
    <row>
      <entry id="types:ARC">ARC<indexterm zone="types:ARC" significance="preferred"><primary>Types</primary><secondary>ARC</secondary></indexterm></entry>
      <entry>
<para>[x, y: INT16,</para>
<para>width, height: CARD16,</para>
<para>angle1, angle2: INT16]</para>
      </entry>
    </row>
    <row>
      <entry id="types:HOST">HOST<indexterm zone="types:HOST" significance="preferred"><primary>Types</primary><secondary>HOST</secondary></indexterm></entry>
      <entry>
<para>
[family:
{ <emphasis role='bold'>Internet</emphasis>,
<emphasis role='bold'>InternetV6</emphasis>,
<emphasis role='bold'>ServerInterpreted</emphasis>,
<emphasis role='bold'>DECnet</emphasis>,
<emphasis role='bold'>Chaos</emphasis> }
</para>
<para>address: LISTofBYTE]</para>
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
The [x,y] coordinates of a RECTANGLE specify the upper-left corner.
</para>

<para>
The primary interpretation of large characters in a STRING16 is that they
are composed of two bytes used to index a two-dimensional matrix,
hence, the use of CHAR2B rather than CARD16.
This corresponds to the JIS/ISO method of indexing 2-byte characters.
It is expected that most large fonts will be defined with 2-byte
matrix indexing.
For large fonts constructed with linear indexing,
a CHAR2B can be interpreted as a 16-bit number by treating byte1 as
the most significant byte.
This means that clients should always transmit such
16-bit character values most significant byte first, as the server will never
byte-swap CHAR2B quantities.
</para>

<para>
The length, format, and interpretation of a HOST address are specific to the
family (see
<link linkend="requests:ChangeHosts"><emphasis role='bold'>ChangeHosts</emphasis></link>
request).
</para>
</chapter>

<chapter id='Errors'>
<title>Errors</title>
<!-- .XS -->
<!-- (SN Errors -->
<!-- .XE -->
<para>
<!-- .LP -->
In general, when a request terminates with an error,
the request has no side effects (that is, there is no partial execution).
The only requests for which this is not true are
<link linkend="requests:ChangeWindowAttributes"><emphasis role='bold'>ChangeWindowAttributes</emphasis></link>,
<link linkend="requests:ChangeGC"><emphasis role='bold'>ChangeGC</emphasis></link>,
<link linkend="requests:PolyText8"><emphasis role='bold'>PolyText8</emphasis></link>,
<link linkend="requests:PolyText16"><emphasis role='bold'>PolyText16</emphasis></link>,
<link linkend="requests:FreeColors"><emphasis role='bold'>FreeColors</emphasis></link>,
<link linkend="requests:StoreColors"><emphasis role='bold'>StoreColors</emphasis></link>
and
<link linkend="requests:ChangeKeyboardControl"><emphasis role='bold'>ChangeKeyboardControl</emphasis></link>.
</para>

<para>
The following error codes result from various requests as follows:
</para>

<informaltable frame="topbot">
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='2.0*'/>
  <thead>
    <row rowsep='1'>
      <entry>Error</entry>
      <entry>Description</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry id="errors:Access"><emphasis role='bold'>Access</emphasis><indexterm zone="errors:Access" significance="preferred"><primary>Error Codes</primary><secondary>Access</secondary></indexterm></entry>
      <entry>
An attempt is made to grab a key/button combination already grabbed by another
client.
An attempt is made to free a colormap entry not allocated by the client
or to free an entry in a colormap that was created with all entries writable.
An attempt is made to store into a read-only or an unallocated colormap entry.
An attempt is made to modify the access control list from other than the local
host (or otherwise authorized client).
An attempt is made to select an event type that only one client can
select at a time when another client has already selected it.
      </entry>
    </row>
    <row>
      <entry id="errors:Alloc"><emphasis role='bold'>Alloc</emphasis><indexterm zone="errors:Alloc" significance="preferred"><primary>Error Codes</primary><secondary>Alloc</secondary></indexterm></entry>
      <entry>
The server failed to allocate the requested resource.
Note that the explicit listing of
<emphasis role='bold'>Alloc</emphasis>
errors in request only covers allocation errors at a very coarse level
and is not intended to cover all cases
of a server running out of allocation space in the middle of service.
The semantics when a server runs out of allocation space are left unspecified,
but a server may generate an
<emphasis role='bold'>Alloc</emphasis>
error on any request for this reason,
and clients should be prepared to receive such errors and handle
or discard them.
      </entry>
    </row>
    <row>
      <entry id="errors:Atom"><emphasis role='bold'>Atom</emphasis><indexterm zone="errors:Atom" significance="preferred"><primary>Error Codes</primary><secondary>Atom</secondary></indexterm></entry>
      <entry>
A value for an ATOM argument does not name a defined ATOM.
      </entry>
    </row>
    <row>
      <entry id="errors:Colormap"><emphasis role='bold'>Colormap</emphasis><indexterm zone="errors:Colormap" significance="preferred"><primary>Error Codes</primary><secondary>Colormap</secondary></indexterm></entry>
      <entry>
A value for a COLORMAP argument does not name a defined COLORMAP.
      </entry>
    </row>
    <row>
      <entry id="errors:Cursor"><emphasis role='bold'>Cursor</emphasis><indexterm zone="errors:Cursor" significance="preferred"><primary>Error Codes</primary><secondary>Cursor</secondary></indexterm></entry>
      <entry>
A value for a CURSOR argument does not name a defined CURSOR.
      </entry>
    </row>
    <row>
      <entry id="errors:Drawable"><emphasis role='bold'>Drawable</emphasis><indexterm zone="errors:Drawable" significance="preferred"><primary>Error Codes</primary><secondary>Drawable</secondary></indexterm></entry>
      <entry>
A value for a DRAWABLE argument does not name a defined WINDOW
or PIXMAP.
      </entry>
    </row>
    <row>
      <entry id="errors:Font"><emphasis role='bold'>Font</emphasis><indexterm zone="errors:Font" significance="preferred"><primary>Error Codes</primary><secondary>Font</secondary></indexterm></entry>
      <entry>
A value for a FONT argument does not name a defined FONT.
A value for a FONTABLE argument does not name a defined FONT or a
defined GCONTEXT.
      </entry>
    </row>
    <row>
      <entry id="errors:GContext"><emphasis role='bold'>GContext</emphasis><indexterm zone="errors:GContext" significance="preferred"><primary>Error Codes</primary><secondary>GContext</secondary></indexterm></entry>
      <entry>
A value for a GCONTEXT argument does not name a defined GCONTEXT.
      </entry>
    </row>
    <row>
      <entry id="errors:IDChoice"><emphasis role='bold'>IDChoice</emphasis><indexterm zone="errors:IDChoice" significance="preferred"><primary>Error Codes</primary><secondary>IDChoice</secondary></indexterm></entry>
      <entry>
The value chosen for a resource identifier either is not included
in the range assigned to the client or is already in use.
      </entry>
    </row>
    <row>
      <entry id="errors:Implementation"><emphasis role='bold'>Implementation</emphasis><indexterm zone="errors:Implementation" significance="preferred"><primary>Error Codes</primary><secondary>Implementation</secondary></indexterm></entry>
      <entry>
The server does not implement some aspect of the request.
A server that generates this error for a core request is deficient.
As such, this error is not listed for any of the requests,
but clients should be prepared to receive such errors
and handle or discard them.
      </entry>
    </row>
    <row>
      <entry id="errors:Length"><emphasis role='bold'>Length</emphasis><indexterm zone="errors:Length" significance="preferred"><primary>Error Codes</primary><secondary>Length</secondary></indexterm></entry>
      <entry>
The length of a request is shorter or longer than that required
to minimally contain the arguments.
The length of a request exceeds the maximum length accepted by the
server.
      </entry>
    </row>
    <row>
      <entry id="errors:Match"><emphasis role='bold'>Match</emphasis><indexterm zone="errors:Match" significance="preferred"><primary>Error Codes</primary><secondary>Match</secondary></indexterm></entry>
      <entry>
An
<emphasis role='bold'>InputOnly</emphasis>
window is used as a DRAWABLE.
In a graphics request, the GCONTEXT argument does not have the same
root and depth as the destination DRAWABLE argument.
Some argument (or pair of arguments) has the correct type and range,
but it fails to match in some other way required by the request.
      </entry>
    </row>
    <row>
      <entry id="errors:Name"><emphasis role='bold'>Name</emphasis><indexterm zone="errors:Name" significance="preferred"><primary>Error Codes</primary><secondary>Name</secondary></indexterm></entry>
      <entry>
A font or color of the specified name does not exist.
      </entry>
    </row>
    <row>
      <entry id="errors:Pixmap"><emphasis role='bold'>Pixmap</emphasis><indexterm zone="errors:Pixmap" significance="preferred"><primary>Error Codes</primary><secondary>Pixmap</secondary></indexterm></entry>
      <entry>
A value for a PIXMAP argument does not name a defined PIXMAP.
      </entry>
    </row>
    <row>
      <entry id="errors:Request"><emphasis role='bold'>Request</emphasis><indexterm zone="errors:Request" significance="preferred"><primary>Error Codes</primary><secondary>Request</secondary></indexterm></entry>
      <entry>
The major or minor opcode does not specify a valid request.
      </entry>
    </row>
    <row>
      <entry id="errors:Value"><emphasis role='bold'>Value</emphasis><indexterm zone="errors:Value" significance="preferred"><primary>Error Codes</primary><secondary>Value</secondary></indexterm></entry>
      <entry>
Some numeric value falls outside the range of values accepted by the request.
Unless a specific range is specified for an argument,
the full range defined by the argument's type is accepted.
Any argument defined as a set of alternatives typically can generate
this error (due to the encoding).
      </entry>
    </row>
    <row>
      <entry id="errors:Window"><emphasis role='bold'>Window</emphasis><indexterm zone="errors:Window" significance="preferred"><primary>Error Codes</primary><secondary>Window</secondary></indexterm></entry>
      <entry>
A value for a WINDOW argument does not name a defined WINDOW.
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<note><para>
The
<emphasis role='bold'>Atom</emphasis>,
<emphasis role='bold'>Colormap</emphasis>,
<emphasis role='bold'>Cursor</emphasis>,
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>Font</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Pixmap</emphasis>
and
<emphasis role='bold'>Window</emphasis>
errors are also used when the argument type is extended by union with a
set of fixed alternatives, for example, &lt;WINDOW or
<emphasis role='bold'>PointerRoot</emphasis>
or
<emphasis role='bold'>None</emphasis>&gt;.
</para></note>
</chapter>

<chapter id='Keyboards'>
<title>Keyboards</title>
<indexterm zone="Keyboards"><primary>Keyboard</primary></indexterm>
<!-- .XS -->
<!-- (SN Keyboards -->
<!-- .XE -->
<para id="keycode">
A KEYCODE represents a physical (or logical) key.
<indexterm zone="keycode"><primary>Types</primary><secondary>KEYCODE</secondary></indexterm>
<indexterm zone="keycode" significance="preferred"><primary>Keycode</primary></indexterm>
Keycodes lie in the inclusive range [8,255].
A keycode value carries no intrinsic information,
although server implementors may attempt to encode geometry information
(for example, matrix) to be interpreted in a server-dependent fashion.
The mapping between keys and keycodes cannot be changed using the
protocol.
</para>
<para id="keysym">
A KEYSYM is an encoding of a symbol on the cap of a key.
<indexterm zone="keysym"><primary>Types</primary><secondary>KEYSYM</secondary></indexterm>
<indexterm zone="keysym"><primary>Keysym</primary></indexterm>
The set of defined KEYSYMs include the character sets Latin-1, Latin-2,
Latin-3, Latin-4, Kana, Arabic, Cyrillic, Greek, Tech, Special, Publish, APL,
Hebrew, Thai, and Korean as well as a set of symbols common on keyboards
(Return, Help, Tab,
and so on).
KEYSYMs with the most significant bit (of the 29 bits) set are reserved
as vendor-specific.
</para>
<para>
A list of KEYSYMs is associated with each KEYCODE.
The list is intended to convey the set of symbols on the corresponding key.
If the list (ignoring trailing
<keysym>NoSymbol</keysym>
entries) is a single KEYSYM "<emphasis remap='I'>K</emphasis>",
then the list is treated as if it were
the list "<emphasis remap='I'>K</emphasis> <keysym>NoSymbol</keysym>
<emphasis remap='I'>K</emphasis> <keysym>NoSymbol</keysym>".
If the list (ignoring trailing <keysym>NoSymbol</keysym> entries)
is a pair of KEYSYMs
"<emphasis remap='I'>K1 K2</emphasis>", then the list is treated as
if it were the list
"<emphasis remap='I'>K1 K2 K1 K2</emphasis>".
If the list (ignoring trailing
<keysym>NoSymbol</keysym>
entries) is
a triple of KEYSYMs "<emphasis remap='I'>K1 K2 K3</emphasis>",
then the list is treated as if it were the list "
<emphasis remap='I'>K1 K2 K3</emphasis> <keysym>NoSymbol</keysym>".
When an explicit "void" element is desired in the list,
the value
<keysym>VoidSymbol</keysym>
can be used.
</para>
<para>
<!-- .LP -->
The first four elements of the list are split into two groups of KEYSYMs.
Group 1 contains the first and second KEYSYMs, Group 2 contains the third and
fourth KEYSYMs.
Within each group,
if the second element of the group is
<keysym>NoSymbol</keysym>,
then the group should be treated as if the second element were the
same as the first element, except when the first element is an alphabetic
KEYSYM "<emphasis remap='I'>K</emphasis>" for which both lowercase
and uppercase forms are defined.
In that case, the group should be treated as if the first element were the
lowercase form of "<emphasis remap='I'>K</emphasis>" and the second
element were the uppercase form
of "<emphasis remap='I'>K</emphasis>".
</para>

<para id="keysym_from_keypress">
The standard rules for obtaining a KEYSYM from a
<link linkend="events:KeyPress"><emphasis role='bold'>KeyPress</emphasis></link>
<indexterm zone="keysym_from_keypress"><primary>KeyPress</primary></indexterm>
event make use of only the Group 1 and Group 2 KEYSYMs; no interpretation of
other KEYSYMs in the list is defined.  The modifier state determines which
group to use.  Switching between groups is controlled by the KEYSYM named
MODE SWITCH, by attaching that KEYSYM to some KEYCODE and attaching that
KEYCODE to any one of the modifiers
<emphasis role='bold'>Mod1</emphasis>
through
<emphasis role='bold'>Mod5</emphasis>.
This modifier is
<indexterm zone="group_modifier" significance="preferred"><primary>modifier</primary><secondary>group</secondary></indexterm>
called the "<firstterm id="group_modifier">group modifier</firstterm>".  For any KEYCODE, Group 1 is used when the
group modifier is off, and Group 2 is used when the group modifier is on.
</para>

<para id="modifier:lock">
The
<emphasis role='bold'>Lock</emphasis>
<indexterm zone="modifier:lock" significance="preferred"><primary>modifier</primary><secondary>Lock</secondary></indexterm>
modifier is interpreted as CapsLock when the KEYSYM named CAPS
LOCK is attached to some KEYCODE and that KEYCODE is attached to the
<emphasis role='bold'>Lock</emphasis>
modifier.  The
<emphasis role='bold'>Lock</emphasis>
modifier is interpreted as ShiftLock when the KEYSYM
named SHIFT LOCK is attached to some KEYCODE and that KEYCODE is attached
to the
<emphasis role='bold'>Lock</emphasis>
modifier.  If the
<emphasis role='bold'>Lock</emphasis>
modifier could be interpreted as both
CapsLock and ShiftLock, the CapsLock interpretation is used.
</para>

<para id="modifier:numlock">
<!-- .LP -->
The operation of "keypad" keys is controlled by the KEYSYM named NUM LOCK,
by attaching that KEYSYM to some KEYCODE and attaching that KEYCODE to any
one of the modifiers
<emphasis role='bold'>Mod1</emphasis>
through
<emphasis role='bold'>Mod5</emphasis>.
This modifier is called the
<indexterm zone="modifier:lock" significance="preferred"><primary>modifier</primary><secondary>NumLock</secondary></indexterm>
"numlock modifier".  The standard KEYSYMs with the prefix KEYPAD in their
name are called "keypad" KEYSYMs; these are KEYSYMS with numeric value in
the hexadecimal range #xFF80 to #xFFBD inclusive.  In addition,
vendor-specific KEYSYMS in the hexadecimal range #x11000000 to #x1100FFFF
are also keypad KEYSYMs.
</para>
<para>
<!-- .LP -->
Within a group, the choice of KEYSYM is determined by applying the first
rule that is satisfied from the following list:
</para>

<itemizedlist>
  <listitem>
    <para>
The numlock modifier is on and the second KEYSYM is a keypad KEYSYM.  In
this case, if the
<emphasis role='bold'>Shift</emphasis>
modifier is on, or if the
<emphasis role='bold'>Lock</emphasis>
modifier is on and
is interpreted as ShiftLock, then the first KEYSYM is used; otherwise, the
second KEYSYM is used.
    </para>
  </listitem>
  <listitem>
    <para>
The
<emphasis role='bold'>Shift</emphasis>
and
<emphasis role='bold'>Lock</emphasis>
modifiers are both off.  In this case, the first
KEYSYM is used.
    </para>
  </listitem>
  <listitem>
    <para>
The
<emphasis role='bold'>Shift</emphasis>
modifier is off, and the
<emphasis role='bold'>Lock</emphasis>
modifier is on and is
interpreted as CapsLock.  In this case, the first KEYSYM is used, but if
that KEYSYM is lowercase alphabetic, then the corresponding uppercase
KEYSYM is used instead.
    </para>
  </listitem>
  <listitem>
    <para>
The
<emphasis role='bold'>Shift</emphasis>
modifier is on, and the
<emphasis role='bold'>Lock</emphasis>
modifier is on and is interpreted
as CapsLock.  In this case, the second KEYSYM is used, but if that KEYSYM
is lowercase alphabetic, then the corresponding uppercase KEYSYM is used
instead.
    </para>
  </listitem>
  <listitem>
    <para>
The
<emphasis role='bold'>Shift</emphasis>
modifier is on, or the
<emphasis role='bold'>Lock</emphasis>
modifier is on and is interpreted
as ShiftLock, or both.  In this case, the second KEYSYM is used.
    </para>
  </listitem>
</itemizedlist>

<para>
<!-- .LP -->
The mapping between KEYCODEs and KEYSYMs is not used directly by the server;
it is merely stored for reading and writing by clients.
</para>
</chapter>

<chapter id='Pointers'>
<title>Pointers</title>
<!-- .XS -->
<!-- (SN Pointers -->
<!-- .XE -->
<para id="pointers:buttons">
Buttons are always numbered starting with one.
<indexterm zone="pointers:buttons"><primary>Button</primary><secondary>number</secondary></indexterm>
</para>
</chapter>

<chapter id='Predefined_Atoms'>
<title>Predefined Atoms</title>
<!-- .XS -->
<!-- (SN Predefined Atoms -->
<!-- .XE -->
<para>
<!-- .LP -->
<indexterm zone="Predefined_Atoms"><primary>Atom</primary><secondary>predefined</secondary></indexterm>
Predefined atoms are not strictly necessary and may not be useful in all
environments, but they will eliminate many
<link linkend="requests:InternAtom"><emphasis role='bold'>InternAtom</emphasis></link>
requests in most applications.
Note that they are predefined only in the sense of having numeric values,
not in the sense of having required semantics.
The core protocol imposes no semantics on these names,
but semantics are specified in other X Window System standards,
such as the
<citetitle>Inter-Client Communication Conventions Manual</citetitle>
and the <citetitle>X Logical Font Description Conventions</citetitle>.
</para>

<para>
The following names have predefined atom values.
Note that uppercase and lowercase matter.
</para>

<simplelist type='vert' columns='3'>
  <member>ARC</member>
  <member>ATOM</member>
  <member>BITMAP</member>
  <member>CAP_HEIGHT</member>
  <member>CARDINAL</member>
  <member>COLORMAP</member>
  <member>COPYRIGHT</member>
  <member>CURSOR</member>
  <member>CUT_BUFFER0</member>
  <member>CUT_BUFFER1</member>
  <member>CUT_BUFFER2</member>
  <member>CUT_BUFFER3</member>
  <member>CUT_BUFFER4</member>
  <member>CUT_BUFFER5</member>
  <member>CUT_BUFFER6</member>
  <member>CUT_BUFFER7</member>
  <member>DRAWABLE</member>
  <member>END_SPACE</member>
  <member>FAMILY_NAME</member>
  <member>FONT</member>
  <member>FONT_NAME</member>
  <member>FULL_NAME</member>
  <member>INTEGER</member>
  <member>ITALIC_ANGLE</member>
  <member>MAX_SPACE</member>
  <member>MIN_SPACE</member>
  <member>NORM_SPACE</member>
  <member>NOTICE</member>
  <member>PIXMAP</member>
  <member>POINT</member>
  <member>POINT_SIZE</member>
  <member>PRIMARY</member>
  <member>QUAD_WIDTH</member>
  <member>RECTANGLE</member>
  <member>RESOLUTION</member>
  <member>RESOURCE_MANAGER</member>
  <member>RGB_BEST_MAP</member>
  <member>RGB_BLUE_MAP</member>
  <member>RGB_COLOR_MAP</member>
  <member>RGB_DEFAULT_MAP</member>
  <member>RGB_GRAY_MAP</member>
  <member>RGB_GREEN_MAP</member>
  <member>RGB_RED_MAP</member>
  <member>SECONDARY</member>
  <member>STRIKEOUT_ASCENT</member>
  <member>STRIKEOUT_DESCENT</member>
  <member>STRING</member>
  <member>SUBSCRIPT_X</member>
  <member>SUBSCRIPT_Y</member>
  <member>SUPERSCRIPT_X</member>
  <member>SUPERSCRIPT_Y</member>
  <member>UNDERLINE_POSITION</member>
  <member>UNDERLINE_THICKNESS</member>
  <member>VISUALID</member>
  <member>WEIGHT</member>
  <member>WINDOW</member>
  <member>WM_CLASS</member>
  <member>WM_CLIENT_MACHINE</member>
  <member>WM_COMMAND</member>
  <member>WM_HINTS</member>
  <member>WM_ICON_NAME</member>
  <member>WM_ICON_SIZE</member>
  <member>WM_NAME</member>
  <member>WM_NORMAL_HINTS</member>
  <member>WM_SIZE_HINTS</member>
  <member>WM_TRANSIENT_FOR</member>
  <member>WM_ZOOM_HINTS</member>
  <member>X_HEIGHT</member>
</simplelist>

<para>
<!-- .LP -->
To avoid conflicts with possible future names for which semantics might be
imposed (either at the protocol level or in terms of higher level user
interface models),
names beginning with an underscore should be used for atoms
that are private to a particular vendor or organization.
To guarantee no conflicts between vendors and organizations,
additional prefixes need to be used.
However, the protocol does not define the mechanism for choosing such prefixes.
For names private to a single application or end user but stored in globally
accessible locations,
it is suggested that two leading underscores be used to avoid conflicts with
other names.
</para>
</chapter>

<chapter id='Connection_Setup'>
  <title>Connection Setup</title>
  <indexterm zone="Connection_Setup"><primary>Connection</primary></indexterm>

  <para>
For remote clients,
the X protocol can be built on top of any reliable byte stream.
  </para>

  <section id="connection_initiation">
    <title>Connection Initiation</title>
    <indexterm zone="connection_initiation"><primary>Connection</primary><secondary>opening</secondary></indexterm>

    <para id="byte-order">
The client must send an initial byte of data to identify the byte order to be
employed.
<indexterm zone="byte-order"><primary>Byte order</primary></indexterm>
The value of the byte must be octal 102 or 154.
The value 102 (ASCII uppercase B) means values are transmitted most significant
byte first, and value 154 (ASCII lowercase l) means values are transmitted
least significant byte first.
Except where explicitly noted in the protocol,
all 16-bit and 32-bit quantities sent by the client must be transmitted with
this byte order,
and all 16-bit and 32-bit quantities returned by the server will be transmitted
with this byte order.
    </para>
    <para>
Following the byte-order byte,
the client sends the following information at connection setup:
    </para>
    <blockquote>
    <para>
protocol-major-version: CARD16
    </para>
    <para>
protocol-minor-version: CARD16
    </para>
    <para>
authorization-protocol-name: STRING8
    </para>
    <para>
authorization-protocol-data: STRING8
    </para>
    </blockquote>
    <para>
The version numbers indicate what version of the protocol the client
expects the server to implement.
    </para>
    <para id="authorization">
The authorization name indicates what authorization (and authentication)
protocol the client
expects the server to use, and the data is specific to that protocol.
<indexterm zone="authorization" significance="preferred"><primary>Authorization</primary></indexterm>
Specification of valid authorization mechanisms is not part of the core
X protocol.
A server that does not implement the protocol the client expects
or that only implements the host-based mechanism may simply ignore this
information.
If both name and data strings are empty,
this is to be interpreted as "no explicit authorization."
    </para>
  </section>

  <section id="server_response">
    <title>Server Response</title>

    <para>
The client receives the following information at connection setup:
    </para>

<itemizedlist>
  <listitem>
    <para>
success:
{ <emphasis role='bold'>Failed</emphasis>,
<emphasis role='bold'>Success</emphasis>,
<emphasis role='bold'>Authenticate</emphasis>}
    </para>
  </listitem>
</itemizedlist>

    <para>
The client receives the following additional data if the returned success
value is
<emphasis role='bold'>Failed</emphasis>,
and the connection is not successfully established:
    </para>

<blockquote>
    <para>
protocol-major-version: CARD16
    </para>
    <para>
protocol-minor-version: CARD16
    </para>
    <para>
reason: STRING8
    </para>
</blockquote>

    <para>
The client receives the following additional data if the returned success
value is
<emphasis role='bold'>Authenticate</emphasis>,
and further authentication negotiation is required:
    </para>

<blockquote>
    <para>
reason: STRING8
    </para>
</blockquote>

    <para>
The contents of the reason string are specific to the authorization
protocol in use.  The semantics of this authentication negotiation are
not constrained, except that the negotiation must eventually terminate
with a reply from the server containing a success value of
<emphasis role='bold'>Failed</emphasis>
or
<emphasis role='bold'>Success</emphasis>.
    </para>

    <para>
The client receives the following additional data if the returned success
value is
<emphasis role='bold'>Success</emphasis>,
and the connection is successfully established:
    </para>

<blockquote>
    <para>
protocol-major-version: CARD16
    </para>
    <para>
<!-- .br -->
protocol-minor-version: CARD16
    </para>
    <para>
<!-- .br -->
vendor: STRING8
    </para>
    <para>
<!-- .br -->
release-number: CARD32
    </para>
    <para>
<!-- .br -->
resource-id-base, resource-id-mask: CARD32
    </para>
    <para>
<!-- .br -->
image-byte-order:
{ <emphasis role='bold'>LSBFirst</emphasis>,
<emphasis role='bold'>MSBFirst</emphasis> }
    </para>
    <para>
<!-- .br -->
bitmap-scanline-unit: {8, 16, 32}
    </para>
    <para>
<!-- .br -->
bitmap-scanline-pad: {8, 16, 32}
    </para>
    <para>
<!-- .br -->
bitmap-bit-order:
{ <emphasis role='bold'>LeastSignificant</emphasis>,
<emphasis role='bold'>MostSignificant</emphasis> }
    </para>
    <para>
<!-- .br -->
pixmap-formats: LISTofFORMAT
    </para>
    <para>
<!-- .br -->
roots: LISTofSCREEN
    </para>
    <para>
<!-- .br -->
motion-buffer-size: CARD32
    </para>
    <para>
<!-- .br -->
maximum-request-length: CARD16
    </para>
    <para>
<!-- .br -->
min-keycode, max-keycode: KEYCODE
    </para>
    <para>
where:
    </para>
    <blockquote>
<informaltable frame="none">
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='2.0*'/>
  <tbody>
    <row>
      <entry>FORMAT:</entry>
      <entry>[depth: CARD8,</entry>
    </row>
    <row>
      <entry></entry>
      <entry>
bits-per-pixel: {1, 4, 8, 16, 24, 32}
      </entry>
    </row>
    <row>
      <entry></entry>
      <entry>
scanline-pad: {8, 16, 32}]
      </entry>
    </row>
    <row>
      <entry>SCREEN:</entry>
      <entry>[root: WINDOW</entry>
    </row>
    <row>
      <entry></entry>
      <entry>width-in-pixels, height-in-pixels: CARD16</entry>
    </row>
    <row>
      <entry></entry>
      <entry>width-in-millimeters, height-in-millimeters: CARD16</entry>
    </row>
    <row>
      <entry></entry>
      <entry>allowed-depths: LISTofDEPTH</entry>
    </row>
    <row>
      <entry></entry>
      <entry>root-depth: CARD8</entry>
    </row>
    <row>
      <entry></entry>
      <entry>root-visual: VISUALID</entry>
    </row>
    <row>
      <entry></entry>
      <entry>default-colormap: COLORMAP</entry>
    </row>
    <row>
      <entry></entry>
      <entry>white-pixel, black-pixel: CARD32</entry>
    </row>
    <row>
      <entry></entry>
      <entry>min-installed-maps, max-installed-maps: CARD16</entry>
    </row>
    <row>
      <entry></entry>
      <entry>backing-stores: {Never, WhenMapped, Always}</entry>
    </row>
    <row>
      <entry></entry>
      <entry>save-unders: BOOL</entry>
    </row>
    <row>
      <entry></entry>
      <entry>current-input-masks: SETofEVENT]</entry>
    </row>
    <row>
      <entry>DEPTH:</entry>
      <entry>[depth: CARD8</entry>
    </row>
    <row>
      <entry></entry>
      <entry>visuals: LISTofVISUALTYPE]</entry>
    </row>
    <row>
      <entry>VISUALTYPE:</entry>
      <entry>[visual-id: VISUALID</entry>
    </row>
    <row>
      <entry></entry>
      <entry>
class: {StaticGray, StaticColor, TrueColor, GrayScale,
PseudoColor, DirectColor}
      </entry>
    </row>
    <row>
      <entry></entry>
      <entry>red-mask, green-mask, blue-mask: CARD32</entry>
    </row>
    <row>
      <entry></entry>
      <entry>bits-per-rgb-value: CARD8</entry>
    </row>
    <row>
      <entry></entry>
      <entry>colormap-entries: CARD16]</entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
    </blockquote>
</blockquote>
  </section>

  <section id="server_information">
    <title>Server Information</title>

    <para>
The information that is global to the server is:
    </para>

    <para>
The protocol version numbers are an escape hatch in case future revisions of
the protocol are necessary.
In general,
the major version would increment for incompatible changes,
and the minor version would increment for small upward compatible changes.
Barring changes,
the major version will be 11, and the minor version will be 0.
The protocol version numbers returned indicate the protocol the server
actually supports.
This might not equal the version sent by the client.
The server can (but need not) refuse connections from clients that offer a
different version than the server supports.
A server can (but need not) support more than one version simultaneously.
    </para>
    <para>
The vendor string gives some identification of the owner of the server
implementation.
The vendor controls the semantics of the release number.
    </para>
    <para id="resource-id-mask">
The resource-id-mask contains a single contiguous set of bits (at least 18).
The client allocates resource IDs for types WINDOW, PIXMAP,
CURSOR, FONT, GCONTEXT, and COLORMAP by choosing a value with only
some subset of these bits set and ORing it with resource-id-base.
<indexterm zone="resource-id-mask"><primary>Resource</primary><secondary>ID</secondary></indexterm>
Only values constructed in this way can be used to name newly created
resources over this connection.
Resource IDs never have the top three bits set.
The client is not restricted to linear or contiguous allocation
of resource IDs.
Once an ID has been freed,
it can be reused.
An ID must be unique with respect to the IDs of all other resources,
not just other resources of the same type.
However, note that the value spaces of resource identifiers,
atoms, visualids, and keysyms are distinguished by context, and
as such, are not required to be disjoint; for example, a given numeric value
might be both a valid window ID, a valid atom, and a valid keysym.
    </para>
    <para>
Although the server is in general responsible for byte-swapping data to
match the client,
images are always transmitted and received in formats (including byte order)
specified by the server.
The byte order for images is given by image-byte-order and applies to each
scanline unit in XY format (bitmap format) and to each pixel value in Z format.
    </para>
    <para id="bitmap-format">
A bitmap is represented in <glossterm linkend="glossary:Scanline_order">scanline order</glossterm>.
<indexterm zone="bitmap-format"><primary>Bitmap</primary><secondary>format</secondary></indexterm>
Each <glossterm linkend="glossary:Scanline">scanline</glossterm>
is padded to a multiple of bits as given by bitmap-scanline-pad.
The pad bits are of arbitrary value.
The scanline is quantized in multiples of bits as given by bitmap-scanline-unit.
The bitmap-scanline-unit is always less than or equal to the
bitmap-scanline-pad.
Within each unit,
the leftmost bit in the bitmap is either the least significant
or most significant bit in the unit, as given by bitmap-bit-order.
<indexterm zone="bitmap-format"><primary>XYFormat</primary></indexterm>
If a pixmap is represented in XY format,
each plane is represented as a bitmap, and the planes appear from
most significant to least significant in bit order with no padding
between planes.
    </para>
    <para id="pixmap-formats">
Pixmap-formats contains one entry for each
<glossterm linkend="glossary:Depth">depth</glossterm> value.
<indexterm zone="pixmap-formats"><primary>Pixmap</primary><secondary>format</secondary></indexterm>
<indexterm zone="pixmap-formats"><primary>ZFormat</primary></indexterm>
The entry describes the <glossterm linkend="glossary:ZFormat">Z format</glossterm>
used to represent images of that depth.
An entry for a depth is included if any screen supports that depth,
and all screens supporting that depth must support only that Z format for that
depth.
In Z format,
the pixels are in scanline order, left to right within a scanline.
The number of bits used to hold each pixel is given by bits-per-pixel.
Bits-per-pixel may be larger than strictly required by the depth,
in which case the least significant bits are used to hold
the pixmap data, and the values of the unused high-order bits are
undefined.
When the bits-per-pixel is 4,
the order of nibbles in the byte is the same as the image byte-order.
When the bits-per-pixel is 1,
the format is identical for bitmap format.
Each scanline is padded to a multiple of bits as given by scanline-pad.
When bits-per-pixel is 1,
this will be identical to bitmap-scanline-pad.
    </para>
    <para>
How a pointing device roams the screens is up to the server
implementation and is transparent to the protocol.
No geometry is defined among screens.
    </para>
    <para>
The server may retain the recent history of pointer motion and do so to a
finer granularity than is reported by
<link linkend="events:MotionNotify"><emphasis role='bold'>MotionNotify</emphasis></link>
events.
The
<link linkend="requests:GetMotionEvents"><emphasis role='bold'>GetMotionEvents</emphasis></link>
request makes such history available.
The motion-buffer-size gives the approximate maximum number
of elements in the history buffer.
    </para>
    <para id="Maximum-request-length">
Maximum-request-length specifies the maximum length of a request
accepted by the server, in 4-byte units.
<indexterm zone="Maximum-request-length"><primary>Request</primary><secondary>length</secondary></indexterm>
That is, length is the maximum value that can appear in the length field of a
request.
Requests larger than this maximum generate a
<emphasis role='bold'>Length</emphasis>
error,
and the server will read and simply discard the entire request.
Maximum-request-length will always be at least 4096
(that is, requests of length up to and including 16384 bytes
will be accepted by all servers).
    </para>
    <para id="keycode_range">
<indexterm zone="keycode_range"><primary>Types</primary><secondary>KEYCODE</secondary></indexterm>
<indexterm zone="keycode_range"><primary>Keycode</primary></indexterm>
Min-keycode and max-keycode specify the smallest and largest keycode
values transmitted by the server.
Min-keycode is never less than 8,
and max-keycode is never greater than 255.
Not all keycodes in this range are required to have corresponding keys.
    </para>
  </section>

  <section id="screen_information">
    <title>Screen Information</title>
    <indexterm zone="screen_information"><primary>Screen</primary></indexterm>

    <para>
The information that applies per screen is:
    </para>

    <para>
The allowed-depths specifies what pixmap and window depths are supported.
Pixmaps are supported for each depth listed,
and windows of that depth are supported if at least one visual type is listed
for the depth.
A pixmap depth of one is always supported and listed,
but windows of depth one might not be supported.
A depth of zero is never listed,
but zero-depth
<emphasis role='bold'>InputOnly</emphasis>
windows are always supported.
    </para>
    <para>
Root-depth and root-visual specify the depth and visual type of the
root window.
Width-in-pixels and height-in-pixels specify the size of
the root window (which cannot be changed).
The class of the root window is always
<emphasis role='bold'>InputOutput</emphasis>.
Width-in-millimeters and height-in-millimeters can be used to determine the
physical size and the aspect ratio.
    </para>
    <para>
The default-colormap is the one initially associated with the root window.
Clients with minimal color requirements creating windows of
the same depth as the root may want to allocate from this map by
default.
    </para>
    <para>
Black-pixel and white-pixel can be used in implementing a monochrome
application.
These pixel values are for permanently allocated entries in the
default-colormap.
The actual RGB values may be settable on some screens
and, in any case, may not actually be black and white.
The names are intended to convey the expected relative intensity of the colors.
    </para>
    <para>
The border of the root window is initially a pixmap filled with the black-pixel.
The initial background of the root window is a pixmap filled with some
unspecified two-color pattern using black-pixel and white-pixel.
    </para>
    <para>
Min-installed-maps specifies the number of maps that can be guaranteed
to be installed simultaneously (with
<link linkend="requests:InstallColormap"><emphasis role='bold'>InstallColormap</emphasis></link>),
regardless of the number of entries allocated in each map.
Max-installed-maps specifies the maximum number of maps that might possibly be
installed simultaneously, depending on their allocations.
Multiple static-visual colormaps with identical contents but differing in
resource ID should be considered as a single map for the purposes of this
number.
For the typical case of a single hardware colormap, both values will be 1.
    </para>
    <para id="Backing-stores">
<indexterm zone="Backing-stores"><primary>Backing store</primary></indexterm>
Backing-stores indicates when the server supports backing stores for
this screen, although it may be storage limited in the number of
windows it can support at once.
If save-unders is
<emphasis role='bold'>True</emphasis>,
the server can support the save-under mode in
<link linkend="requests:CreateWindow"><emphasis role='bold'>CreateWindow</emphasis></link>
and
<link linkend="requests:ChangeWindowAttributes"><emphasis role='bold'>ChangeWindowAttributes</emphasis></link>,
although again it may be storage limited.
    </para>
    <para>
The current-input-events is what
<link linkend="requests:GetWindowAttributes"><emphasis role='bold'>GetWindowAttributes</emphasis></link>
would return for the all-event-masks for the root window.
    </para>
  </section>

  <section id="visual_information">
    <title>Visual Information</title>
    <indexterm zone="visual_information" significance="preferred"><primary>Visual</primary><secondary>information</secondary></indexterm>

    <para>
The information that applies per visual-type is:
    </para>

    <para>
A given visual type might be listed for more than one depth or for
more than one screen.
    </para>

    <para id="colormap_types">
<indexterm zone="colormap_types" significance="preferred"><primary>Colormap</primary><secondary>types</secondary></indexterm>
<indexterm zone="colormap_types"><primary>Pixel value</primary></indexterm>
For
<emphasis role='bold'>PseudoColor</emphasis>,
a pixel value indexes a colormap to produce independent RGB values;
the RGB values can be changed dynamically.
<emphasis role='bold'>GrayScale</emphasis>
is treated in the same way as
<emphasis role='bold'>PseudoColor</emphasis>
except which primary drives the screen is undefined;
thus, the client should always store the
same value for red, green, and blue in colormaps.
For
<emphasis role='bold'>DirectColor</emphasis>,
a pixel value is decomposed into separate RGB subfields,
and each subfield separately indexes the colormap for the corresponding value.
The RGB values can be changed dynamically.
<emphasis role='bold'>TrueColor</emphasis>
is treated in the same way as
<emphasis role='bold'>DirectColor</emphasis>
except the colormap has predefined read-only RGB values.
These values are server-dependent but provide linear or near-linear
increasing ramps in each primary.
<emphasis role='bold'>StaticColor</emphasis>
is treated in the same way as
<emphasis role='bold'>PseudoColor</emphasis>
except the colormap has predefined read-only RGB values,
which are server-dependent.
<emphasis role='bold'>StaticGray</emphasis>
is treated in the same way as
<emphasis role='bold'>StaticColor</emphasis>
except the red, green, and blue values are equal for any
single pixel value, resulting in shades of gray.
<emphasis role='bold'>StaticGray</emphasis>
with a two-entry colormap can be thought of as monochrome.
    </para>

    <para>
The red-mask, green-mask, and blue-mask are only defined for
<emphasis role='bold'>DirectColor</emphasis>
and
<emphasis role='bold'>TrueColor</emphasis>.
Each has one contiguous set of bits set to 1 with no intersections.
Usually each mask has the same number of bits set to 1.
    </para>

    <para>
The bits-per-rgb-value specifies the log base 2 of the number of
distinct color intensity values (individually) of red, green, and blue.
This number need not bear any relation to the number of colormap entries.
Actual RGB values are always passed in the protocol within a
16-bit spectrum, with 0 being minimum intensity and 65535 being the
maximum intensity.
On hardware that provides a linear zero-based intensity ramp,
the following relationship exists:
    </para>

    <para>
<literallayout class="monospaced">
       hw-intensity = protocol-intensity / (65536 / total-hw-intensities)
</literallayout>
    </para>

    <para>
Colormap entries are indexed from 0.
The colormap-entries defines the number of available colormap entries in a
newly created colormap.
For
<emphasis role='bold'>DirectColor</emphasis>
and
<emphasis role='bold'>TrueColor</emphasis>,
this will usually be 2 to the power of the maximum number of bits set to 1 in
red-mask, green-mask, and blue-mask.
    </para>
  </section>
</chapter>

<chapter id='Requests'>
  <title>Requests</title>
  <section id="requests:CreateWindow">
    <title>CreateWindow</title>
    <indexterm zone="requests:CreateWindow" significance="preferred"><primary>CreateWindow</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>wid</emphasis>, <emphasis remap='I'>parent</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>class</emphasis>:
{ <emphasis role='bold'>InputOutput</emphasis>,
<emphasis role='bold'>InputOnly</emphasis>,
<emphasis role='bold'>CopyFromParent</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>depth</emphasis>: CARD8
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>visual</emphasis>: VISUALID or
<emphasis role='bold'>CopyFromParent</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>x</emphasis>, <emphasis remap='I'>y</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>width</emphasis>, <emphasis remap='I'>height</emphasis>, <emphasis remap='I'>border-width</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>value-mask</emphasis>: BITMASK
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>value-list</emphasis>: LISTofVALUE
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>Colormap</emphasis>,
<emphasis role='bold'>Cursor</emphasis>,
<emphasis role='bold'>IDChoice</emphasis>,
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Pixmap</emphasis>,
<emphasis role='bold'>Value</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request creates an unmapped window and assigns the identifier wid to it.
</para>
<para>
A class of
<emphasis role='bold'>CopyFromParent</emphasis>
means the class is taken from the parent.
A depth of zero for class
<emphasis role='bold'>InputOutput</emphasis>
or
<emphasis role='bold'>CopyFromParent</emphasis>
means the depth is taken from the parent.
A visual of
<emphasis role='bold'>CopyFromParent</emphasis>
means the visual type is taken from the parent.
For class
<emphasis role='bold'>InputOutput</emphasis>,
the visual type and depth must be a combination supported for the screen
(or a
<emphasis role='bold'>Match</emphasis>
error results).
The depth need not be the same as the parent,
but the parent must not be of class
<emphasis role='bold'>InputOnly</emphasis>
(or a
<emphasis role='bold'>Match</emphasis>
error results).
For class
<emphasis role='bold'>InputOnly</emphasis>,
the depth must be zero (or a
<emphasis role='bold'>Match</emphasis>
error results), and the visual must be one supported for the screen (or a
<emphasis role='bold'>Match</emphasis>
error results).
However, the parent can have any depth and class.
</para>
<para>
The server essentially acts as if
<emphasis role='bold'>InputOnly</emphasis>
windows do not exist for the purposes of graphics requests,
exposure processing, and
<link linkend="events:VisibilityNotify"><emphasis role='bold'>VisibilityNotify</emphasis></link>
events.
An
<emphasis role='bold'>InputOnly</emphasis>
window cannot be used as a drawable (as a source or destination for graphics
requests).
<emphasis role='bold'>InputOnly</emphasis>
and
<emphasis role='bold'>InputOutput</emphasis>
windows act identically in other respects-properties,
grabs, input control, and so on.
</para>
<para>
The coordinate system has the X axis horizontal and the Y axis vertical
with the origin [0, 0] at the upper-left corner.
Coordinates are integral,
in terms of pixels,
and coincide with pixel centers.
Each window and pixmap has its own coordinate system.
For a window,
the origin is inside the border at the inside, upper-left corner.
</para>
<para>
The x and y coordinates
for the window are relative to the parent's origin
and specify the position of the upper-left outer corner of the window
(not the origin).
The width and height specify the inside size (not including the border)
and must be nonzero (or a
<emphasis role='bold'>Value</emphasis>
error results).
The border-width for an
<emphasis role='bold'>InputOnly</emphasis>
window must be zero (or a
<emphasis role='bold'>Match</emphasis>
error results).
</para>
<para>
The window is placed on top in the stacking order with respect to siblings.
</para>
<para>
The value-mask and value-list specify attributes of the window that are
to be explicitly initialized.
The possible values are:
</para>
<informaltable frame='topbot'>
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='2.0*'/>
  <thead>
    <row rowsep='1'>
      <entry>Attribute</entry>
      <entry>Type</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry>background-pixmap</entry>
      <entry>
PIXMAP or
<emphasis role='bold'>None</emphasis>
or
<emphasis role='bold'>ParentRelative</emphasis>
      </entry>
    </row>
    <row>
      <entry>background-pixel</entry>
      <entry>CARD32</entry>
    </row>
    <row>
      <entry>border-pixmap</entry>
      <entry>
PIXMAP or
<emphasis role='bold'>CopyFromParent</emphasis>
      </entry>
    </row>
    <row>
      <entry>border-pixel</entry>
      <entry>CARD32</entry>
    </row>
    <row>
      <entry>bit-gravity</entry>
      <entry>BITGRAVITY</entry>
    </row>
    <row>
      <entry>win-gravity</entry>
      <entry>WINGRAVITY</entry>
    </row>
    <row>
      <entry>backing-store</entry>
      <entry>
{ <emphasis role='bold'>NotUseful</emphasis>,
<emphasis role='bold'>WhenMapped</emphasis>,
<emphasis role='bold'>Always</emphasis> }
      </entry>
    </row>
    <row>
      <entry>backing-planes</entry>
      <entry>CARD32</entry>
    </row>
    <row>
      <entry>backing-pixel</entry>
      <entry>CARD32</entry>
    </row>
    <row>
      <entry>save-under</entry>
      <entry>BOOL</entry>
    </row>
    <row>
      <entry>event-mask</entry>
      <entry>SETofEVENT</entry>
    </row>
    <row>
      <entry>do-not-propagate-mask</entry>
      <entry>SETofDEVICEEVENT</entry>
    </row>
    <row>
      <entry>override-redirect</entry>
      <entry>BOOL</entry>
    </row>
    <row>
      <entry>colormap</entry>
      <entry>
COLORMAP or
<emphasis role='bold'>CopyFromParent</emphasis>
      </entry>
    </row>
    <row>
      <entry>cursor</entry>
      <entry>
CURSOR or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
The default values when attributes are not explicitly initialized
are:
</para>

<informaltable frame='topbot'>
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='2.0*'/>
  <thead>
    <row rowsep='1'>
      <entry>Attribute</entry>
      <entry>Default</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry>background-pixmap</entry>
      <entry>
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>border-pixmap</entry>
      <entry>
<emphasis role='bold'>CopyFromParent</emphasis>
      </entry>
    </row>
    <row>
      <entry>bit-gravity</entry>
      <entry>
<emphasis role='bold'>Forget</emphasis>
      </entry>
    </row>
    <row>
      <entry>win-gravity</entry>
      <entry>
<emphasis role='bold'>NorthWest</emphasis>
      </entry>
    </row>
    <row>
      <entry>backing-store</entry>
      <entry>
<emphasis role='bold'>NotUseful</emphasis>
      </entry>
    </row>
    <row>
      <entry>backing-planes</entry>
      <entry>all ones</entry>
    </row>
    <row>
      <entry>backing-pixel</entry>
      <entry>zero</entry>
    </row>
    <row>
      <entry>save-under</entry>
      <entry>
<emphasis role='bold'>False</emphasis>
      </entry>
    </row>
    <row>
      <entry>event-mask</entry>
      <entry>{} (empty set)</entry>
    </row>
    <row>
      <entry>do-not-propagate-mask</entry>
      <entry>{} (empty set)</entry>
    </row>
    <row>
      <entry>override-redirect</entry>
      <entry>
<emphasis role='bold'>False</emphasis>
      </entry>
    </row>
    <row>
      <entry>colormap</entry>
      <entry>
<emphasis role='bold'>CopyFromParent</emphasis>
      </entry>
    </row>
    <row rowsep='1'>
      <entry>cursor</entry>
      <entry>
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
Only the following attributes are defined for
<emphasis role='bold'>InputOnly</emphasis>
windows:
</para>

<itemizedlist>
  <listitem>
    <para>
win-gravity
    </para>
  </listitem>
  <listitem>
    <para>
event-mask
    </para>
  </listitem>
  <listitem>
    <para>
do-not-propagate-mask
    </para>
  </listitem>
  <listitem>
    <para>
override-redirect
    </para>
  </listitem>
  <listitem>
    <para>
cursor
    </para>
  </listitem>
</itemizedlist>

<para>
It is a
<emphasis role='bold'>Match</emphasis>
error to specify any other attributes for
<emphasis role='bold'>InputOnly</emphasis>
windows.
</para>
<para id="requests:CreateWindow:background">
<indexterm zone="requests:CreateWindow:background"><primary>Background</primary></indexterm>
If background-pixmap is given,
it overrides the default background-pixmap.
The background pixmap and the window must have the
same root and the same depth (or a
<emphasis role='bold'>Match</emphasis>
error results).
Any size pixmap can be used, although some sizes may be faster than others.
If background
<emphasis role='bold'>None</emphasis>
is specified, the window has no defined background.
If background
<emphasis role='bold'>ParentRelative</emphasis>
is specified, the parent's background is used,
but the window must have the same depth as the parent (or a
<emphasis role='bold'>Match</emphasis>
error results).
If the parent has background
<emphasis role='bold'>None</emphasis>,
then the window will also have background
<emphasis role='bold'>None</emphasis>.
A copy of the parent's background is not made.
The parent's background is reexamined each time the window background is
required.
If background-pixel is given, it overrides the default
background-pixmap and any background-pixmap given explicitly,
and a pixmap of undefined size filled with background-pixel is used for the
background.
Range checking is not performed on the background-pixel value;
it is simply truncated to the appropriate number of bits.
For a
<emphasis role='bold'>ParentRelative</emphasis>
background,
the background tile origin always aligns with the parent's background tile
origin.
Otherwise, the background tile origin is always the window origin.
</para>
<para>
When no valid contents are available for regions of a window
and the regions are either visible or the server is maintaining backing store,
the server automatically tiles the regions with the window's background
unless the window has a background of
<emphasis role='bold'>None</emphasis>.
If the background is
<emphasis role='bold'>None</emphasis>,
the previous screen contents from other windows of the same depth as the window
are simply left in place if the contents come from the parent of the window
or an inferior of the parent;
otherwise, the initial contents of the exposed regions are undefined.
Exposure events are then generated for the regions, even if the background is
<emphasis role='bold'>None</emphasis>.
</para>
<para>
The border tile origin is always the same as the background tile origin.
If border-pixmap is given,
it overrides the default border-pixmap.
The border pixmap and the window must have the same root
and the same depth (or a
<emphasis role='bold'>Match</emphasis>
error results).
Any size pixmap can be used,
although some sizes may be faster than others.
If
<emphasis role='bold'>CopyFromParent</emphasis>
is given, the parent's border pixmap is copied (subsequent changes to
the parent's border attribute do not affect the child),
but the window must have the same depth as the parent (or a
<emphasis role='bold'>Match</emphasis>
error results).
The pixmap might be copied by sharing the same pixmap object between the
child and parent or by making a complete copy of the pixmap contents.
If border-pixel is given,
it overrides the default border-pixmap and any border-pixmap given explicitly,
and a pixmap of undefined size filled with border-pixel is used for the border.
Range checking is not performed on the border-pixel value;
it is simply truncated to the appropriate number of bits.
</para>
<para>
Output to a window is always clipped to the inside of the window,
so that the border is never affected.
</para>
<para>
The bit-gravity defines which region of the window should be retained
if the window is resized, and win-gravity defines how the window should
be repositioned if the parent is resized (see
<link linkend="requests:ConfigureWindow"><emphasis role='bold'>ConfigureWindow</emphasis></link>
request).
</para>
<para>
A backing-store of
<emphasis role='bold'>WhenMapped</emphasis>
advises the server that maintaining contents of obscured regions
when the window is mapped would be beneficial.
A backing-store of
<emphasis role='bold'>Always</emphasis>
advises the server that maintaining contents even when the window is
unmapped would be beneficial.
In this case,
the server may generate an exposure event when the window is created.
A value of
<emphasis role='bold'>NotUseful</emphasis>
advises the server that maintaining contents is unnecessary,
although a server may still choose to maintain contents while the window
is mapped.
Note that if the server maintains contents,
then the server should maintain complete contents
not just the region within the parent boundaries,
even if the window is larger than its parent.
While the server maintains contents,
exposure events will not normally be generated,
but the server may stop maintaining contents at any time.
</para>
<para>
If save-under is
<emphasis role='bold'>True</emphasis>,
the server is advised that when this window is
mapped, saving the contents of windows it obscures would be beneficial.
</para>
<para>
When the contents of obscured regions of a window are being maintained,
regions obscured by noninferior windows are included in the
destination (and source, when the window is the source) of graphics
requests, but regions obscured by inferior windows are not included.
</para>
<para>
The backing-planes indicates (with bits set to 1) which bit planes
of the window hold dynamic data that must be preserved in backing-stores
and during save-unders.
The backing-pixel specifies what value to use in planes not
covered by backing-planes.
The server is free to save only the specified bit planes in the backing-store
or save-under and regenerate the remaining planes with the specified pixel
value.
Any bits beyond the specified depth of the window in these
values are simply ignored.
</para>
<para id="requests:CreateWindow:event-mask">
<indexterm zone="requests:CreateWindow:event-mask"><primary>Event</primary><secondary>mask</secondary></indexterm>
<indexterm zone="requests:CreateWindow:event-mask"><primary>Event</primary><secondary>propagation</secondary></indexterm>
The event-mask defines which events the client is interested in for
this window (or for some event types, inferiors of the window).
The do-not-propagate-mask defines which events should not be propagated to
ancestor windows when no client has the event type selected in this
window.
</para>
<para>
The override-redirect specifies whether map and configure requests on this
window should override a
<emphasis role='bold'>SubstructureRedirect</emphasis>
on the parent, typically to inform a window manager not to tamper with
the window.
</para>
<para>
The colormap specifies the colormap that best reflects the true
colors of the window.
Servers capable of supporting multiple hardware colormaps may use this
information, and window managers may use it for
<link linkend="requests:InstallColormap"><emphasis role='bold'>InstallColormap</emphasis></link>
requests.
The colormap must have the same visual type and root as the window (or a
<emphasis role='bold'>Match</emphasis>
error results).
If
<emphasis role='bold'>CopyFromParent</emphasis>
is specified,
the parent's colormap is copied (subsequent changes to the parent's
colormap attribute do not affect the child).
However, the window must have the same visual type as the parent (or a
<emphasis role='bold'>Match</emphasis>
error results), and the parent must not have a colormap of
<emphasis role='bold'>None</emphasis>
(or a
<emphasis role='bold'>Match</emphasis>
error results).
For an explanation of
<emphasis role='bold'>None</emphasis>,
see <link linkend="requests:FreeColormap"><emphasis role='bold'>FreeColormap</emphasis></link>
request.
The colormap is copied by sharing the colormap object between the child
and the parent,
not by making a complete copy of the colormap contents.
</para>
<para>
If a cursor is specified,
it will be used whenever the pointer is in the window.
If
<emphasis role='bold'>None</emphasis>
is specified,
the parent's cursor will be used when the pointer is in the window,
and any change in the parent's cursor will cause an immediate change
in the displayed cursor.
</para>
<para>
This request generates a
<link linkend="events:CreateNotify"><emphasis role='bold'>CreateNotify</emphasis></link>
event.
</para>
<para>
The background and border pixmaps and the cursor may be freed
immediately if no further explicit references to them are to be made.
</para>
<para>
Subsequent drawing into the background or border pixmap has an
undefined effect on the window state.
The server might or might not make a copy of the pixmap.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ChangeWindowAttributes">
    <title>ChangeWindowAttributes</title>
    <indexterm zone="requests:ChangeWindowAttributes" significance="preferred"><primary>ChangeWindowAttributes</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>value-mask</emphasis>: BITMASK
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>value-list</emphasis>: LISTofVALUE
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Access</emphasis>,
<emphasis role='bold'>Colormap</emphasis>,
<emphasis role='bold'>Cursor</emphasis>,
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Pixmap</emphasis>,
<emphasis role='bold'>Value</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
The value-mask and value-list specify which attributes are to be changed.
The values and restrictions are the same as for
<link linkend="requests:CreateWindow"><emphasis role='bold'>CreateWindow</emphasis></link>.
</para>
<para>
Setting a new background, whether by background-pixmap or
background-pixel, overrides any previous background.
Setting a new border, whether by border-pixel or border-pixmap,
overrides any previous border.
</para>
<para>
Changing the background does not cause the window contents to be changed.
Setting the border or changing the background such that the
border tile origin changes causes the border to be repainted.
Changing the background of a root window to
<emphasis role='bold'>None</emphasis>
or
<emphasis role='bold'>ParentRelative</emphasis>
restores the default background pixmap.
Changing the border of a root window to
<emphasis role='bold'>CopyFromParent</emphasis>
restores the default border pixmap.
</para>
<para>
Changing the win-gravity does not affect the current position of the
window.
</para>
<para>
Changing the backing-store of an obscured window to
<emphasis role='bold'>WhenMapped</emphasis>
or
<emphasis role='bold'>Always</emphasis>
or changing the backing-planes, backing-pixel, or save-under of
a mapped window may have no immediate effect.
</para>
<para>
Multiple clients can select input on the same window;
their event-masks are disjoint.
When an event is generated,
it will be reported to all interested clients.
However, only one client at a time can select for
<emphasis role='bold'>SubstructureRedirect</emphasis>,
only one client at a time can select for
<emphasis role='bold'>ResizeRedirect</emphasis>,
and only one client at a time can select for
<link linkend="events:ButtonPress"><emphasis role='bold'>ButtonPress</emphasis></link>.
An attempt to violate these restrictions results in an
<emphasis role='bold'>Access</emphasis>
error.
</para>
<para>
There is only one do-not-propagate-mask for a window, not one per
client.
</para>
<para>
Changing the colormap of a window (by defining a new map, not by
changing the contents of the existing map) generates a
<link linkend="events:ColormapNotify"><emphasis role='bold'>ColormapNotify</emphasis></link>
event.
Changing the colormap of a visible window might have no immediate effect
on the screen (see
<link linkend="requests:InstallColormap"><emphasis role='bold'>InstallColormap</emphasis></link>
request).
</para>
<para>
Changing the cursor of a root window to
<emphasis role='bold'>None</emphasis>
restores the default cursor.
</para>
<para>
The order in which attributes are verified and altered is server-dependent.
If an error is generated,
a subset of the attributes may have been altered.
<!-- .sp -->
</para>
  </section>
  <section id="requests:GetWindowAttributes">
    <title>GetWindowAttributes</title>
    <indexterm zone="requests:GetWindowAttributes" significance="preferred"><primary>GetWindowAttributes</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
visual: VISUALID
      </entry>
    </row>
    <row>
      <entry>
class:
{ <emphasis role='bold'>InputOutput</emphasis>,
<emphasis role='bold'>InputOnly</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
bit-gravity: BITGRAVITY
      </entry>
    </row>
    <row>
      <entry>
win-gravity: WINGRAVITY
      </entry>
    </row>
    <row>
      <entry>
backing-store:
{ <emphasis role='bold'>NotUseful</emphasis>,
<emphasis role='bold'>WhenMapped</emphasis>,
<emphasis role='bold'>Always</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
backing-planes: CARD32
      </entry>
    </row>
    <row>
      <entry>
backing-pixel: CARD32
      </entry>
    </row>
    <row>
      <entry>
save-under: BOOL
      </entry>
    </row>
    <row>
      <entry>
colormap: COLORMAP or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
map-is-installed: BOOL
      </entry>
    </row>
    <row>
      <entry>
map-state:
{ <emphasis role='bold'>Unmapped</emphasis>,
<emphasis role='bold'>Unviewable</emphasis>,
<emphasis role='bold'>Viewable</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
all-event-masks, your-event-mask: SETofEVENT
      </entry>
    </row>
    <row>
      <entry>
do-not-propagate-mask: SETofDEVICEEVENT
      </entry>
    </row>
    <row>
      <entry>
override-redirect: BOOL
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the current attributes of the window.
A window is
<emphasis role='bold'>Unviewable</emphasis>
if it is mapped but some ancestor is unmapped.
All-event-masks is the inclusive-OR of all event masks selected on the window
by clients.
Your-event-mask is the event mask selected by the querying client.
<!-- .sp -->
</para>
  </section>
  <section id="requests:DestroyWindow">
    <title>DestroyWindow</title>
    <indexterm zone="requests:DestroyWindow" significance="preferred"><primary>DestroyWindow</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
If the argument window is mapped,
an
<link linkend="requests:UnmapWindow"><emphasis role='bold'>UnmapWindow</emphasis></link>
request is performed automatically.
The window and all inferiors are then destroyed, and a
<link linkend="events:DestroyNotify"><emphasis role='bold'>DestroyNotify</emphasis></link>
event is generated for each window.
The ordering of the
<emphasis role='bold'>DestroyNotify</emphasis>
events is such that for any given window,
<emphasis role='bold'>DestroyNotify</emphasis>
is generated on all inferiors of the window before being generated on
the window itself.
The ordering among siblings and across subhierarchies is not otherwise
constrained.
</para>
<para>
Normal exposure processing on formerly obscured windows is performed.
</para>
<para>
If the window is a root window,
this request has no effect.
<!-- .sp -->
</para>
  </section>
  <section id="requests:DestroySubwindows">
    <title>DestroySubwindows</title>
    <indexterm zone="requests:DestroySubwindows" significance="preferred"><primary>DestroySubwindows</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request performs a
<link linkend="requests:DestroyWindow"><emphasis role='bold'>DestroyWindow</emphasis></link>
request on all children of the window, in bottom-to-top stacking order.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ChangeSaveSet">
    <title>ChangeSaveSet</title>
    <indexterm zone="requests:ChangeSaveSet" significance="preferred"><primary>ChangeSaveSet</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>mode</emphasis>:
{ <emphasis role='bold'>Insert</emphasis>,
<emphasis role='bold'>Delete</emphasis>}
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
Errors:
<!-- .in +.2i -->
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Value</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request adds or removes the specified window from the client's
save-set.
The window must have been created by some other client (or a
<emphasis role='bold'>Match</emphasis>
error results).
For further information about the use of the save-set,
see <link linkend='Connection_Close'>section 10</link>.
</para>
<para>
When windows are destroyed,
the server automatically removes them from the save-set.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ReparentWindow">
    <title>ReparentWindow</title>
    <indexterm zone="requests:ReparentWindow" significance="preferred"><primary>ReparentWindow</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>, <emphasis remap='I'>parent</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>x</emphasis>, <emphasis remap='I'>y</emphasis>: INT16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
If the window is mapped,
an
<link linkend="requests:UnmapWindow"><emphasis role='bold'>UnmapWindow</emphasis></link>
request is performed automatically first.
The window is then removed from its current position in the hierarchy
and is inserted as a child of the specified parent.
The x and y coordinates are relative to the parent's origin
and specify the new position of the upper-left outer corner of the
window.
The window is placed on top in the stacking order with respect
to siblings.
A
<link linkend="events:ReparentNotify"><emphasis role='bold'>ReparentNotify</emphasis></link>
event is then generated.
The override-redirect attribute of the window is passed on in this event;
a value of
<emphasis role='bold'>True</emphasis>
indicates that a window manager should not tamper with this window.
Finally, if the window was originally mapped, a
<link linkend="requests:MapWindow"><emphasis role='bold'>MapWindow</emphasis></link>
request is performed automatically.
</para>
<para>
Normal exposure processing on formerly obscured windows is performed.
The server might not generate exposure events for regions from the
initial unmap that are immediately obscured by the final map.
</para>
<para>
A
<emphasis role='bold'>Match</emphasis>
error is generated if:
<!-- .IP bu 5 -->
The new parent is not on the same screen as the old parent.
<!-- .IP bu 5 -->
The new parent is the window itself or an inferior of the window.
<!-- .IP bu 5 -->
The new parent is
<emphasis role='bold'>InputOnly</emphasis>,
and the window is not.
<!-- .IP bu 5 -->
The window has a
<emphasis role='bold'>ParentRelative</emphasis>
background, and the new parent is not the same depth as the window.
<!-- .sp -->
</para>
  </section>
  <section id="requests:MapWindow">
    <title>MapWindow</title>
    <indexterm zone="requests:MapWindow" significance="preferred"><primary>MapWindow</primary></indexterm>
    <indexterm zone="requests:MapWindow"><primary>Mapped window</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
If the window is already mapped, this request has no effect.
</para>
<para>
If the override-redirect attribute of the window is
<emphasis role='bold'>False</emphasis>
and some other client has selected
<emphasis role='bold'>SubstructureRedirect</emphasis>
on the parent, then a
<link linkend="events:MapRequest"><emphasis role='bold'>MapRequest</emphasis></link>
event is generated, but the window remains unmapped.
Otherwise, the window is mapped,
and a
<link linkend="events:MapNotify"><emphasis role='bold'>MapNotify</emphasis></link>
event is generated.
</para>
<para>
If the window is now viewable and its contents have been discarded,
the window is tiled with its background (if no background is defined,
the existing screen contents are not altered), and zero or more exposure
events are generated.
If a backing-store has been maintained while the window was unmapped,
no exposure events are generated.
If a backing-store will now be maintained,
a full-window exposure is always generated.
Otherwise, only visible regions may be reported.
Similar tiling and exposure take place for any newly viewable inferiors.
<!-- .sp -->
</para>
  </section>
  <section id="requests:MapSubwindows">
    <title>MapSubwindows</title>
    <indexterm zone="requests:MapSubwindows" significance="preferred"><primary>MapSubwindows</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request performs a
<link linkend="requests:MapWindow"><emphasis role='bold'>MapWindow</emphasis></link>
request on all unmapped children of the window,
in top-to-bottom stacking order.
<!-- .sp -->
</para>
  </section>
  <section id="requests:UnmapWindow">
    <title>UnmapWindow</title>
    <indexterm zone="requests:UnmapWindow" significance="preferred"><primary>UnmapWindow</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
If the window is already unmapped, this request has no effect.
Otherwise, the window is unmapped, and an
<link linkend="events:UnmapNotify"><emphasis role='bold'>UnmapNotify</emphasis></link>
event is generated.
Normal exposure processing on formerly obscured windows is performed.
<!-- .sp -->
</para>
  </section>
  <section id="requests:UnmapSubwindows">
    <title>UnmapSubwindows</title>
    <indexterm zone="requests:UnmapSubwindows" significance="preferred"><primary>UnmapSubwindows</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request performs an
<link linkend="requests:UnmapWindow"><emphasis role='bold'>UnmapWindow</emphasis></link>
request on all mapped children of the window,
in bottom-to-top stacking order.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ConfigureWindow">
    <title>ConfigureWindow</title>
    <indexterm zone="requests:ConfigureWindow" significance="preferred"><primary>ConfigureWindow</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>value-mask</emphasis>: BITMASK
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>value-list</emphasis>: LISTofVALUE
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Value</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request changes the configuration of the window.
The value-mask and value-list specify which values are to be given.
The possible values are:
</para>

<informaltable frame='topbot'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='2.0*'/>
  <thead>
    <row rowsep='1'>
      <entry>Attribute</entry>
      <entry>Type</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry>x</entry>
      <entry>INT16</entry>
    </row>
    <row>
      <entry>y</entry>
      <entry>INT16</entry>
    </row>
    <row>
      <entry>width</entry>
      <entry>CARD16</entry>
    </row>
    <row>
      <entry>height</entry>
      <entry>CARD16</entry>
    </row>
    <row>
      <entry>border-width</entry>
      <entry>CARD16</entry>
    </row>
    <row>
      <entry>sibling</entry>
      <entry>WINDOW</entry>
    </row>
    <row>
      <entry>stack-mode</entry>
      <entry>
{ <emphasis role='bold'>Above</emphasis>,
<emphasis role='bold'>Below</emphasis>,
<emphasis role='bold'>TopIf</emphasis>,
<emphasis role='bold'>BottomIf</emphasis>,
<emphasis role='bold'>Opposite</emphasis> }
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
The x and y coordinates are relative to the parent's origin
and specify the position of the upper-left outer corner of the window.
The width and height specify the inside size, not including the border, and
must be nonzero (or a
<emphasis role='bold'>Value</emphasis>
error results).
Those values not specified are taken from the existing geometry of the window.
Note that changing just the border-width leaves the outer-left corner
of the window in a fixed position but moves the absolute position of the
window's origin.
It is a
<emphasis role='bold'>Match</emphasis>
error to attempt to make the border-width of an
<emphasis role='bold'>InputOnly</emphasis>
window nonzero.
</para>
<para>
If the override-redirect attribute of the window is
<emphasis role='bold'>False</emphasis>
and some other client has selected
<emphasis role='bold'>SubstructureRedirect</emphasis>
on the parent, a
<link linkend="events:ConfigureRequest"><emphasis role='bold'>ConfigureRequest</emphasis></link>
event is generated, and no further processing is performed.
Otherwise, the following is performed:
</para>
<para>
If some other client has selected
<emphasis role='bold'>ResizeRedirect</emphasis>
on the window and the inside width or height of the window is being changed,
a
<link linkend="events:ResizeRequest"><emphasis role='bold'>ResizeRequest</emphasis></link>
event is generated,
and the current inside width and height are used instead.
Note that the override-redirect attribute of the window has no effect on
<emphasis role='bold'>ResizeRedirect</emphasis>
and that
<emphasis role='bold'>SubstructureRedirect</emphasis>
on the parent has precedence over
<emphasis role='bold'>ResizeRedirect</emphasis>
on the window.
</para>
<para id="requests:ConfigureWindow:gravity">
<indexterm zone="requests:ConfigureWindow:gravity"><primary>Gravity</primary></indexterm>
<indexterm zone="requests:ConfigureWindow:gravity"><primary>Bit</primary><secondary>gravity</secondary></indexterm>
<indexterm zone="requests:ConfigureWindow:gravity"><primary>Window</primary><secondary>gravity</secondary></indexterm>
The geometry of the window is changed as specified,
the window is restacked among siblings, and a
<link linkend="events:ConfigureNotify"><emphasis role='bold'>ConfigureNotify</emphasis></link>
event is generated if the state of the window actually changes.
If the inside width or height of the window has actually changed,
then children of the window are affected,
according to their win-gravity.
Exposure processing is performed on formerly obscured windows
(including the window itself and its inferiors if regions of them were
obscured but now are not).
Exposure processing is also performed on any new regions of the window
(as a result of increasing the width or height)
and on any regions where window contents are lost.
</para>
<para>
If the inside width or height of a window is not changed
but the window is moved or its border is changed,
then the contents of the window are not lost but move with the window.
Changing the inside width or height of the window causes its contents to be
moved or lost, depending on the bit-gravity of the window.
It also causes children to be reconfigured, depending on their win-gravity.
For a change of width and height of W and H,
we define the [x, y] pairs as:
</para>

<informaltable frame='topbot'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='2.0*'/>
  <thead>
    <row rowsep='1'>
      <entry>Direction</entry>
      <entry>Deltas</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry>
<emphasis role='bold'>NorthWest</emphasis>
      </entry>
      <entry>[0, 0]</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>North</emphasis>
      </entry>
      <entry>[W/2, 0]</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>NorthEast</emphasis>
      </entry>
      <entry>[W, 0]</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>West</emphasis>
      </entry>
      <entry>[0, H/2]</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Center</emphasis>
      </entry>
      <entry>[W/2, H/2]</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>East</emphasis>
      </entry>
      <entry>[W, H/2]</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>SouthWest</emphasis>
      </entry>
      <entry>[0, H]</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>South</emphasis>
      </entry>
      <entry>[W/2, H]</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>SouthEast</emphasis>
      </entry>
      <entry>[W, H]</entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
When a window with one of these bit-gravities is resized,
the corresponding pair defines the change in position of each pixel in the
window.
When a window with one of these win-gravities has its parent window resized,
the corresponding pair defines the change in position
of the window within the parent.
This repositioning generates a
<link linkend="events:GravityNotify"><emphasis role='bold'>GravityNotify</emphasis></link>
event.
<emphasis role='bold'>GravityNotify</emphasis>
events are generated after the
<link linkend="events:ConfigureNotify"><emphasis role='bold'>ConfigureNotify</emphasis></link>
event is generated.
</para>
<para>
A gravity of
<emphasis role='bold'>Static</emphasis>
indicates that the contents or origin should not move relative to the origin
of the root window.
If the change in size of the window is coupled with a change
in position of [X, Y],
then for bit-gravity the change in position of each pixel is [-X, -Y] and for
win-gravity the change in position of a child when its parent is so
resized is [-X, -Y].
Note that
<emphasis role='bold'>Static</emphasis>
gravity still only takes effect when the width or height of the
window is changed, not when the window is simply moved.
</para>
<para>
A bit-gravity of
<emphasis role='bold'>Forget</emphasis>
indicates that the window contents are always discarded after a size change,
even if backing-store or save-under has been requested.
The window is tiled with its background (except, if no background is defined,
the existing screen contents are not altered)
and zero or more exposure events are generated.
</para>
<para>
The contents and borders of inferiors are not affected by their parent's
bit-gravity.
A server is permitted to ignore the specified bit-gravity and use
<emphasis role='bold'>Forget</emphasis>
instead.
</para>
<para>
A win-gravity of
<emphasis role='bold'>Unmap</emphasis>
is like
<emphasis role='bold'>NorthWest</emphasis>,
but the child is also unmapped when the parent is resized,
and an
<link linkend="events:UnmapNotify"><emphasis role='bold'>UnmapNotify</emphasis></link>
event is generated.
<emphasis role='bold'>UnmapNotify</emphasis>
events are generated after the
<link linkend="events:ConfigureNotify"><emphasis role='bold'>ConfigureNotify</emphasis></link>
event is generated.
</para>
<para>
If a sibling and a stack-mode are specified,
the window is restacked as follows:
</para>

<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='3.0*'/>
  <tbody>
    <row>
      <entry>
<emphasis role='bold'>Above</emphasis>
      </entry>
      <entry>
The window is placed just above the sibling.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Below</emphasis>
      </entry>
      <entry>
The window is placed just below the sibling.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>TopIf</emphasis>
      </entry>
      <entry>
If the sibling occludes the window,
then the window is placed at the top of the stack.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>BottomIf</emphasis>
      </entry>
      <entry>
If the window occludes the sibling,
then the window is placed at the bottom of the stack.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Opposite</emphasis>
      </entry>
      <entry>
If the sibling occludes the window,
then the window is placed at the top of the stack.
Otherwise, if the window occludes the sibling,
then the window is placed at the bottom of the stack.
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
If a stack-mode is specified but no sibling is specified,
the window is restacked as follows:
</para>

<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='3.0*'/>
  <tbody>
    <row>
      <entry>
<emphasis role='bold'>Above</emphasis>
      </entry>
      <entry>
The window is placed at the top of the stack.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Below</emphasis>
      </entry>
      <entry>
The window is placed at the bottom of the stack.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>TopIf</emphasis>
      </entry>
      <entry>
If any sibling occludes the window,
then the window is placed at the top of the stack.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>BottomIf</emphasis>
      </entry>
      <entry>
If the window occludes any sibling,
then the window is placed at the bottom of the stack.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Opposite</emphasis>
      </entry>
      <entry>
If any sibling occludes the window,
then the window is placed at the top of the stack.
Otherwise, if the window occludes any sibling,
then the window is placed at the bottom of the stack.
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
It is a
<emphasis role='bold'>Match</emphasis>
error if a sibling is specified without a stack-mode
or if the window is not actually a sibling.
</para>
<para>
Note that the computations for
<emphasis role='bold'>BottomIf</emphasis>,
<emphasis role='bold'>TopIf</emphasis>,
and
<emphasis role='bold'>Opposite</emphasis>
are performed with respect to the window's final geometry (as controlled by
the other arguments to the request), not to its initial geometry.
</para>
<para>
Attempts to configure a root window have no effect.
</para>

  </section>
  <section id="requests:CirculateWindow">
    <title>CirculateWindow</title>
    <indexterm zone="requests:CirculateWindow" significance="preferred"><primary>CirculateWindow</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>direction</emphasis>:
{ <emphasis role='bold'>RaiseLowest</emphasis>,
<emphasis role='bold'>LowerHighest</emphasis>}
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Value</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
If some other client has selected
<emphasis role='bold'>SubstructureRedirect</emphasis>
on the window, then a
<link linkend="events:CirculateRequest"><emphasis role='bold'>CirculateRequest</emphasis></link>
event is generated, and no further processing is performed.
Otherwise, the following is performed, and then a
<link linkend="events:CirculateNotify"><emphasis role='bold'>CirculateNotify</emphasis></link>
event is generated if the window is actually restacked.
</para>
<para>
For
<emphasis role='bold'>RaiseLowest</emphasis>,
<emphasis role='bold'>CirculateWindow</emphasis>
raises the lowest mapped child (if any) that is
occluded by another child to the top of the stack.
For
<emphasis role='bold'>LowerHighest</emphasis>,
<emphasis role='bold'>CirculateWindow</emphasis>
lowers the highest mapped child (if any) that occludes another child to
the bottom of the stack.
Exposure processing is performed on formerly obscured windows.
<!-- .sp -->
</para>
  </section>
  <section id="requests:GetGeometry">
    <title>GetGeometry</title>
    <indexterm zone="requests:GetGeometry" significance="preferred"><primary>GetGeometry</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
root: WINDOW
      </entry>
    </row>
    <row>
      <entry>
depth: CARD8
      </entry>
    </row>
    <row>
      <entry>
x, y: INT16
      </entry>
    </row>
    <row>
      <entry>
width, height, border-width: CARD16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Drawable</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the root and current geometry of the drawable.
The depth is the number of bits per pixel for the object.
The x, y, and border-width will always be zero for pixmaps.
For a window,
the x and y coordinates specify the upper-left outer corner of the window
relative to its parent's origin,
and the width and height specify the inside size, not including the border.
</para>
<para>
It is legal to pass an
<emphasis role='bold'>InputOnly</emphasis>
window as a drawable to this request.
<!-- .sp -->
</para>
  </section>
  <section id="requests:QueryTree">
    <title>QueryTree</title>
    <indexterm zone="requests:QueryTree" significance="preferred"><primary>QueryTree</primary></indexterm>
    <indexterm zone="requests:QueryTree"><primary>Children</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
root: WINDOW
      </entry>
    </row>
    <row>
      <entry>
parent: WINDOW or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
children: LISTofWINDOW
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the root, the parent, and the children of the window.
The children are listed in bottom-to-top stacking order.
<!-- .sp -->
</para>
  </section>
  <section id="requests:InternAtom">
    <title>InternAtom</title>
    <indexterm zone="requests:InternAtom" significance="preferred"><primary>InternAtom</primary></indexterm>
    <indexterm zone="requests:InternAtom"><primary>Atom</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>name</emphasis>: STRING8
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>only-if-exists</emphasis>: BOOL
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
atom: ATOM or
<emphasis role='bold'>None</emphasis>
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the atom for the given name.
If only-if-exists is
<emphasis role='bold'>False</emphasis>,
then the atom is created if it does not exist.
The string should use the ISO Latin-1 encoding.
Uppercase and lowercase matter.
</para>
<para>
The lifetime of an atom is not tied to the interning client.
Atoms remain defined until server reset (see <link linkend='Connection_Close'>section 10</link>).
<!-- .sp -->
</para>
  </section>
  <section id="requests:GetAtomName">
    <title>GetAtomName</title>
    <indexterm zone="requests:GetAtomName" significance="preferred"><primary>GetAtomName</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>atom</emphasis>: ATOM
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
name: STRING8
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Atom</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the name for the given atom.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ChangeProperty">
    <title>ChangeProperty</title>
    <indexterm zone="requests:ChangeProperty" significance="preferred"><primary>ChangeProperty</primary></indexterm>
    <indexterm zone="requests:ChangeProperty"><primary>Property</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>property</emphasis>, <emphasis remap='I'>type</emphasis>: ATOM
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>format</emphasis>: {8, 16, 32}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>mode</emphasis>:
{ <emphasis role='bold'>Replace</emphasis>,
<emphasis role='bold'>Prepend</emphasis>,
<emphasis role='bold'>Append</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>data</emphasis>: LISTofINT8 or LISTofINT16 or LISTofINT32
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>Atom</emphasis>,
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Value</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request alters the property for the specified window.
The type is uninterpreted by the server.
The format specifies whether the data should be viewed as a list of 8-bit,
16-bit, or 32-bit quantities so that the server can correctly byte-swap
as necessary.
</para>
<para>
If the mode is
<emphasis role='bold'>Replace</emphasis>,
the previous property value is discarded.
If the mode is
<emphasis role='bold'>Prepend</emphasis>
or
<emphasis role='bold'>Append</emphasis>,
then the type and format must match the existing property value (or a
<emphasis role='bold'>Match</emphasis>
error results).
If the property is undefined,
it is treated as defined with the correct type
and format with zero-length data.
For
<emphasis role='bold'>Prepend</emphasis>,
the data is tacked on to the beginning of the existing data, and for
<emphasis role='bold'>Append</emphasis>,
it is tacked on to the end of the existing data.
</para>
<para>
This request generates a
<link linkend="events:PropertyNotify"><emphasis role='bold'>PropertyNotify</emphasis></link>
event on the window.
</para>
<para>
The lifetime of a property is not tied to the storing client.
Properties remain until explicitly deleted, until the window is destroyed,
or until server reset (see <link linkend='Connection_Close'>section 10</link>).
</para>
<para>
The maximum size of a property is server-dependent and may vary dynamically.
<!-- .sp -->
</para>
  </section>
  <section id="requests:DeleteProperty">
    <title>DeleteProperty</title>
    <indexterm zone="requests:DeleteProperty" significance="preferred"><primary>DeleteProperty</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>property</emphasis>: ATOM
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Atom</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request deletes the property from the specified window
if the property exists and generates a
<link linkend="events:PropertyNotify"><emphasis role='bold'>PropertyNotify</emphasis></link>
event on the window unless the property does not exist.
<!-- .sp -->
</para>
  </section>
  <section id="requests:GetProperty">
    <title>GetProperty</title>
    <indexterm zone="requests:GetProperty" significance="preferred"><primary>GetProperty</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>property</emphasis>: ATOM
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>type</emphasis>: ATOM or
<emphasis role='bold'>AnyPropertyType</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>long-offset</emphasis>, <emphasis remap='I'>long-length</emphasis>: CARD32
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>delete</emphasis>: BOOL
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
type: ATOM or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
format: {0, 8, 16, 32}
      </entry>
    </row>
    <row>
      <entry>
bytes-after: CARD32
      </entry>
    </row>
    <row>
      <entry>
value: LISTofINT8 or LISTofINT16 or LISTofINT32
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Atom</emphasis>,
<emphasis role='bold'>Value</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
If the specified property does not exist for the specified window,
then the return type is
<emphasis role='bold'>None</emphasis>,
the format and bytes-after are zero,
and the value is empty.
The delete argument is ignored in this case.
If the specified property exists but its type does not match the specified type,
then the return type is the actual type of the property,
the format is the actual format of the property (never zero),
the bytes-after is the length of the property in bytes
(even if the format is 16 or 32),
and the value is empty.
The delete argument is ignored in this case.
If the specified property exists and either
<emphasis role='bold'>AnyPropertyType</emphasis>
is specified or the specified type matches the actual type of the property,
then the return type is the actual type of the property,
the format is the actual format of the property (never zero),
and the bytes-after and value are as follows, given:
<literallayout class="monospaced">
	N = actual length of the stored property in bytes
	    (even if the format is 16 or 32)
	I = 4 * long-offset
	T = N - I
	L = MINIMUM(T, 4 * long-length)
	A = N - (I + L)
</literallayout>
</para>
<para>
The returned value starts at byte index I in the property (indexing from 0),
and its length in bytes is L.
However, it is a
<emphasis role='bold'>Value</emphasis>
error if long-offset is given such that L is negative.
The value of bytes-after is A,
giving the number of trailing unread bytes in the stored
property.
If delete is
<emphasis role='bold'>True</emphasis>
and the bytes-after is zero,
the property is also deleted from the window,
and a
<link linkend="events:PropertyNotify"><emphasis role='bold'>PropertyNotify</emphasis></link>
event is generated on the window.
<!-- .sp -->
</para>
  </section>
  <section id="requests:RotateProperties">
    <title>RotateProperties</title>
    <indexterm zone="requests:RotateProperties" significance="preferred"><primary>RotateProperties</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>delta</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>properties</emphasis>: LISTofATOM
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Atom</emphasis>,
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
If the property names in the list are viewed as being numbered starting
from zero, and there are N property names in the list,
then the value associated with property name I becomes the value
associated with property name (I + delta) mod N, for all I from zero to N - 1.
The effect is to rotate the states by delta places around the virtual ring
of property names (right for positive delta, left for negative delta).
</para>
<para>
If delta mod N is nonzero,
a
<link linkend="events:PropertyNotify"><emphasis role='bold'>PropertyNotify</emphasis></link>
event is generated for each property in the order listed.
</para>
<para>
If an atom occurs more than once in the list or no property with that
name is defined for the window,
a
<emphasis role='bold'>Match</emphasis>
error is generated.
If an
<emphasis role='bold'>Atom</emphasis>
or
<emphasis role='bold'>Match</emphasis>
error is generated, no properties are changed.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ListProperties">
    <title>ListProperties</title>
    <indexterm zone="requests:ListProperties" significance="preferred"><primary>ListProperties</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
atoms: LISTofATOM
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the atoms of properties currently defined on the window.
<!-- .sp -->
</para>
  </section>
  <section id="requests:SetSelectionOwner">
    <title>SetSelectionOwner</title>
    <indexterm zone="requests:SetSelectionOwner" significance="preferred"><primary>SetSelectionOwner</primary></indexterm>
    <indexterm zone="requests:SetSelectionOwner"><primary>Selection</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>selection</emphasis>: ATOM
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>owner</emphasis>: WINDOW or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>time</emphasis>: TIMESTAMP or
<emphasis role='bold'>CurrentTime</emphasis>
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Atom</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request changes the owner, owner window,
and last-change time of the specified selection.
This request has no effect if the specified time is earlier
than the current last-change time of the specified selection or is
later than the current server time.
Otherwise, the last-change time is set to the specified time
with
<emphasis role='bold'>CurrentTime</emphasis>
replaced by the current server time.
If the owner window is specified as
<emphasis role='bold'>None</emphasis>,
then the owner of the selection becomes
<emphasis role='bold'>None</emphasis>
(that is, no owner).
Otherwise, the owner of the selection becomes the client executing the request.
If the new owner (whether a client or
<emphasis role='bold'>None</emphasis>)
is not the same as the current owner
and the current owner is not
<emphasis role='bold'>None</emphasis>,
then the current owner is sent a
<link linkend="events:SelectionClear"><emphasis role='bold'>SelectionClear</emphasis></link>
event.
</para>
<para>
If the client that is the owner of a selection is later terminated
(that is, its connection is closed) or if the owner window it has
specified in the request is later destroyed,
then the owner of the selection automatically reverts to
<emphasis role='bold'>None</emphasis>,
but the last-change time is not affected.
</para>
<para>
The selection atom is uninterpreted by the server.
The owner window is returned by the
<link linkend="requests:GetSelectionOwner"><emphasis role='bold'>GetSelectionOwner</emphasis></link>
request and is reported in
<link linkend="events:SelectionRequest"><emphasis role='bold'>SelectionRequest</emphasis></link>
and
<link linkend="events:SelectionClear"><emphasis role='bold'>SelectionClear</emphasis></link>
events.
</para>
<para>
Selections are global to the server.
<!-- .sp -->
</para>
  </section>
  <section id="requests:GetSelectionOwner">
    <title>GetSelectionOwner</title>
    <indexterm zone="requests:GetSelectionOwner" significance="preferred"><primary>GetSelectionOwner</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>selection</emphasis>: ATOM
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
owner: WINDOW or
<emphasis role='bold'>None</emphasis>
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Atom</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the current owner window of the specified selection,
if any.
If
<emphasis role='bold'>None</emphasis>
is returned, then there is no owner for the selection.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ConvertSelection">
    <title>ConvertSelection</title>
    <indexterm zone="requests:ConvertSelection" significance="preferred"><primary>ConvertSelection</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>selection</emphasis>, <emphasis remap='I'>target</emphasis>: ATOM
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>property</emphasis>: ATOM or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>requestor</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>time</emphasis>: TIMESTAMP or
<emphasis role='bold'>CurrentTime</emphasis>
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Atom</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
If the specified selection has an owner,
the server sends a
<link linkend="events:SelectionRequest"><emphasis role='bold'>SelectionRequest</emphasis></link>
event to that owner.
If no owner for the specified selection exists,
the server generates a
<link linkend="events:SelectionNotify"><emphasis role='bold'>SelectionNotify</emphasis></link>
event to the requestor with property
<emphasis role='bold'>None</emphasis>.
The arguments are passed on unchanged in either of the events.
<!-- .sp -->
</para>
  </section>
  <section id="requests:SendEvent">
    <title>SendEvent</title>
    <indexterm zone="requests:SendEvent" significance="preferred"><primary>SendEvent</primary></indexterm>
    <indexterm zone="requests:SendEvent" significance="preferred"><primary>Event</primary><secondary>sending</secondary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>destination</emphasis>: WINDOW or
<emphasis role='bold'>PointerWindow</emphasis>
or
<emphasis role='bold'>InputFocus</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>propagate</emphasis>: BOOL
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>event-mask</emphasis>: SETofEVENT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>event</emphasis>: &lt;normal-event-format&gt;
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Value</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
If
<emphasis role='bold'>PointerWindow</emphasis>
is specified,
destination is replaced with the window that the pointer is in.
If
<emphasis role='bold'>InputFocus</emphasis>
is specified and the focus window contains the pointer,
destination is replaced with the window that the pointer is in.
Otherwise, destination is replaced with the focus window.
</para>
<para>
If the event-mask is the empty set,
then the event is sent to the client that created the destination window.
If that client no longer exists, no event is sent.
</para>
<para>
If propagate is
<emphasis role='bold'>False</emphasis>,
then the event is sent to every client selecting
on destination any of the event types in event-mask.
</para>
<para>
If propagate is
<emphasis role='bold'>True</emphasis>
and no clients have selected on destination any
of the event types in event-mask,
then destination is replaced with the
closest ancestor of destination for which some client has selected a
type in event-mask and no intervening window has that type in its
do-not-propagate-mask.
If no such window exists or if the window is an ancestor of the focus window
and
<emphasis role='bold'>InputFocus</emphasis>
was originally specified as the destination,
then the event is not sent to any clients.
Otherwise, the event is reported to every client selecting on the final
destination any of the types specified in event-mask.
</para>
<para>
The event code must be one of the core events or one of the events
defined by an extension (or a
<emphasis role='bold'>Value</emphasis>
error results) so that the server can correctly byte-swap the
contents as necessary.
The contents of the event are otherwise unaltered and unchecked
by the server except to force on the most significant bit of the event code
and to set the sequence number in the event correctly.
</para>
<para>
Active grabs are ignored for this request.
<!-- .sp -->
</para>
  </section>
  <section id="requests:GrabPointer">
    <title>GrabPointer</title>
    <indexterm zone="requests:GrabPointer" significance="preferred"><primary>GrabPointer</primary></indexterm>
    <indexterm zone="requests:GrabPointer"><primary>Active grab</primary><secondary>pointer</secondary></indexterm>
    <indexterm zone="requests:GrabPointer"><primary>Pointer</primary><secondary>grabbing</secondary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>grab-window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>owner-events</emphasis>: BOOL
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>event-mask</emphasis>: SETofPOINTEREVENT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>pointer-mode</emphasis>, <emphasis remap='I'>keyboard-mode</emphasis>:
{ <emphasis role='bold'>Synchronous</emphasis>,
<emphasis role='bold'>Asynchronous</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>confine-to</emphasis>: WINDOW or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>cursor</emphasis>: CURSOR or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>time</emphasis>: TIMESTAMP or
<emphasis role='bold'>CurrentTime</emphasis>
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
status:
{ <emphasis role='bold'>Success</emphasis>,
<emphasis role='bold'>AlreadyGrabbed</emphasis>,
<emphasis role='bold'>Frozen</emphasis>,
<emphasis role='bold'>InvalidTime</emphasis>,
<emphasis role='bold'>NotViewable</emphasis>}
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Cursor</emphasis>,
<emphasis role='bold'>Value</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request actively grabs control of the pointer.
Further pointer events are only reported to the grabbing client.
The request overrides any active pointer grab by this client.
</para>
<para>
If owner-events is
<emphasis role='bold'>False</emphasis>,
all generated pointer events are reported with respect to grab-window
and are only reported if selected by event-mask.
If owner-events is
<emphasis role='bold'>True</emphasis>
and a generated pointer event would normally be reported to this client,
it is reported normally.
Otherwise, the event is reported with respect to the grab-window and is
only reported if selected by event-mask.
For either value of owner-events,
unreported events are simply discarded.
</para>
<para>
If pointer-mode is
<emphasis role='bold'>Asynchronous</emphasis>,
pointer event processing continues normally.
If the pointer is currently frozen by this client,
then processing of pointer events is resumed.
If pointer-mode is
<emphasis role='bold'>Synchronous</emphasis>,
the state of the pointer (as seen by means of the protocol) appears to freeze,
and no further pointer events are generated by the server until the
grabbing client issues a releasing
<link linkend="requests:AllowEvents"><emphasis role='bold'>AllowEvents</emphasis></link>
request or until the pointer grab is released.
Actual pointer changes are not lost while the pointer is frozen.
They are simply queued for later processing.
</para>
<para>
If keyboard-mode is
<emphasis role='bold'>Asynchronous</emphasis>,
keyboard event processing is unaffected by activation of the grab.
If keyboard-mode is
<emphasis role='bold'>Synchronous</emphasis>,
the state of the keyboard (as seen by means of the protocol) appears to freeze,
and no further keyboard events are generated by the server until the grabbing
client issues a releasing
<emphasis role='bold'>AllowEvents</emphasis>
request or until the pointer grab is released.
Actual keyboard changes are not lost while the keyboard is frozen.
They are simply queued for later processing.
</para>
<para>
If a cursor is specified,
then it is displayed regardless of what window the pointer is in.
If no cursor is specified,
then when the pointer is in grab-window or one of its subwindows,
the normal cursor for that window is displayed.
Otherwise, the cursor for grab-window is displayed.
</para>
<para>
If a confine-to window is specified,
then the pointer will be restricted to stay contained in that window.
The confine-to window need have no relationship to the grab-window.
If the pointer is not initially in the confine-to window,
then it is warped automatically to the closest edge
(and enter/leave events are generated normally) just before the grab activates.
If the confine-to window is subsequently reconfigured,
the pointer will be warped automatically as necessary to
keep it contained in the window.
</para>
<para>
This request generates
<link linkend="events:EnterNotify"><emphasis role='bold'>EnterNotify</emphasis></link>
and
<link linkend="events:LeaveNotify"><emphasis role='bold'>LeaveNotify</emphasis></link>
events.
</para>
<para>
The request fails with status
<emphasis role='bold'>AlreadyGrabbed</emphasis>
if the pointer is actively grabbed by some other client.
The request fails with status
<emphasis role='bold'>Frozen</emphasis>
if the pointer is frozen by an active grab of another client.
The request fails with status
<emphasis role='bold'>NotViewable</emphasis>
if grab-window or confine-to window is not viewable
or if the confine-to window lies completely outside the boundaries
of the root window.
The request fails with status
<emphasis role='bold'>InvalidTime</emphasis>
if the specified time is earlier than the last-pointer-grab time or later than
the current server time.
Otherwise, the last-pointer-grab time is set to the specified time, with
<emphasis role='bold'>CurrentTime</emphasis>
replaced by the current server time.
<!-- .sp -->
</para>
  </section>
  <section id="requests:UngrabPointer">
    <title>UngrabPointer</title>
    <indexterm zone="requests:UngrabPointer" significance="preferred"><primary>UngrabPointer</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>time</emphasis>: TIMESTAMP or
<emphasis role='bold'>CurrentTime</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request releases the pointer if this client has it actively grabbed (from
either
<link linkend="requests:GrabPointer"><emphasis role='bold'>GrabPointer</emphasis></link>
or
<link linkend="requests:GrabButton"><emphasis role='bold'>GrabButton</emphasis></link>
or from a normal button press) and releases any queued events.
The request has no effect if the specified time is earlier than
the last-pointer-grab time or is later than the current server time.
</para>
<para>
This request generates
<link linkend="events:EnterNotify"><emphasis role='bold'>EnterNotify</emphasis></link>
and
<link linkend="events:LeaveNotify"><emphasis role='bold'>LeaveNotify</emphasis></link>
events.
</para>
<para>
An
<link linkend="requests:UngrabPointer"><emphasis role='bold'>UngrabPointer</emphasis></link>
request is performed automatically if the event window or
confine-to window for an active pointer grab becomes not viewable
or if window reconfiguration causes the confine-to window to lie
completely outside the boundaries of the root window.
<!-- .sp -->
</para>
  </section>
  <section id="requests:GrabButton">
    <title>GrabButton</title>
    <indexterm zone="requests:GrabButton" significance="preferred"><primary>GrabButton</primary></indexterm>
    <indexterm zone="requests:GrabButton"><primary>Button</primary><secondary>grabbing</secondary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>modifiers</emphasis>: SETofKEYMASK or
<emphasis role='bold'>AnyModifier</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>button</emphasis>: BUTTON or
<emphasis role='bold'>AnyButton</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>grab-window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>owner-events</emphasis>: BOOL
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>event-mask</emphasis>: SETofPOINTEREVENT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>pointer-mode</emphasis>, <emphasis remap='I'>keyboard-mode</emphasis>:
{ <emphasis role='bold'>Synchronous</emphasis>,
<emphasis role='bold'>Asynchronous</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>confine-to</emphasis>: WINDOW or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>cursor</emphasis>: CURSOR or
<emphasis role='bold'>None</emphasis>
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Access</emphasis>,
<emphasis role='bold'>Cursor</emphasis>,
<emphasis role='bold'>Value</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request establishes a passive grab.
<indexterm zone="requests:GrabButton"><primary>Passive grab</primary><secondary>pointer</secondary></indexterm>
In the future,
the pointer is actively grabbed as described in
<link linkend="requests:GrabPointer"><emphasis role='bold'>GrabPointer</emphasis></link>,
the last-pointer-grab time is set to the time at which the button was
pressed (as transmitted in the
<link linkend="events:ButtonPress"><emphasis role='bold'>ButtonPress</emphasis></link>
event), and the
<emphasis role='bold'>ButtonPress</emphasis>
event is reported if all of the following conditions are true:
<!-- .IP bu 5 -->
The pointer is not grabbed and the specified button is logically pressed
when the specified modifier keys are logically down,
and no other buttons or modifier keys are logically down.
<!-- .IP bu 5 -->
The grab-window contains the pointer.
<!-- .IP bu 5 -->
The confine-to window (if any) is viewable.
<!-- .IP bu 5 -->
A passive grab on the same button/key combination does not exist
on any ancestor of grab-window.
</para>
<para>
The interpretation of the remaining arguments is the same as for
<link linkend="requests:GrabPointer"><emphasis role='bold'>GrabPointer</emphasis></link>.
The active grab is terminated automatically when
the logical state of the pointer has all buttons released,
independent of the logical state of modifier keys.
Note that the logical state of a device (as seen by means of the protocol)
may lag the physical state if device event processing is frozen.
</para>
<para>
This request overrides all previous passive grabs by the same client on
the same button/key combinations on the same window.
A modifier of
<emphasis role='bold'>AnyModifier</emphasis>
is equivalent to issuing the request for all possible modifier combinations
(including the combination of no modifiers).
It is not required that all specified modifiers have currently assigned
keycodes.
A button of
<emphasis role='bold'>AnyButton</emphasis>
is equivalent to issuing the request for all possible buttons.
Otherwise, it is not required that the button specified currently be assigned
to a physical button.
</para>
<para>
An
<emphasis role='bold'>Access</emphasis>
error is generated if some other client has already issued a
<emphasis role='bold'>GrabButton</emphasis>
request with the same button/key combination on the same window.
When using
<emphasis role='bold'>AnyModifier</emphasis>
or
<emphasis role='bold'>AnyButton</emphasis>,
the request fails completely (no grabs are established), and an
<emphasis role='bold'>Access</emphasis>
error is generated if there is a conflicting grab for any combination.
The request has no effect on an active grab.
<!-- .sp -->
</para>
  </section>
  <section id="requests:UngrabButton">
    <title>UngrabButton</title>
    <indexterm zone="requests:UngrabButton" significance="preferred"><primary>UngrabButton</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>modifiers</emphasis>: SETofKEYMASK or
<emphasis role='bold'>AnyModifier</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>button</emphasis>: BUTTON or
<emphasis role='bold'>AnyButton</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>grab-window</emphasis>: WINDOW
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Value</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request releases the passive button/key combination
on the specified window if it was grabbed by this client.
A modifiers argument of
<emphasis role='bold'>AnyModifier</emphasis>
is equivalent to issuing the request for all possible modifier
combinations (including the combination of no modifiers).
A button of
<emphasis role='bold'>AnyButton</emphasis>
is equivalent to issuing the request for all possible buttons.
The request has no effect on an active grab.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ChangeActivePointerGrab">
    <title>ChangeActivePointerGrab</title>
    <indexterm zone="requests:ChangeActivePointerGrab" significance="preferred"><primary>ChangeActivePointerGrab</primary></indexterm>
    <indexterm zone="requests:ChangeActivePointerGrab"><primary>Active grab</primary><secondary>pointer</secondary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>event-mask</emphasis>: SETofPOINTEREVENT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>cursor</emphasis>: CURSOR or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>time</emphasis>: TIMESTAMP or
<emphasis role='bold'>CurrentTime</emphasis>
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Cursor</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request changes the specified dynamic parameters if the pointer is
actively grabbed by the client and the specified time is no earlier than the
last-pointer-grab time and no later than the current server time.
The interpretation of event-mask and cursor are the same as in
<link linkend="requests:GrabPointer"><emphasis role='bold'>GrabPointer</emphasis></link>.
This request has no effect on the parameters of any passive grabs established
with
<link linkend="requests:GrabButton"><emphasis role='bold'>GrabButton</emphasis></link>.
<!-- .sp -->
</para>
  </section>
  <section id="requests:GrabKeyboard">
    <title>GrabKeyboard</title>
    <indexterm zone="requests:GrabKeyboard" significance="preferred"><primary>GrabKeyboard</primary></indexterm>
    <indexterm zone="requests:GrabKeyboard"><primary>Active grab</primary><secondary>keyboard</secondary></indexterm>
    <indexterm zone="requests:GrabKeyboard"><primary>Keyboard</primary><secondary>grabbing</secondary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>grab-window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>owner-events</emphasis>: BOOL
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>pointer-mode</emphasis>, <emphasis remap='I'>keyboard-mode</emphasis>:
{ <emphasis role='bold'>Synchronous</emphasis>,
<emphasis role='bold'>Asynchronous</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>time</emphasis>: TIMESTAMP or
<emphasis role='bold'>CurrentTime</emphasis>
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
status:
{ <emphasis role='bold'>Success</emphasis>,
<emphasis role='bold'>AlreadyGrabbed</emphasis>,
<emphasis role='bold'>Frozen</emphasis>,
<emphasis role='bold'>InvalidTime</emphasis>,
<emphasis role='bold'>NotViewable</emphasis>}
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Value</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request actively grabs control of the keyboard.
Further key events are reported only to the grabbing client.
This request overrides any active keyboard grab by this client.
</para>
<para>
If owner-events is
<emphasis role='bold'>False</emphasis>,
all generated key events are reported with respect to grab-window.
If owner-events is
<emphasis role='bold'>True</emphasis>
and if a generated key event would normally be reported to this client,
it is reported normally.
Otherwise, the event is reported with respect to the grab-window.
Both
<link linkend="events:KeyPress"><emphasis role='bold'>KeyPress</emphasis></link>
and
<link linkend="events:KeyRelease"><emphasis role='bold'>KeyRelease</emphasis></link>
events are always reported,
independent of any event selection made by the client.
</para>
<para>
If keyboard-mode is
<emphasis role='bold'>Asynchronous</emphasis>,
keyboard event processing continues normally.
If the keyboard is currently frozen by this client,
then processing of keyboard events is resumed.
If keyboard-mode is
<emphasis role='bold'>Synchronous</emphasis>,
the state of the keyboard (as seen by means of the protocol) appears to freeze.
No further keyboard events are generated by the server until the
grabbing client issues a releasing
<link linkend="requests:AllowEvents"><emphasis role='bold'>AllowEvents</emphasis></link>
request or until the keyboard grab is released.
Actual keyboard changes are not lost while the keyboard is frozen.
They are simply queued for later processing.
</para>
<para>
If pointer-mode is
<emphasis role='bold'>Asynchronous</emphasis>,
pointer event processing is unaffected by activation of the grab.
If pointer-mode is
<emphasis role='bold'>Synchronous</emphasis>,
the state of the pointer (as seen by means of the protocol) appears to freeze.
No further pointer events are generated by the server
until the grabbing client issues a releasing
<link linkend="requests:AllowEvents"><emphasis role='bold'>AllowEvents</emphasis></link>
request or until the keyboard grab is released.
Actual pointer changes are not lost while the pointer is frozen.
They are simply queued for later processing.
</para>
<para>
This request generates
<link linkend="events:FocusIn"><emphasis role='bold'>FocusIn</emphasis></link>
and
<link linkend="events:FocusOut"><emphasis role='bold'>FocusOut</emphasis></link>
events.
</para>
<para>
The request fails with status
<emphasis role='bold'>AlreadyGrabbed</emphasis>
if the keyboard is actively grabbed by some other client.
The request fails with status
<emphasis role='bold'>Frozen</emphasis>
if the keyboard is frozen by an active grab of another client.
The request fails with status
<emphasis role='bold'>NotViewable</emphasis>
if grab-window is not viewable.
The request fails with status
<emphasis role='bold'>InvalidTime</emphasis>
if the specified time is earlier than the last-keyboard-grab time
or later than the current server time.
Otherwise, the last-keyboard-grab time is set to the specified time with
<emphasis role='bold'>CurrentTime</emphasis>
replaced by the current server time.
<!-- .sp -->
</para>
  </section>
  <section id="requests:UngrabKeyboard">
    <title>UngrabKeyboard</title>
    <indexterm zone="requests:UngrabKeyboard" significance="preferred"><primary>UngrabKeyboard</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>time</emphasis>: TIMESTAMP or
<emphasis role='bold'>CurrentTime</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request releases the keyboard if this client has it actively grabbed
(as a result of either
<link linkend="requests:GrabKeyboard"><emphasis role='bold'>GrabKeyboard</emphasis></link>
or
<link linkend="requests:GrabKey"><emphasis role='bold'>GrabKey</emphasis></link>)
and releases any queued events.
The request has no effect if the specified time is earlier than the
last-keyboard-grab time or is later than the current server time.
</para>
<para>
This request generates
<link linkend="events:FocusIn"><emphasis role='bold'>FocusIn</emphasis></link>
and
<link linkend="events:FocusOut"><emphasis role='bold'>FocusOut</emphasis></link>
events.
</para>
<para>
An
<link linkend="requests:UngrabKeyboard"><emphasis role='bold'>UngrabKeyboard</emphasis></link>
is performed automatically if the event window for an active keyboard grab
becomes not viewable.
<!-- .sp -->
</para>
  </section>
  <section id="requests:GrabKey">
    <title>GrabKey</title>
    <indexterm zone="requests:GrabKey" significance="preferred"><primary>GrabKey</primary></indexterm>
    <indexterm zone="requests:GrabKey"><primary>Key</primary><secondary>grabbing</secondary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>key</emphasis>: KEYCODE or
<emphasis role='bold'>AnyKey</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>modifiers</emphasis>: SETofKEYMASK or
<emphasis role='bold'>AnyModifier</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>grab-window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>owner-events</emphasis>: BOOL
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>pointer-mode</emphasis>, <emphasis remap='I'>keyboard-mode</emphasis>:
{ <emphasis role='bold'>Synchronous</emphasis>,
<emphasis role='bold'>Asynchronous</emphasis>}
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Access</emphasis>,
<emphasis role='bold'>Value</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request establishes a passive grab on the keyboard.
<indexterm zone="requests:GrabKey"><primary>Passive grab</primary><secondary>keyboard</secondary></indexterm>
In the future,
the keyboard is actively grabbed as described in
<link linkend="requests:GrabKeyboard"><emphasis role='bold'>GrabKeyboard</emphasis></link>,
the last-keyboard-grab time is set to the time at which the key was pressed
(as transmitted in the
<link linkend="events:KeyPress"><emphasis role='bold'>KeyPress</emphasis></link>
event), and the
<emphasis role='bold'>KeyPress</emphasis>
event is reported if all of the following conditions are true:
<!-- .IP bu 5 -->
The keyboard is not grabbed and the specified key
(which can itself be a modifier key) is logically pressed
when the specified modifier keys are logically down,
and no other modifier keys are logically down.
<!-- .IP bu 5 -->
Either the grab-window is an ancestor of (or is) the focus window,
or the grab-window is a descendent of the focus window and contains the pointer.
<!-- .IP bu 5 -->
A passive grab on the same key combination does not exist
on any ancestor of grab-window.
</para>
<para>
The interpretation of the remaining arguments is the same as for
<link linkend="requests:GrabKeyboard"><emphasis role='bold'>GrabKeyboard</emphasis></link>.
The active grab is terminated automatically when the logical state
of the keyboard has the specified key released,
independent of the logical state of modifier keys.
Note that the logical state of a device (as seen by means of the protocol)
may lag the physical state if device event processing is frozen.
</para>
<para>
This request overrides all previous passive grabs by the same client
on the same key combinations on the same window.
A modifier of
<emphasis role='bold'>AnyModifier</emphasis>
is equivalent to issuing the request for all possible modifier combinations
(including the combination of no modifiers).
It is not required that all modifiers specified have
currently assigned keycodes.
A key of
<emphasis role='bold'>AnyKey</emphasis>
is equivalent to issuing the request for all possible keycodes.
Otherwise, the key must be in the range specified by min-keycode
and max-keycode in the connection setup (or a
<emphasis role='bold'>Value</emphasis>
error results).
</para>
<para>
An
<emphasis role='bold'>Access</emphasis>
error is generated if some other client has issued a
<emphasis role='bold'>GrabKey</emphasis>
with the same key combination on the same window.
When using
<emphasis role='bold'>AnyModifier</emphasis>
or
<emphasis role='bold'>AnyKey</emphasis>,
the request fails completely (no grabs are established),
and an
<emphasis role='bold'>Access</emphasis>
error is generated if there is a conflicting grab for any combination.
<!-- .sp -->
</para>
  </section>
  <section id="requests:UngrabKey">
    <title>UngrabKey</title>
    <indexterm zone="requests:UngrabKey" significance="preferred"><primary>UngrabKey</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>key</emphasis>: KEYCODE or
<emphasis role='bold'>AnyKey</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>modifiers</emphasis>: SETofKEYMASK or
<emphasis role='bold'>AnyModifier</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>grab-window</emphasis>: WINDOW
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Value</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request releases the key combination on the specified window
if it was grabbed by this client.
A modifiers argument of
<emphasis role='bold'>AnyModifier</emphasis>
is equivalent to issuing the request for all possible modifier combinations
(including the combination of no modifiers).
A key of
<emphasis role='bold'>AnyKey</emphasis>
is equivalent to issuing the request for all possible keycodes.
This request has no effect on an active grab.
<!-- .sp -->
</para>
  </section>
  <section id="requests:AllowEvents">
    <title>AllowEvents</title>
    <indexterm zone="requests:AllowEvents" significance="preferred"><primary>AllowEvents</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>mode</emphasis>:
{ <emphasis role='bold'>AsyncPointer</emphasis>,
<emphasis role='bold'>SyncPointer</emphasis>,
<emphasis role='bold'>ReplayPointer</emphasis>,
<emphasis role='bold'>AsyncKeyboard</emphasis>,
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>SyncKeyboard</emphasis>,
<emphasis role='bold'>ReplayKeyboard</emphasis>,
<emphasis role='bold'>AsyncBoth</emphasis>,
<emphasis role='bold'>SyncBoth</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>time</emphasis>: TIMESTAMP or
<emphasis role='bold'>CurrentTime</emphasis>
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request releases some queued events if the client has caused a device to
freeze.
The request has no effect if the specified time is earlier
than the last-grab time of the most recent active grab for the client
or if the specified time is later than the current server time.
</para>
<para>
For
<emphasis role='bold'>AsyncPointer</emphasis>,
if the pointer is frozen by the client,
pointer event processing continues normally.
If the pointer is frozen twice by the client on behalf of two separate grabs,
<emphasis role='bold'>AsyncPointer</emphasis>
thaws for both.
<emphasis role='bold'>AsyncPointer</emphasis>
has no effect if the pointer is not frozen by the client,
but the pointer need not be grabbed by the client.
</para>
<para>
For
<emphasis role='bold'>SyncPointer</emphasis>,
if the pointer is frozen and actively grabbed by the client,
pointer event processing continues normally until the next
<link linkend="events:ButtonPress"><emphasis role='bold'>ButtonPress</emphasis></link>
or
<link linkend="events:ButtonRelease"><emphasis role='bold'>ButtonRelease</emphasis></link>
event is reported to the client,
at which time the pointer again appears to freeze.
However, if the reported event causes the pointer grab to be released,
then the pointer does not freeze.
<emphasis role='bold'>SyncPointer</emphasis>
has no effect if the pointer is not frozen by the
client or if the pointer is not grabbed by the client.
</para>
<para>
For
<emphasis role='bold'>ReplayPointer</emphasis>,
if the pointer is actively grabbed by the client and
is frozen as the result of an event having been sent to the client
(either from the activation of a
<link linkend="requests:GrabButton"><emphasis role='bold'>GrabButton</emphasis></link>
or from a previous
<emphasis role='bold'>AllowEvents</emphasis>
with mode
<emphasis role='bold'>SyncPointer</emphasis>
but not from a
<link linkend="requests:GrabPointer"><emphasis role='bold'>GrabPointer</emphasis></link>),
then the pointer grab is released and that event is completely reprocessed,
this time ignoring any passive grabs at or above (towards the root)
the grab-window of the grab just released.
The request has no effect if the pointer is not grabbed by the client
or if the pointer is not frozen as the result of an event.
</para>
<para>
For
<emphasis role='bold'>AsyncKeyboard</emphasis>,
if the keyboard is frozen by the client,
keyboard event processing continues normally.
If the keyboard is frozen twice by the client on behalf of two separate grabs,
<emphasis role='bold'>AsyncKeyboard</emphasis>
thaws for both.
<emphasis role='bold'>AsyncKeyboard</emphasis>
has no effect if the keyboard is not frozen by the client,
but the keyboard need not be grabbed by the client.
</para>
<para>
For
<emphasis role='bold'>SyncKeyboard</emphasis>,
if the keyboard is frozen and actively grabbed by the client,
keyboard event processing continues normally until the next
<link linkend="events:KeyPress"><emphasis role='bold'>KeyPress</emphasis></link>
or
<link linkend="events:KeyRelease"><emphasis role='bold'>KeyRelease</emphasis></link>
event is reported to the client,
at which time the keyboard again appears to freeze.
However, if the reported event causes the keyboard grab to be released,
then the keyboard does not freeze.
<emphasis role='bold'>SyncKeyboard</emphasis>
has no effect if the keyboard is not frozen by the client or
if the keyboard is not grabbed by the client.
</para>
<para>
For
<emphasis role='bold'>ReplayKeyboard</emphasis>,
if the keyboard is actively grabbed by the client
and is frozen as the result of an event having been sent to the client
(either from the activation of a
<link linkend="requests:GrabKey"><emphasis role='bold'>GrabKey</emphasis></link>
or from a previous
<emphasis role='bold'>AllowEvents</emphasis>
with mode
<emphasis role='bold'>SyncKeyboard</emphasis>
but not from a
<link linkend="requests:GrabKeyboard"><emphasis role='bold'>GrabKeyboard</emphasis></link>),
then the keyboard grab is released and that event is completely reprocessed,
this time ignoring any passive grabs at or above (towards the root)
the grab-window of the grab just released.
The request has no effect if the keyboard is not grabbed by the client
or if the keyboard is not frozen as the result of an event.
</para>
<para>
For
<emphasis role='bold'>SyncBoth</emphasis>,
if both pointer and keyboard are frozen by the client,
event processing (for both devices) continues normally until the next
<link linkend="events:ButtonPress"><emphasis role='bold'>ButtonPress</emphasis></link>,
<link linkend="events:ButtonRelease"><emphasis role='bold'>ButtonRelease</emphasis></link>,
<link linkend="events:KeyPress"><emphasis role='bold'>KeyPress</emphasis></link>,
or
<link linkend="events:KeyRelease"><emphasis role='bold'>KeyRelease</emphasis></link>
event is reported to the client for a grabbed device
(button event for the pointer, key event for the keyboard),
at which time the devices again appear to freeze.
However, if the reported event causes the grab to be released,
then the devices do not freeze (but if the other device is still
grabbed, then a subsequent event for it will still cause both devices
to freeze).
<emphasis role='bold'>SyncBoth</emphasis>
has no effect unless both pointer and keyboard are frozen by the client.
If the pointer or keyboard is frozen twice by the client on behalf
of two separate grabs,
<emphasis role='bold'>SyncBoth</emphasis>
thaws for both (but a subsequent freeze for
<emphasis role='bold'>SyncBoth</emphasis>
will only freeze each device once).
</para>
<para>
For
<emphasis role='bold'>AsyncBoth</emphasis>,
if the pointer and the keyboard are frozen by the client,
event processing for both devices continues normally.
If a device is frozen twice by the client on behalf of two separate grabs,
<emphasis role='bold'>AsyncBoth</emphasis>
thaws for both.
<emphasis role='bold'>AsyncBoth</emphasis>
has no effect unless both pointer and keyboard are frozen by the client.
</para>
<para>
<emphasis role='bold'>AsyncPointer</emphasis>,
<emphasis role='bold'>SyncPointer</emphasis>,
and
<emphasis role='bold'>ReplayPointer</emphasis>
have no effect on processing of keyboard events.
<emphasis role='bold'>AsyncKeyboard</emphasis>,
<emphasis role='bold'>SyncKeyboard</emphasis>,
and
<emphasis role='bold'>ReplayKeyboard</emphasis>
have no effect on processing of pointer events.
</para>
<para>
It is possible for both a pointer grab and a keyboard grab to be active
simultaneously (by the same or different clients).
When a device is frozen on behalf of either grab,
no event processing is performed for the device.
It is possible for a single device to be frozen because of both grabs.
In this case, the freeze must be released on behalf of both grabs
before events can again be processed.
If a device is frozen twice by a single client, then a single
<emphasis role='bold'>AllowEvents</emphasis>
releases both.
<!-- .sp -->
</para>
  </section>
  <section id="requests:GrabServer">
    <title>GrabServer</title>
    <indexterm zone="requests:GrabServer" significance="preferred"><primary>GrabServer</primary></indexterm>
    <indexterm zone="requests:GrabServer"><primary>Server</primary><secondary>grabbing</secondary></indexterm>
<para>
This request disables processing of requests and close-downs on all
connections other than the one this request arrived on.
<!-- .sp -->
</para>
  </section>
  <section id="requests:UngrabServer">
    <title>UngrabServer</title>
    <indexterm zone="requests:UngrabServer" significance="preferred"><primary>UngrabServer</primary></indexterm>
<para>
This request restarts processing of requests and close-downs
on other connections.
<!-- .sp -->
</para>
  </section>
  <section id="requests:QueryPointer">
    <title>QueryPointer</title>
    <indexterm zone="requests:QueryPointer" significance="preferred"><primary>QueryPointer</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
root: WINDOW
      </entry>
    </row>
    <row>
      <entry>
child: WINDOW or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
same-screen: BOOL
      </entry>
    </row>
    <row>
      <entry>
root-x, root-y, win-x, win-y: INT16
      </entry>
    </row>
    <row>
      <entry>
mask: SETofKEYBUTMASK
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
The root window the pointer is logically on and the pointer coordinates
relative to the root's origin are returned.
If same-screen is
<emphasis role='bold'>False</emphasis>,
then the pointer is not on the same screen as the argument window,
child is
<emphasis role='bold'>None</emphasis>,
and win-x and win-y are zero.
If same-screen is
<emphasis role='bold'>True</emphasis>,
then win-x and win-y are the pointer coordinates relative to the
argument window's origin, and child is the child containing the
pointer, if any.
The current logical state of the modifier keys and the buttons
are also returned.
Note that the logical state of a device (as seen by means of the protocol)
may lag the physical state if device event processing is frozen.
<!-- .sp -->
</para>
  </section>
  <section id="requests:GetMotionEvents">
    <title>GetMotionEvents</title>
    <indexterm zone="requests:GetMotionEvents" significance="preferred"><primary>GetMotionEvents</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>start</emphasis>,
<emphasis remap='I'>stop</emphasis>: TIMESTAMP or
<emphasis role='bold'>CurrentTime</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>window</emphasis>: WINDOW
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
events: LISTofTIMECOORD
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
where:
      </entry>
    </row>
    <row>
      <entry>
TIMECOORD:	[x, y: INT16
      </entry>
    </row>
    <row>
      <entry>
      time: TIMESTAMP]
<!-- .TE -->
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns all events in the motion history buffer that fall
between the specified start and stop times (inclusive)
and that have coordinates that lie within (including borders)
the specified window at its present placement.
The x and y coordinates are reported relative to the origin of the window.
</para>
<para>
If the start time is later than the stop time or if the start time is
in the future, no events are returned.
If the stop time is in the future, it is equivalent to specifying
<emphasis role='bold'>CurrentTime</emphasis>.
<!-- .sp -->
</para>
  </section>
  <section id="requests:TranslateCoordinates">
    <title>TranslateCoordinates</title>
    <indexterm zone="requests:TranslateCoordinates" significance="preferred"><primary>TranslateCoordinates</primary></indexterm>
    <indexterm zone="glossary:Coordinate_system"><primary>Coordinate system</primary><secondary>translating</secondary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>src-window</emphasis>, <emphasis remap='I'>dst-window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>src-x</emphasis>, <emphasis remap='I'>src-y</emphasis>: INT16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
same-screen: BOOL
      </entry>
    </row>
    <row>
      <entry>
child: WINDOW or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
dst-x, dst-y: INT16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
The src-x and src-y coordinates are taken relative to src-window's
origin and are returned as dst-x and dst-y coordinates relative to
dst-window's origin.
If same-screen is
<emphasis role='bold'>False</emphasis>,
then src-window and dst-window are on different screens,
and dst-x and dst-y are zero.
If the coordinates are contained in a mapped child of dst-window,
then that child is returned.
<!-- .sp -->
</para>
  </section>
  <section id="requests:WarpPointer">
    <title>WarpPointer</title>
    <indexterm zone="requests:WarpPointer" significance="preferred"><primary>WarpPointer</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>src-window</emphasis>: WINDOW or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>dst-window</emphasis>: WINDOW or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>src-x</emphasis>, <emphasis remap='I'>src-y</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>src-width</emphasis>, <emphasis remap='I'>src-height</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>dst-x</emphasis>, <emphasis remap='I'>dst-y</emphasis>: INT16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
If dst-window is
<emphasis role='bold'>None</emphasis>,
this request moves the pointer by offsets [dst-x, dst-y]
relative to the current position of the pointer.
If dst-window is a window,
this request moves the pointer to [dst-x, dst-y] relative to dst-window's
origin.
However, if src-window is not
<emphasis role='bold'>None</emphasis>,
the move only takes place if src-window contains the pointer
and the pointer is contained in the specified rectangle of src-window.
</para>
<para>
The src-x and src-y coordinates are relative to src-window's origin.
If src-height is zero,
it is replaced with the current height of src-window minus src-y.
If src-width is zero,
it is replaced with the current width of src-window minus src-x.
</para>
<para>
This request cannot be used to move the pointer outside the confine-to
window of an active pointer grab.
An attempt will only move the pointer as far as the closest edge
of the confine-to window.
</para>
<para>
This request will generate events just as if the user had instantaneously
moved the pointer.
<!-- .sp -->
</para>
  </section>
  <section id="requests:SetInputFocus">
    <title>SetInputFocus</title>
    <indexterm zone="requests:SetInputFocus" significance="preferred"><primary>SetInputFocus</primary></indexterm>
    <indexterm zone="requests:SetInputFocus"><primary>Input focus</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>focus</emphasis>: WINDOW or
<emphasis role='bold'>PointerRoot</emphasis>
or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>revert-to</emphasis>:
{ <emphasis role='bold'>Parent</emphasis>,
<emphasis role='bold'>PointerRoot</emphasis>,
<emphasis role='bold'>None</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>time</emphasis>: TIMESTAMP or
<emphasis role='bold'>CurrentTime</emphasis>
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Value</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request changes the input focus and the last-focus-change time.
The request has no effect if the specified time is earlier than the current
last-focus-change time or is later than the current server time.
Otherwise, the last-focus-change time is set to the specified time
with
<emphasis role='bold'>CurrentTime</emphasis>
replaced by the current server time.
</para>
<para>
If
<emphasis role='bold'>None</emphasis>
is specified as the focus,
all keyboard events are discarded until a new focus window is set.
In this case, the revert-to argument is ignored.
</para>
<para>
If a window is specified as the focus,
it becomes the keyboard's focus window.
If a generated keyboard event would normally be reported to
this window or one of its inferiors, the event is reported normally.
Otherwise, the event is reported with respect to the focus window.
</para>
<para>
If
<emphasis role='bold'>PointerRoot</emphasis>
is specified as the focus,
the focus window is dynamically taken to be the root window of whatever screen
the pointer is on at each keyboard event.
In this case,
the revert-to argument is ignored.
</para>
<para>
This request generates
<link linkend="events:FocusIn"><emphasis role='bold'>FocusIn</emphasis></link>
and
<link linkend="events:FocusOut"><emphasis role='bold'>FocusOut</emphasis></link>
events.
</para>
<para>
The specified focus window must be viewable at the time of the request (or a
<emphasis role='bold'>Match</emphasis>
error results).
If the focus window later becomes not viewable,
the new focus window depends on the revert-to argument.
If revert-to is
<emphasis role='bold'>Parent</emphasis>,
the focus reverts to the parent (or the closest viewable ancestor)
and the new revert-to value is taken to be
<emphasis role='bold'>None</emphasis>.
If revert-to is
<emphasis role='bold'>PointerRoot</emphasis>
or
<emphasis role='bold'>None</emphasis>,
the focus reverts to that value.
When the focus reverts,
<link linkend="events:FocusIn"><emphasis role='bold'>FocusIn</emphasis></link>
and
<link linkend="events:FocusOut"><emphasis role='bold'>FocusOut</emphasis></link>
events are generated,
but the last-focus-change time is not affected.
<!-- .sp -->
</para>
  </section>
  <section id="requests:GetInputFocus">
    <title>GetInputFocus</title>
    <indexterm zone="requests:GetInputFocus" significance="preferred"><primary>GetInputFocus</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
focus: WINDOW or
<emphasis role='bold'>PointerRoot</emphasis>
or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
revert-to:
{ <emphasis role='bold'>Parent</emphasis>,
<emphasis role='bold'>PointerRoot</emphasis>,
<emphasis role='bold'>None</emphasis>}
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the current focus state.
<!-- .sp -->
</para>
  </section>
  <section id="requests:QueryKeymap">
    <title>QueryKeymap</title>
    <indexterm zone="requests:QueryKeymap" significance="preferred"><primary>QueryKeymap</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
keys: LISTofCARD8
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns a bit vector for the logical state of the keyboard.
Each bit set to 1 indicates that the corresponding key is currently pressed.
The vector is represented as 32 bytes.
Byte N (from 0) contains the bits for keys 8N to 8N + 7
with the least significant bit in the byte representing key 8N.
Note that the logical state of a device (as seen by means of the protocol)
may lag the physical state if device event processing is frozen.
<!-- .sp -->
</para>
  </section>
  <section id="requests:OpenFont">
    <title>OpenFont</title>
    <indexterm zone="requests:OpenFont" significance="preferred"><primary>OpenFont</primary></indexterm>
    <indexterm zone="requests:OpenFont"><primary>Font</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>fid</emphasis>: FONT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>name</emphasis>: STRING8
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>IDChoice</emphasis>,
<emphasis role='bold'>Name</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request loads the specified font, if necessary,
and associates identifier fid with it.
The font name should use the ISO Latin-1 encoding,
and uppercase and lowercase do not matter.
When the characters <quote>?</quote> and <quote>*</quote> are used in a font name, a
pattern match is performed and any matching font is used.
In the pattern,
the <quote>?</quote> character (octal value 77) will match any single character,
and the <quote>*</quote> character (octal value 52) will match any number
of characters.
A structured format for font names is specified in the
X.Org standard <citetitle>X Logical Font Description Conventions</citetitle>.
</para>
<para>
Fonts are not associated with a particular screen
and can be stored as a component of any graphics context.
<!-- .sp -->
</para>
  </section>
  <section id="requests:CloseFont">
    <title>CloseFont</title>
    <indexterm zone="requests:CloseFont" significance="preferred"><primary>CloseFont</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>font</emphasis>: FONT
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Font</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request deletes the association between the resource ID and the font.
The font itself will be freed when no other resource references it.
<!-- .sp -->
</para>
  </section>
  <section id="requests:QueryFont">
    <title>QueryFont</title>
    <indexterm zone="requests:QueryFont" significance="preferred"><primary>QueryFont</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='3' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='1.0*'/>
  <colspec colname='c3' colwidth='1.0*'/>
  <spanspec spanname='all' namest='c1' nameend='c3'/>
  <tbody>
    <row>
      <entry spanname='all'>
<!-- .in +.2i -->
<emphasis remap='I'>font</emphasis>: FONTABLE
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry spanname='all'>
   ▶
      </entry>
    </row>
    <row>
      <entry spanname='all'>
<!-- .in +.2i -->
font-info: FONTINFO
      </entry>
    </row>
    <row>
      <entry spanname='all'>
char-infos: LISTofCHARINFO
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry valign='top' morerows='18'>
<!-- .in +.2i -->
where:
      </entry>
    </row>
    <row>
      <entry valign='top' morerows='9'>
FONTINFO:
      </entry>
      <entry>
[draw-direction:
{ <emphasis role='bold'>LeftToRight</emphasis>,
<emphasis role='bold'>RightToLeft</emphasis> }
      </entry>
    </row>
    <row>
      <entry>
min-char-or-byte2, max-char-or-byte2: CARD16
      </entry>
    </row>
    <row>
      <entry>
min-byte1, max-byte1: CARD8
      </entry>
    </row>
    <row>
      <entry>
all-chars-exist: BOOL
      </entry>
    </row>
    <row>
      <entry>
default-char: CARD16
      </entry>
    </row>
    <row>
      <entry>
min-bounds: CHARINFO
      </entry>
    </row>
    <row>
      <entry>
max-bounds: CHARINFO
      </entry>
    </row>
    <row>
      <entry>
font-ascent: INT16
      </entry>
    </row>
    <row>
      <entry>
font-descent: INT16
      </entry>
    </row>
    <row>
      <entry>
properties: LISTofFONTPROP]
      </entry>
    </row>
    <row>
      <entry valign='top' morerows='1'>
FONTPROP:
      </entry>
      <entry>
[name: ATOM
      </entry>
    </row>
    <row>
      <entry>
value: &lt;32-bit-value&gt;]
      </entry>
    </row>
    <row>
      <entry valign='top' morerows='5'>
CHARINFO:
      </entry>
      <entry>
[left-side-bearing: INT16
      </entry>
    </row>
    <row>
      <entry>
right-side-bearing: INT16
      </entry>
    </row>
    <row>
      <entry>
character-width: INT16
      </entry>
    </row>
    <row>
      <entry>
ascent: INT16
      </entry>
    </row>
    <row>
      <entry>
descent: INT16
      </entry>
    </row>
    <row>
      <entry>
attributes: CARD16]
      </entry>
    </row>
    <row>
      <entry spanname='all'>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Font</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns logical information about a font.
If a gcontext is given for font,
the currently contained font is used.
</para>
<para>
The draw-direction is just a hint
and indicates whether most char-infos have a positive,
<emphasis role='bold'>LeftToRight</emphasis>,
or a negative,
<emphasis role='bold'>RightToLeft</emphasis>,
character-width metric.
The core protocol defines no support for vertical text.
</para>
<para>
If min-byte1 and max-byte1 are both zero,
then min-char-or-byte2 specifies the linear character index corresponding
to the first element of char-infos,
and max-char-or-byte2 specifies the linear character index of the last element.
If either min-byte1 or max-byte1 are nonzero,
then both min-char-or-byte2 and max-char-or-byte2 will be less than 256,
and the 2-byte character index values corresponding to char-infos element N
(counting from 0) are:
<literallayout class="monospaced">
	byte1 = N/D + min-byte1
	byte2 = N\\D + min-char-or-byte2
</literallayout>
</para>
<para>
where:
<literallayout class="monospaced">
	D = max-char-or-byte2 - min-char-or-byte2 + 1
	/ = integer division
	\\ = integer modulus
</literallayout>
</para>
<para>
If char-infos has length zero,
then min-bounds and max-bounds will be identical,
and the effective char-infos is one filled with this char-info, of length:
<literallayout class="monospaced">
	L = D * (max-byte1 - min-byte1 + 1)
</literallayout>
</para>
<para>
That is,
all glyphs in the specified linear or matrix range have the same information,
as given by min-bounds (and max-bounds).
If all-chars-exist is
<emphasis role='bold'>True</emphasis>,
then all characters in char-infos have nonzero bounding boxes.
</para>
<para>
The default-char specifies the character that will be used when an
undefined or nonexistent character is used.
Note that default-char is a CARD16, not CHAR2B.
For a font using 2-byte matrix format,
the default-char has byte1 in the most significant byte
and byte2 in the least significant byte.
If the default-char itself specifies an undefined or nonexistent character,
then no printing is performed for an undefined or nonexistent character.
</para>
<para>
The min-bounds and max-bounds contain the minimum and maximum values of
each individual CHARINFO component over all char-infos (ignoring
nonexistent characters).
The bounding box of the font (that is, the
smallest rectangle enclosing the shape obtained by superimposing all
characters at the same origin [x,y]) has its upper-left coordinate at:
<literallayout class="monospaced">
	[x + min-bounds.left-side-bearing, y - max-bounds.ascent]
</literallayout>
with a width of:
<literallayout class="monospaced">
	max-bounds.right-side-bearing - min-bounds.left-side-bearing
</literallayout>
</para>
<para>
and a height of:
<literallayout class="monospaced">
	max-bounds.ascent + max-bounds.descent
</literallayout>
</para>
<para>
The font-ascent is the logical extent of the font above the baseline
and is used for determining line spacing.
Specific characters may extend beyond this.
The font-descent is the logical extent of the font at or below the baseline
and is used for determining line spacing.
Specific characters may extend beyond this.
If the baseline is at Y-coordinate y,
then the logical extent of the font is inclusive
between the Y-coordinate values (y - font-ascent) and (y + font-descent - 1).
</para>
<para>
A font is not guaranteed to have any properties.
The interpretation of the property value (for example, INT32, CARD32)
must be derived from <emphasis remap='I'>a priori</emphasis> knowledge of the property.
A basic set of font properties is specified in the X.Org
standard <citetitle>X Logical Font Description Conventions</citetitle>.
</para>
<para>
For a character origin at [x,y],
the bounding box of a character (that is,
the smallest rectangle enclosing the character's shape), described in
terms of CHARINFO components, is a rectangle with its upper-left corner at:
<literallayout class="monospaced">
	[x + left-side-bearing, y - ascent]
</literallayout>
</para>
<para>
with a width of:
<literallayout class="monospaced">
	right-side-bearing - left-side-bearing
</literallayout>
</para>
<para>
and a height of:
<literallayout class="monospaced">
	ascent + descent
</literallayout>
</para>
<para>
and the origin for the next character is defined to be:
<literallayout class="monospaced">
	[x + character-width, y]
</literallayout>
</para>
<para>
Note that the baseline is logically viewed as being just below
nondescending characters (when descent is zero, only pixels with
Y-coordinates less than y are drawn) and that the origin is logically
viewed as being coincident with the left edge of a nonkerned character
(when left-side-bearing is zero, no pixels with X-coordinate less than
x are drawn).
</para>
<para>
Note that CHARINFO metric values can be negative.
</para>
<para>
A nonexistent character is represented with all CHARINFO components
zero.
</para>
<para>
The interpretation of the per-character attributes field is
server-dependent.
<!-- .sp -->
</para>
  </section>
  <section id="requests:QueryTextExtents">
    <title>QueryTextExtents</title>
    <indexterm zone="requests:QueryTextExtents" significance="preferred"><primary>QueryTextExtents</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>font</emphasis>: FONTABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>string</emphasis>: STRING16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
draw-direction:
{ <emphasis role='bold'>LeftToRight</emphasis>,
<emphasis role='bold'>RightToLeft</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
font-ascent: INT16
      </entry>
    </row>
    <row>
      <entry>
font-descent: INT16
      </entry>
    </row>
    <row>
      <entry>
overall-ascent: INT16
      </entry>
    </row>
    <row>
      <entry>
overall-descent: INT16
      </entry>
    </row>
    <row>
      <entry>
overall-width: INT32
      </entry>
    </row>
    <row>
      <entry>
overall-left: INT32
      </entry>
    </row>
    <row>
      <entry>
overall-right: INT32
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Font</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the logical extents of the specified string of characters
in the specified font.
If a gcontext is given for font,
the currently contained font is used.
The draw-direction, font-ascent, and font-descent are the same as
described in
<link linkend="requests:QueryFont"><emphasis role='bold'>QueryFont</emphasis></link>.
The overall-ascent is the maximum of the ascent metrics of all characters
in the string, and the overall-descent is the maximum of the descent metrics.
The overall-width is the sum of the character-width metrics of all characters
in the string.
For each character in the string,
let W be the sum of the character-width metrics of all characters preceding it
in the string,
let L be the left-side-bearing metric of the character plus W,
and let R be the right-side-bearing metric of the character plus W.
The overall-left is the minimum L of all characters in the string,
and the overall-right is the maximum R.
</para>
<para>
For fonts defined with linear indexing rather than 2-byte matrix indexing,
the server will interpret each CHAR2B as a 16-bit number that
has been transmitted most significant byte first (that is, byte1 of the
CHAR2B is taken as the most significant byte).
</para>
<para>
Characters with all zero metrics are ignored.
If the font has no defined default-char,
then undefined characters in the string are also ignored.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ListFonts">
    <title>ListFonts</title>
    <indexterm zone="requests:ListFonts" significance="preferred"><primary>ListFonts</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>pattern</emphasis>: STRING8
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>max-names</emphasis>: CARD16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
names: LISTofSTRING8
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns a list
of available font names (as controlled by the font search path; see
<link linkend="requests:SetFontPath"><emphasis role='bold'>SetFontPath</emphasis></link>
request)
that match the pattern.
At most, max-names names will be returned.
The pattern should use the ISO Latin-1 encoding,
and uppercase and lowercase do not matter.
In the pattern,
the <quote>?</quote> character (octal value 77) will match any single character,
and the <quote>*</quote> character (octal value 52) will match any number
of characters.
The returned names are in lowercase.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ListFontsWithInfo">
    <title>ListFontsWithInfo</title>
    <indexterm zone="requests:ListFontsWithInfo" significance="preferred"><primary>ListFontsWithInfo</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>pattern</emphasis>: STRING8
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>max-names</emphasis>: CARD16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
name: STRING8
      </entry>
    </row>
    <row>
      <entry>
info FONTINFO
      </entry>
    </row>
    <row>
      <entry>
replies-hint: CARD32
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
where:
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
FONTINFO: &lt;same type definition as in
<link linkend="requests:QueryFont"><emphasis role='bold'>QueryFont</emphasis></link>&gt;
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request is similar to
<link linkend="requests:ListFonts"><emphasis role='bold'>ListFonts</emphasis></link>,
but it also returns information about each font.
The information returned for each font is identical to what
<link linkend="requests:QueryFont"><emphasis role='bold'>QueryFont</emphasis></link>
would return except that the per-character metrics are not returned.
Note that this request can generate multiple replies.
With each reply,
replies-hint may provide an indication of how many more fonts will be returned.
This number is a hint only and may be larger or smaller than
the number of fonts actually returned.
A zero value does not guarantee that no more fonts will be returned.
After the font replies,
a reply with a zero-length name is sent to indicate the end of the reply
sequence.
<!-- .sp -->
</para>
  </section>
  <section id="requests:SetFontPath">
    <title>SetFontPath</title>
    <indexterm zone="requests:SetFontPath" significance="preferred"><primary>SetFontPath</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>path</emphasis>: LISTofSTRING8
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request defines the search path for font lookup.
There is only one search path per server, not one per client.
The interpretation of the strings is operating-system-dependent,
but the strings are intended to specify directories to be searched in the
order listed.
</para>
<para>
Setting the path to the empty list restores the default path defined
for the server.
</para>
<para>
As a side effect of executing this request,
the server is guaranteed to flush all cached information about fonts
for which there currently are no explicit resource IDs allocated.
</para>
<para>
The meaning of an error from this request is system specific.
<!-- .sp -->
</para>
  </section>
  <section id="requests:GetFontPath">
    <title>GetFontPath</title>
    <indexterm zone="requests:GetFontPath" significance="preferred"><primary>GetFontPath</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
path: LISTofSTRING8
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the current search path for fonts.
<!-- .sp -->
</para>
  </section>
  <section id="requests:CreatePixmap">
    <title>CreatePixmap</title>
    <indexterm zone="requests:CreatePixmap" significance="preferred"><primary>CreatePixmap</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>pid</emphasis>: PIXMAP
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>depth</emphasis>: CARD8
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>width</emphasis>, <emphasis remap='I'>height</emphasis>: CARD16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>IDChoice</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request creates a pixmap and assigns the identifier pid to it.
The width and height must be nonzero (or a
<emphasis role='bold'>Value</emphasis>
error results).
The depth must be one of the depths supported by the root of the specified
drawable (or a
<emphasis role='bold'>Value</emphasis>
error results).
The initial contents of the pixmap are undefined.
</para>
<para>
It is legal to pass an
<emphasis role='bold'>InputOnly</emphasis>
window as a drawable to this request.
<!-- .sp -->
</para>
  </section>
  <section id="requests:FreePixmap">
    <title>FreePixmap</title>
    <indexterm zone="requests:FreePixmap" significance="preferred"><primary>FreePixmap</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>pixmap</emphasis>: PIXMAP
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Pixmap</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request deletes the association between the resource ID and the pixmap.
The pixmap storage will be freed when no other resource references it.
<!-- .sp -->
</para>
  </section>
  <section id="requests:CreateGC">
    <title>CreateGC</title>
    <indexterm zone="requests:CreateGC" significance="preferred"><primary>CreateGC</primary></indexterm>
    <indexterm zone="requests:CreateGC"><primary>Graphics context</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>cid</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>value-mask</emphasis>: BITMASK
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>value-list</emphasis>: LISTofVALUE
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>Font</emphasis>,
<emphasis role='bold'>IDChoice</emphasis>,
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Pixmap</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
This request creates a graphics context
and assigns the identifier cid to it.
The gcontext can be used with any destination drawable having the same root
and depth as the specified drawable;
use with other drawables results in a
<emphasis role='bold'>Match</emphasis>
error.
</para>
<para>
The value-mask and value-list specify which components are to be
explicitly initialized.
The context components are:
</para>

<informaltable frame='topbot'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='2.0*'/>
  <thead>
    <row rowsep='1'>
      <entry>Component</entry>
      <entry>Type</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry>function</entry>
      <entry>
{ <emphasis role='bold'>Clear</emphasis>,
<emphasis role='bold'>And</emphasis>,
<emphasis role='bold'>AndReverse</emphasis>,
<emphasis role='bold'>Copy</emphasis>,
<emphasis role='bold'>AndInverted</emphasis>,
<emphasis role='bold'>NoOp</emphasis>,
<emphasis role='bold'>Xor</emphasis>,
<emphasis role='bold'>Or</emphasis>,
<emphasis role='bold'>Nor</emphasis>,
<emphasis role='bold'>Equiv</emphasis>,
<emphasis role='bold'>Invert</emphasis>,
<emphasis role='bold'>OrReverse</emphasis>,
<emphasis role='bold'>CopyInverted</emphasis>,
<emphasis role='bold'>OrInverted</emphasis>,
<emphasis role='bold'>Nand</emphasis>,
<emphasis role='bold'>Set</emphasis> }
      </entry>
    </row>
    <row>
      <entry>plane-mask</entry>
      <entry>CARD32</entry>
    </row>
    <row>
      <entry>foreground</entry>
      <entry>CARD32</entry>
    </row>
    <row>
      <entry>background</entry>
      <entry>CARD32</entry>
    </row>
    <row>
      <entry>line-width</entry>
      <entry>CARD16</entry>
    </row>
    <row>
      <entry>line-style</entry>
      <entry>
{ <emphasis role='bold'>Solid</emphasis>,
<emphasis role='bold'>OnOffDash</emphasis>,
<emphasis role='bold'>DoubleDash</emphasis> }
      </entry>
    </row>
    <row>
      <entry>cap-style</entry>
      <entry>
{ <emphasis role='bold'>NotLast</emphasis>,
<emphasis role='bold'>Butt</emphasis>,
<emphasis role='bold'>Round</emphasis>,
<emphasis role='bold'>Projecting</emphasis> }
      </entry>
    </row>
    <row>
      <entry>join-style</entry>
      <entry>
{ <emphasis role='bold'>Miter</emphasis>,
<emphasis role='bold'>Round</emphasis>,
<emphasis role='bold'>Bevel</emphasis> }
      </entry>
    </row>
    <row>
      <entry>fill-style</entry>
      <entry>
{ <emphasis role='bold'>Solid</emphasis>,
<emphasis role='bold'>Tiled</emphasis>,
<emphasis role='bold'>OpaqueStippled</emphasis>,
<emphasis role='bold'>Stippled</emphasis> }
      </entry>
    </row>
    <row>
      <entry>fill-rule</entry>
      <entry>
{ <emphasis role='bold'>EvenOdd</emphasis>,
<emphasis role='bold'>Winding</emphasis> }
      </entry>
    </row>
    <row>
      <entry>arc-mode</entry>
      <entry>
{ <emphasis role='bold'>Chord</emphasis>,
<emphasis role='bold'>PieSlice</emphasis> }
      </entry>
    </row>
    <row>
      <entry>tile</entry>
      <entry>PIXMAP</entry>
    </row>
    <row>
      <entry>stipple</entry>
      <entry>PIXMAP</entry>
    </row>
    <row>
      <entry>tile-stipple-x-origin</entry>
      <entry>INT16</entry>
    </row>
    <row>
      <entry>tile-stipple-y-origin</entry>
      <entry>INT16</entry>
    </row>
    <row>
      <entry>font</entry>
      <entry>FONT</entry>
    </row>
    <row>
      <entry>subwindow-mode</entry>
      <entry>
{ <emphasis role='bold'>ClipByChildren</emphasis>,
<emphasis role='bold'>IncludeInferiors</emphasis> }
      </entry>
    </row>
    <row>
      <entry>graphics-exposures</entry>
      <entry>BOOL</entry>
    </row>
    <row>
      <entry>clip-x-origin</entry>
      <entry>INT16</entry>
    </row>
    <row>
      <entry>clip-y-origin</entry>
      <entry>INT16</entry>
    </row>
    <row>
      <entry>clip-mask</entry>
      <entry>
PIXMAP or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>dash-offset</entry>
      <entry>CARD16</entry>
    </row>
    <row>
      <entry>dashes</entry>
      <entry>CARD8</entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para id="requests:CreateGC:plane-mask">
<indexterm zone="requests:CreateGC:plane-mask"><primary>Plane</primary><secondary>mask</secondary></indexterm>
In graphics operations,
given a source and destination pixel,
the result is computed bitwise on corresponding bits of the pixels;
that is, a Boolean operation is performed in each bit plane.
The plane-mask restricts the operation to a subset of planes,
so the result is:
</para>

<literallayout class="monospaced">
	((src FUNC dst) AND plane-mask) OR (dst AND (NOT plane-mask))
</literallayout>

<para>
Range checking is not performed on the values for foreground, background,
or plane-mask.
They are simply truncated to the appropriate number of bits.
</para>

<para id="requests:CreateGC:function">
The meanings of the functions are:
</para>

<informaltable frame='topbot'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='2.0*'/>
  <thead>
    <row rowsep='1'>
      <entry>Function</entry>
      <entry>Operation</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry>
<emphasis role='bold'>Clear</emphasis>
      </entry>
      <entry>0</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>And</emphasis>
      </entry>
      <entry>src AND dst</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>AndReverse</emphasis>
      </entry>
      <entry>src AND (NOT dst)</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Copy</emphasis>
      </entry>
      <entry>src</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>AndInverted</emphasis>
      </entry>
      <entry>(NOT src) AND dst</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>NoOp</emphasis>
      </entry>
      <entry>dst</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Xor</emphasis>
      </entry>
      <entry>src XOR dst</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Or</emphasis>
      </entry>
      <entry>src OR dst</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Nor</emphasis>
      </entry>
      <entry>(NOT src) AND (NOT dst)</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Equiv</emphasis>
      </entry>
      <entry>(NOT src) XOR dst</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Invert</emphasis>
      </entry>
      <entry>NOT dst</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>OrReverse</emphasis>
      </entry>
      <entry>src OR (NOT dst)</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>CopyInverted</emphasis>
      </entry>
      <entry>NOT src</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>OrInverted</emphasis>
      </entry>
      <entry>(NOT src) OR dst</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Nand</emphasis>
      </entry>
      <entry>(NOT src) OR (NOT dst)</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Set</emphasis>
      </entry>
      <entry>1</entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para id="requests:CreateGC:line-width">
<indexterm zone="requests:CreateGC:line-width"><primary>Line</primary><secondary>drawing</secondary></indexterm>
The line-width is measured in pixels and can be greater than or equal to
one, a wide line, or the special value zero, a thin line.
</para>

<para>
Wide lines are drawn centered on the path described by the graphics request.
Unless otherwise specified by the join or cap style,
the bounding box of a wide line with endpoints [x1, y1], [x2, y2] and
width w is a rectangle with vertices at the following real coordinates:
</para>

<literallayout class="monospaced">
	[x1-(w*sn/2), y1+(w*cs/2)], [x1+(w*sn/2), y1-(w*cs/2)],
	[x2-(w*sn/2), y2+(w*cs/2)], [x2+(w*sn/2), y2-(w*cs/2)]
</literallayout>

<para>
The sn is the sine of the angle of the line and cs is the cosine of
the angle of the line.
A pixel is part of the line (and hence drawn) if the center of the pixel
is fully inside the bounding box, which is viewed as having infinitely thin
edges.
If the center of the pixel is exactly on the bounding box,
it is part of the line if and only if the interior is immediately to its right
(x increasing direction).
Pixels with centers on a horizontal edge are a special case and are part of
the line if and only if the interior or the boundary is immediately below
(y increasing direction) and if the interior or the boundary is immediately
to the right (x increasing direction).
Note that this description is a mathematical model describing the pixels
that are drawn for a wide line and does not imply that trigonometry is required
to implement such a model.
Real or fixed point arithmetic is recommended for computing the corners of the
line endpoints for lines greater than one pixel in width.
</para>
<para>
Thin lines (zero line-width) are nominally one pixel wide lines drawn using an
unspecified, device-dependent algorithm.
There are only two constraints on this algorithm.
First, if a line is drawn unclipped from [x1,y1] to [x2,y2]
and another line is drawn unclipped from [x1+dx,y1+dy] to [x2+dx,y2+dy],
then a point [x,y] is touched by drawing the first line if
and only if the point [x+dx,y+dy] is touched by drawing the second line.
Second, the effective set of points comprising a line cannot be affected
by clipping.
Thus, a point is touched in a clipped line if and only if the point lies
inside the clipping region and the point would be touched by the line
when drawn unclipped.
</para>
<para>
Note that a wide line drawn from [x1,y1] to [x2,y2] always draws the
same pixels as a wide line drawn from [x2,y2] to [x1,y1], not counting
cap-style and join-style.
Implementors are encouraged to make this property true for thin lines,
but it is not required.
A line-width of zero may differ from a line-width of one in which pixels
are drawn.
In general,
drawing a thin line will be faster than drawing a wide line of width one,
but thin lines may not mix well aesthetically with wide lines
because of the different drawing algorithms.
If it is desirable to obtain precise and uniform results across all displays,
a client should always use a line-width of one, rather than a line-width of
zero.
</para>
<para id="requests:CreateGC:line-style">
The line-style defines which sections of a line are drawn:
</para>

<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='2.0*'/>
  <tbody>
    <row>
      <entry>
<emphasis role='bold'>Solid</emphasis>
      </entry>
      <entry>
The full path of the line is drawn.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>DoubleDash</emphasis>
      </entry>
      <entry>
The full path of the line is drawn,
but the even dashes are filled differently than the odd dashes
(see fill-style), with
<emphasis role='bold'>Butt</emphasis>
cap-style used where even and odd dashes meet.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>OnOffDash</emphasis>
      </entry>
      <entry>
Only the even dashes are drawn,
and cap-style applies to all internal ends of the individual dashes
(except
<emphasis role='bold'>NotLast</emphasis>
is treated as
<emphasis role='bold'>Butt</emphasis>).
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para id="requests:CreateGC:cap-style">
The cap-style defines how the endpoints of a path are drawn:
</para>

<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='2.0*'/>
  <tbody>
    <row>
      <entry>
<emphasis role='bold'>NotLast</emphasis>
      </entry>
      <entry>
The result is equivalent to
<emphasis role='bold'>Butt</emphasis>,
except that for a line-width of zero the final endpoint is not drawn.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Butt</emphasis>
      </entry>
      <entry>
The result is square at the endpoint (perpendicular to the slope of the
line) with no projection beyond.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Round</emphasis>
      </entry>
      <entry>
The result is a circular arc with its diameter equal to the line-width,
centered on the endpoint; it is equivalent to
<emphasis role='bold'>Butt</emphasis>
for line-width zero.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Projecting</emphasis>
      </entry>
      <entry>
The result is square at the end, but the path continues beyond the
endpoint for a distance equal to half the line-width;
it is equivalent to
<emphasis role='bold'>Butt</emphasis>
for line-width zero.
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>



<para id="requests:CreateGC:join-style">
The join-style defines how corners are drawn for wide lines:
</para>

<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='2.0*'/>
  <tbody>
    <row>
      <entry>
<emphasis role='bold'>Miter</emphasis>
      </entry>
      <entry>
The outer edges of the two lines extend to meet at an angle.
However, if the angle is less than 11 degrees, a
<emphasis role='bold'>Bevel</emphasis>
join-style is used instead.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Round</emphasis>
      </entry>
      <entry>
The result is a circular arc with a diameter equal to the line-width,
centered on the joinpoint.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Bevel</emphasis>
      </entry>
      <entry>
The result is
<emphasis role='bold'>Butt</emphasis>
endpoint styles, and then the triangular notch is filled.
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>


<para>
For a line with coincident endpoints (x1=x2, y1=y2), when the cap-style
is applied to both endpoints, the semantics depends on the line-width
and the cap-style:
</para>

<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='3' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='1.0*'/>
  <colspec colname='c3' colwidth='4.0*'/>
  <tbody>
    <row>
      <entry>
<emphasis role='bold'>NotLast</emphasis>
      </entry>
      <entry>thin</entry>
      <entry>
This is device-dependent, but the desired effect is that nothing is drawn.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Butt</emphasis>
      </entry>
      <entry>thin</entry>
      <entry>
This is device-dependent, but the desired effect is that
a single pixel is drawn.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Round</emphasis>
      </entry>
      <entry>thin</entry>
      <entry>
This is the same as
<emphasis role='bold'>Butt</emphasis>/thin.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Projecting</emphasis>
      </entry>
      <entry>thin</entry>
      <entry>
This is the same as
<emphasis role='bold'>Butt</emphasis>/thin.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Butt</emphasis>
      </entry>
      <entry>wide</entry>
      <entry>
Nothing is drawn.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Round</emphasis>
      </entry>
      <entry>wide</entry>
      <entry>
The closed path is a circle, centered at the endpoint and
with a diameter equal to the line-width.
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Projecting</emphasis>
      </entry>
      <entry>wide</entry>
      <entry>
The closed path is a square, aligned with the coordinate axes,
centered at the endpoint and with sides equal to the line-width.
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
For a line with coincident endpoints (x1=x2, y1=y2),
when the join-style is applied at one or both endpoints,
the effect is as if the line was removed from the overall path.
However, if the total path consists of (or is reduced to) a single point
joined with itself,
the effect is the same as when the cap-style is applied at both endpoints.
</para>
<para>
The tile/stipple represents an infinite two-dimensional plane
with the tile/stipple
replicated in all dimensions.  When that plane is superimposed on
the drawable for use in a graphics operation, the upper-left corner
of some instance of the tile/stipple is at the coordinates within
the drawable specified by the tile/stipple origin.
The tile/stipple and clip origins are interpreted relative to the
origin of whatever destination drawable is specified in a graphics
request.
</para>
<para>
The tile pixmap must have the same root and depth as the gcontext (or a
<emphasis role='bold'>Match</emphasis>
error results).
The stipple pixmap must have depth one and must have the same root
as the gcontext (or a
<emphasis role='bold'>Match</emphasis>
error results).
For fill-style
<emphasis role='bold'>Stippled</emphasis>
(but not fill-style
<emphasis role='bold'>OpaqueStippled</emphasis>),
the stipple pattern is tiled in a single plane
and acts as an additional clip mask to be ANDed with the clip-mask.
Any size pixmap can be used for tiling or stippling,
although some sizes may be faster to use than others.
</para>
<para>
The fill-style defines the contents of the source for line, text, and
fill requests.
For all text and fill requests (for example,
<link linkend="requests:PolyText8"><emphasis role='bold'>PolyText8</emphasis></link>,
<link linkend="requests:PolyText16"><emphasis role='bold'>PolyText16</emphasis></link>,
<link linkend="requests:PolyFillRectangle"><emphasis role='bold'>PolyFillRectangle</emphasis></link>,
<link linkend="requests:FillPoly"><emphasis role='bold'>FillPoly</emphasis></link>,
and
<link linkend="requests:PolyFillArc"><emphasis role='bold'>PolyFillArc</emphasis></link>)
as well as for line requests with line-style
<emphasis role='bold'>Solid</emphasis>,
(for example,
<link linkend="requests:PolyLine"><emphasis role='bold'>PolyLine</emphasis></link>,
<link linkend="requests:PolySegment"><emphasis role='bold'>PolySegment</emphasis></link>,
<link linkend="requests:PolyRectangle"><emphasis role='bold'>PolyRectangle</emphasis></link>,
<link linkend="requests:PolyArc"><emphasis role='bold'>PolyArc</emphasis></link> )
and for the even dashes for line requests with line-style
<emphasis role='bold'>OnOffDash</emphasis>
or
<emphasis role='bold'>DoubleDash</emphasis>:
</para>

<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='2.0*'/>
  <tbody>
    <row>
      <entry>
<emphasis role='bold'>Solid</emphasis>
      </entry>
      <entry>Foreground</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Tiled</emphasis>
      </entry>
      <entry>Tile</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>OpaqueStippled</emphasis>
      </entry>
      <entry>
A tile with the same width and height as stipple
but with background everywhere stipple has a zero
and with foreground everywhere stipple has a one
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Stippled</emphasis>
      </entry>
      <entry>
Foreground masked by stipple
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
For the odd dashes for line requests with line-style
<emphasis role='bold'>DoubleDash</emphasis>:
</para>

<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='2.0*'/>
  <tbody>
    <row>
      <entry>
<emphasis role='bold'>Solid</emphasis>
      </entry>
      <entry>Background</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Tiled</emphasis>
      </entry>
      <entry>Same as for even dashes</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>OpaqueStippled</emphasis>
      </entry>
      <entry>Same as for even dashes</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>Stippled</emphasis>
      </entry>
      <entry>Background masked by stipple</entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>


<para>
The dashes value allowed here is actually a simplified form of the more
general patterns that can be set with
<link linkend="requests:SetDashes"><emphasis role='bold'>SetDashes</emphasis></link>.
Specifying a value of N here is equivalent to specifying
the two element list [N, N] in
<link linkend="requests:SetDashes"><emphasis role='bold'>SetDashes</emphasis></link>.
The value must be nonzero (or a
<emphasis role='bold'>Value</emphasis>
error results).
The meaning of dash-offset and dashes are explained in the
<link linkend="requests:SetDashes"><emphasis role='bold'>SetDashes</emphasis></link>
request.
</para>
<para id="requests:CreateGC:clip-mask">
<indexterm zone="requests:CreateGC:clip-mask"><primary>Clipping region</primary></indexterm>
The clip-mask restricts writes to the destination drawable.
Only pixels where the clip-mask has bits set to 1 are drawn.
Pixels are not drawn outside the area covered by the clip-mask
or where the clip-mask has bits set to 0.
The clip-mask affects all graphics requests,
but it does not clip sources.
The clip-mask origin is interpreted relative to the origin of whatever
destination drawable is specified in a graphics request.
If a pixmap is specified as the clip-mask,
it must have depth 1 and have the same root as the gcontext (or a
<emphasis role='bold'>Match</emphasis>
error results).
If clip-mask is
<emphasis role='bold'>None</emphasis>,
then pixels are always drawn, regardless of the clip origin.
The clip-mask can also be set with the
<link linkend="requests:SetClipRectangles"><emphasis role='bold'>SetClipRectangles</emphasis></link>
request.
</para>
<para>
For
<emphasis role='bold'>ClipByChildren</emphasis>,
both source and destination windows are additionally clipped by all viewable
<emphasis role='bold'>InputOutput</emphasis>
children.
For
<emphasis role='bold'>IncludeInferiors</emphasis>,
neither source nor destination window is clipped by inferiors.
This will result in including subwindow contents in the
source and drawing through subwindow boundaries of the destination.
The use of
<emphasis role='bold'>IncludeInferiors</emphasis>
with a source or destination window of one depth with mapped inferiors
of differing depth is not illegal,
but the semantics is undefined by the core protocol.
</para>
<para id="requests:CreateGC:fill-rule">
<indexterm zone="requests:CreateGC:fill-rule" significance="preferred"><primary>Winding rule</primary></indexterm>
<indexterm zone="requests:CreateGC:fill-rule" significance="preferred"><primary>Fill rule</primary></indexterm>
The fill-rule defines what pixels are inside (that is, are drawn) for
paths given in
<link linkend="requests:FillPoly"><emphasis role='bold'>FillPoly</emphasis></link>
requests.
<emphasis role='bold'>EvenOdd</emphasis>
means a point is inside if an infinite ray with the point as origin crosses
the path an odd number of times.
For
<emphasis role='bold'>Winding</emphasis>,
a point is inside if an infinite ray with the point as origin crosses an
unequal number of clockwise and counterclockwise directed path segments.
A clockwise directed path segment is one that crosses the ray from left
to right as observed from the point.
A counter-clockwise segment is one that crosses the ray from right to left
as observed from the point.
The case where a directed line segment is coincident with the ray is
uninteresting because one can simply choose a different ray that is not
coincident with a segment.
</para>
<para>
For both fill rules,
a point is infinitely small and the path is an infinitely thin line.
A pixel is inside if the center point of the pixel is inside
and the center point is not on the boundary.
If the center point is on the boundary,
the pixel is inside if and only if the polygon interior is immediately
to its right (x increasing direction).
Pixels with centers along a horizontal edge are a special case
and are inside if and only if the polygon interior is immediately below
(y increasing direction).
</para>
<para>
The arc-mode controls filling in the
<link linkend="requests:PolyFillArc"><emphasis role='bold'>PolyFillArc</emphasis></link>
request.
</para>
<para>
The graphics-exposures flag controls
<link linkend="events:GraphicsExposure"><emphasis role='bold'>GraphicsExposure</emphasis></link>
event generation for
<link linkend="requests:CopyArea"><emphasis role='bold'>CopyArea</emphasis></link>
and
<link linkend="requests:CopyPlane"><emphasis role='bold'>CopyPlane</emphasis></link>
requests (and any similar requests defined by extensions).
</para>
<para>
The default component values are:
</para>

<informaltable frame='topbot'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='2.0*'/>
  <thead>
    <row rowsep='1'>
      <entry>Component</entry>
      <entry>Default</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry>function</entry>
      <entry>
<emphasis role='bold'>Copy</emphasis>
      </entry>
    </row>
    <row>
      <entry>plane-mask</entry>
      <entry>all ones</entry>
    </row>
    <row>
      <entry>foreground</entry>
      <entry>0</entry>
    </row>
    <row>
      <entry>background</entry>
      <entry>1</entry>
    </row>
    <row>
      <entry>line-width</entry>
      <entry>0</entry>
    </row>
    <row>
      <entry>line-style</entry>
      <entry><emphasis role='bold'>Solid</emphasis></entry>
    </row>
    <row>
      <entry>cap-style</entry>
      <entry><emphasis role='bold'>Butt</emphasis></entry>
    </row>
    <row>
      <entry>join-style</entry>
      <entry><emphasis role='bold'>Miter</emphasis></entry>
    </row>
    <row>
      <entry>fill-style</entry>
      <entry><emphasis role='bold'>Solid</emphasis></entry>
    </row>
    <row>
      <entry>fill-rule</entry>
      <entry><emphasis role='bold'>EvenOdd</emphasis></entry>
    </row>
    <row>
      <entry>arc-mode</entry>
      <entry><emphasis role='bold'>PieSlice</emphasis></entry>
    </row>
    <row>
      <entry>tile</entry>
      <entry>
<para>
Pixmap of unspecified size filled with foreground pixel
</para>
<para>
(that is, client specified pixel if any, else 0)
</para>
<para>
(subsequent changes to foreground do not affect this pixmap)
</para>
      </entry>
    </row>
    <row>
      <entry>stipple</entry>
      <entry>
Pixmap of unspecified size filled with ones
      </entry>
    </row>
    <row>
      <entry>tile-stipple-x-origin</entry>
      <entry>0</entry>
    </row>
    <row>
      <entry>tile-stipple-y-origin</entry>
      <entry>0</entry>
    </row>
    <row>
      <entry>font</entry>
      <entry>&lt;server-dependent-font&gt;</entry>
    </row>
    <row>
      <entry>subwindow-mode</entry>
      <entry>
<emphasis role='bold'>ClipByChildren</emphasis>
      </entry>
    </row>
    <row>
      <entry>graphics-exposures</entry>
      <entry>
<emphasis role='bold'>True</emphasis>
      </entry>
    </row>
    <row>
      <entry>clip-x-origin</entry>
      <entry>0</entry>
    </row>
    <row>
      <entry>clip-y-origin</entry>
      <entry>0</entry>
    </row>
    <row>
      <entry>clip-mask</entry>
      <entry>
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>dash-offset</entry>
      <entry>0</entry>
    </row>
    <row>
      <entry>dashes</entry>
      <entry>4 (that is, the list [4, 4])</entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
Storing a pixmap in a gcontext might or might not result in a copy
being made.
If the pixmap is later used as the destination for a graphics request,
the change might or might not be reflected in the gcontext.
If the pixmap is used simultaneously in a graphics request
as both a destination and as a tile or stipple,
the results are not defined.
</para>
<para>
It is quite likely that some amount of gcontext information will be
cached in display hardware and that such hardware can only cache a
small number of gcontexts.
Given the number and complexity of components,
clients should view switching between gcontexts with nearly
identical state as significantly more expensive than making minor
changes to a single gcontext.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ChangeGC">
    <title>ChangeGC</title>
    <indexterm zone="requests:ChangeGC" significance="preferred"><primary>ChangeGC</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>gc</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>value-mask</emphasis>: BITMASK
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>value-list</emphasis>: LISTofVALUE
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>Font</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Pixmap</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request changes components in gc.
The value-mask and value-list specify which components are to be changed.
The values and restrictions are the same
as for
<link linkend="requests:CreateGC"><emphasis role='bold'>CreateGC</emphasis></link>.
</para>
<para>
Changing the clip-mask also overrides any previous
<link linkend="requests:SetClipRectangles"><emphasis role='bold'>SetClipRectangles</emphasis></link>
request on the context.
Changing dash-offset or dashes overrides any previous
<link linkend="requests:SetDashes"><emphasis role='bold'>SetDashes</emphasis></link>
request on the context.
</para>
<para>
The order in which components are verified and altered is server-dependent.
If an error is generated,
a subset of the components may have been altered.
<!-- .sp -->
</para>
  </section>
  <section id="requests:CopyGC">
    <title>CopyGC</title>
    <indexterm zone="requests:CopyGC" significance="preferred"><primary>CopyGC</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>src-gc</emphasis>, <emphasis remap='I'>dst-gc</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>value-mask</emphasis>: BITMASK
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request copies components from src-gc to dst-gc.
The value-mask specifies which components to copy, as for
<link linkend="requests:CreateGC"><emphasis role='bold'>CreateGC</emphasis></link>.
The two gcontexts must have the same root and the same depth (or a
<emphasis role='bold'>Match</emphasis>
error results).
<!-- .sp -->
</para>
  </section>
  <section id="requests:SetDashes">
    <title>SetDashes</title>
    <indexterm zone="requests:SetDashes" significance="preferred"><primary>SetDashes</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>gc</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>dash-offset</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>dashes</emphasis>: LISTofCARD8
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request sets dash-offset and dashes in gc for dashed line styles.
Dashes cannot be empty (or a
<emphasis role='bold'>Value</emphasis>
error results).
Specifying an odd-length list is equivalent to specifying the same list
concatenated with itself to produce an even-length list.
The initial and alternating elements of dashes are the even dashes;
the others are the odd dashes.
Each element specifies a dash length in pixels.
All of the elements must be nonzero (or a
<emphasis role='bold'>Value</emphasis>
error results).
The dash-offset defines the phase of the pattern,
specifying how many pixels into dashes the pattern should actually begin in
any single graphics request.
Dashing is continuous through path elements combined with a join-style
but is reset to the dash-offset between each sequence of joined lines.
</para>
<para>
The unit of measure for dashes is the same as in the ordinary
coordinate system.
Ideally, a dash length is measured along the slope of the line,
but implementations are only required to match this ideal
for horizontal and vertical lines.
Failing the ideal semantics,
it is suggested that the length be measured along the major axis of the line.
The major axis is defined as the x axis for lines drawn at an angle of
between -45 and +45 degrees or between 135 and 225 degrees from the x axis.
For all other lines, the major axis is the y axis.
</para>
<para>
For any graphics primitive, the computation of the endpoint of an individual
dash only depends on the geometry of the primitive, the start position
of the dash, the direction of the dash, and the dash length.
</para>
<para>
For any graphics primitive, the total set of pixels used to render the
primitive (both even and odd numbered dash elements) with
<emphasis role='bold'>DoubleDash</emphasis>
line-style is the same as the set of pixels used to render the
primitive with
<emphasis role='bold'>Solid</emphasis>
line-style.
</para>
<para>
For any graphics primitive, if the primitive is drawn with
<emphasis role='bold'>OnOffDash</emphasis>
or
<emphasis role='bold'>DoubleDash</emphasis>
line-style unclipped at position [x,y] and again at position
[x+dx,y+dy], then a point [x1,y1] is included in a dash in the first
instance if and only if the point [x1+dx,y1+dy] is included in the dash in
the second instance.  In addition, the effective set of points comprising a
dash cannot be affected by clipping.  A point is included in a clipped dash
if and only if the point lies inside the clipping region and the point
would be included in the dash when drawn unclipped.
<!-- .sp -->
</para>
  </section>
  <section id="requests:SetClipRectangles">
    <title>SetClipRectangles</title>
    <indexterm zone="requests:SetClipRectangles" significance="preferred"><primary>SetClipRectangles</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>gc</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>clip-x-origin</emphasis>, <emphasis remap='I'>clip-y-origin</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>rectangles</emphasis>: LISTofRECTANGLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>ordering</emphasis>:
{ <emphasis role='bold'>UnSorted</emphasis>,
<emphasis role='bold'>YSorted</emphasis>,
<emphasis role='bold'>YXSorted</emphasis>,
<emphasis role='bold'>YXBanded</emphasis>}
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request changes clip-mask in gc to the specified list of rectangles
and sets the clip origin.
Output will be clipped to remain contained within the rectangles.
The clip origin is interpreted relative to the origin of
whatever destination drawable is specified in a graphics request.
The rectangle coordinates are interpreted relative to the clip origin.
The rectangles should be nonintersecting, or graphics results will be
undefined.
Note that the list of rectangles can be empty,
which effectively disables output.
This is the opposite of passing
<emphasis role='bold'>None</emphasis>
as the clip-mask in
<link linkend="requests:CreateGC"><emphasis role='bold'>CreateGC</emphasis></link>
and
<link linkend="requests:ChangeGC"><emphasis role='bold'>ChangeGC</emphasis></link>.
</para>
<para>
If known by the client,
ordering relations on the rectangles can be specified with the ordering
argument.
This may provide faster operation by the server.
If an incorrect ordering is specified,
the server may generate a
<emphasis role='bold'>Match</emphasis>
error, but it is not required to do so.
If no error is generated,
the graphics results are undefined.
<emphasis role='bold'>UnSorted</emphasis>
means that the rectangles are in arbitrary order.
<emphasis role='bold'>YSorted</emphasis>
means that the rectangles are nondecreasing in their Y origin.
<emphasis role='bold'>YXSorted</emphasis>
additionally constrains
<emphasis role='bold'>YSorted</emphasis>
order in that all rectangles with an equal Y origin are
nondecreasing in their X origin.
<emphasis role='bold'>YXBanded</emphasis>
additionally constrains
<emphasis role='bold'>YXSorted</emphasis>
by requiring that, for every possible Y scanline,
all rectangles that include that scanline have identical Y origins and Y
extents.
<!-- .sp -->
</para>
  </section>
  <section id="requests:FreeGC">
    <title>FreeGC</title>
    <indexterm zone="requests:FreeGC" significance="preferred"><primary>FreeGC</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>gc</emphasis>: GCONTEXT
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>GContext</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request deletes the association between the resource ID and the gcontext
and destroys the gcontext.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ClearArea">
    <title>ClearArea</title>
    <indexterm zone="requests:ClearArea" significance="preferred"><primary>ClearArea</primary></indexterm>
    <indexterm zone="requests:ClearArea"><primary>Background</primary></indexterm><informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>x</emphasis>, <emphasis remap='I'>y</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>width</emphasis>, <emphasis remap='I'>height</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>exposures</emphasis>: BOOL
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Value</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
The x and y coordinates are relative to the window's origin
and specify the upper-left corner of the rectangle.
If width is zero,
it is replaced with the current width of the window minus x.
If height is zero,
it is replaced with the current height of the window minus y.
If the window has a defined background tile,
the rectangle is tiled with a plane-mask of all ones and function of
<emphasis role='bold'>Copy</emphasis>
and a subwindow-mode of
<emphasis role='bold'>ClipByChildren</emphasis>.
If the window has background
<emphasis role='bold'>None</emphasis>,
the contents of the window are not changed.
In either case,
if exposures is
<emphasis role='bold'>True</emphasis>,
then one or more exposure events are generated for regions of the rectangle
that are either visible or are being retained in a backing store.
</para>
<para>
It is a
<emphasis role='bold'>Match</emphasis>
error to use an
<emphasis role='bold'>InputOnly</emphasis>
window in this request.
<!-- .sp -->
</para>
  </section>
  <section id="requests:CopyArea">
    <title>CopyArea</title>
    <indexterm zone="requests:CopyArea" significance="preferred"><primary>CopyArea</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>src-drawable</emphasis>, <emphasis remap='I'>dst-drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>gc</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>src-x</emphasis>, <emphasis remap='I'>src-y</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>width</emphasis>, <emphasis remap='I'>height</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>dst-x</emphasis>, <emphasis remap='I'>dst-y</emphasis>: INT16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Match</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request combines the specified rectangle of src-drawable with the
specified rectangle of dst-drawable.
The src-x and src-y coordinates are relative to src-drawable's origin.
The dst-x and dst-y are relative to dst-drawable's origin,
each pair specifying the upper-left corner of the rectangle.
The src-drawable must have the same root and the same depth
as dst-drawable (or a
<emphasis role='bold'>Match</emphasis>
error results).
</para>
<para>
If regions of the source rectangle are obscured and have not been retained
in backing store
or if regions outside the boundaries of the source drawable are specified,
then those regions are not copied,
but the following occurs on all corresponding destination regions that are
either visible or are retained in backing-store.
If the dst-drawable is a window with a background other than
<emphasis role='bold'>None</emphasis>,
these corresponding destination regions are tiled
(with plane-mask of all ones and function
<emphasis role='bold'>Copy</emphasis>)
with that background.
Regardless of tiling and whether the destination is a window or a pixmap,
if graphics-exposures in gc is
<emphasis role='bold'>True</emphasis>,
then
<link linkend="events:GraphicsExposure"><emphasis role='bold'>GraphicsExposure</emphasis></link>
events for all corresponding destination regions are generated.
</para>
<para>
If graphics-exposures is
<emphasis role='bold'>True</emphasis>
but no
<emphasis role='bold'>GraphicsExposure</emphasis>
events are generated,
then a
<link linkend="events:NoExposure"><emphasis role='bold'>NoExposure</emphasis></link>
event is generated.
</para>
<para>
GC components: function, plane-mask, subwindow-mode,
graphics-exposures, clip-x-origin, clip-y-origin, clip-mask
<!-- .sp -->
</para>
  </section>
  <section id="requests:CopyPlane">
    <title>CopyPlane</title>
    <indexterm zone="requests:CopyPlane" significance="preferred"><primary>CopyPlane</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>src-drawable</emphasis>, <emphasis remap='I'>dst-drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>gc</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>src-x</emphasis>, <emphasis remap='I'>src-y</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>width</emphasis>, <emphasis remap='I'>height</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>dst-x</emphasis>, <emphasis remap='I'>dst-y</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>bit-plane</emphasis>: CARD32
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
The src-drawable must have the same root as dst-drawable (or a
<emphasis role='bold'>Match</emphasis>
error results), but it need not have the same depth.
The bit-plane must have exactly one bit set to 1 and the value of bit-plane
must be less than %2 sup n% where <emphasis remap='I'>n</emphasis> is the depth of src-drawable (or a
<emphasis role='bold'>Value</emphasis>
error results).
Effectively, a pixmap of the same depth as dst-drawable and with size specified
by the source region is formed using the foreground/background pixels in gc
(foreground everywhere the bit-plane in src-drawable contains a bit set to 1,
background everywhere the bit-plane contains a bit set to 0),
and the equivalent of a
<link linkend="requests:CopyArea"><emphasis role='bold'>CopyArea</emphasis></link>
is performed, with all the same exposure semantics.
This can also be thought of as using the specified region of the source
bit-plane as a stipple with a fill-style of
<emphasis role='bold'>OpaqueStippled</emphasis>
for filling a rectangular area of the destination.
</para>
<para>
GC components: function, plane-mask, foreground, background,
subwindow-mode, graphics-exposures, clip-x-origin, clip-y-origin,
clip-mask
<!-- .sp -->
</para>
  </section>
  <section id="requests:PolyPoint">
    <title>PolyPoint</title>
    <indexterm zone="requests:PolyPoint" significance="preferred"><primary>PolyPoint</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>gc</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>coordinate-mode</emphasis>:
{ <emphasis role='bold'>Origin</emphasis>,
<emphasis role='bold'>Previous</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>points</emphasis>: LISTofPOINT
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request combines the foreground pixel in gc with the pixel
at each point in the drawable.
The points are drawn in the order listed.
</para>
<para>
The first point is always relative to the drawable's origin.
The rest are relative either to that origin or the previous point,
depending on the coordinate-mode.
</para>
<para>
GC components: function, plane-mask, foreground, subwindow-mode,
clip-x-origin, clip-y-origin, clip-mask
<!-- .sp -->
</para>
  </section>
  <section id="requests:PolyLine">
    <title>PolyLine</title>
    <indexterm zone="requests:PolyLine" significance="preferred"><primary>PolyLine</primary></indexterm>
    <indexterm zone="requests:PolyLine"><primary>Line</primary><secondary>drawing</secondary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>gc</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>coordinate-mode</emphasis>:
{ <emphasis role='bold'>Origin</emphasis>,
<emphasis role='bold'>Previous</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>points</emphasis>: LISTofPOINT
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request draws lines between each pair of points (point[i], point[i+1]).
The lines are drawn in the order listed.
The lines join correctly at all intermediate points,
and if the first and last points coincide,
the first and last lines also join correctly.
</para>
<para>
For any given line,
no pixel is drawn more than once.
If thin (zero line-width) lines intersect,
the intersecting pixels are drawn multiple times.
If wide lines intersect,
the intersecting pixels are drawn only once, as though the entire
<emphasis role='bold'>PolyLine</emphasis>
were a single filled shape.
</para>
<para>
The first point is always relative to the drawable's origin.
The rest are relative either to that origin or the previous point,
depending on the coordinate-mode.
</para>
<para>
When either of the two lines involved in a
<emphasis role='bold'>Bevel</emphasis>
join is neither vertical
nor horizontal, then the slope and position of the line segment defining
the bevel join edge is implementation dependent.  However, the computation
of the slope and distance (relative to the join point) only depends on
the line width and the slopes of the two lines.
</para>
<para>
GC components: function, plane-mask, line-width, line-style,
cap-style, join-style, fill-style, subwindow-mode, clip-x-origin,
clip-y-origin, clip-mask
</para>
<para>
GC mode-dependent components: foreground, background, tile, stipple,
tile-stipple-x-origin, tile-stipple-y-origin, dash-offset, dashes
<!-- .sp -->
</para>
  </section>
  <section id="requests:PolySegment">
    <title>PolySegment</title>
    <indexterm zone="requests:PolySegment" significance="preferred"><primary>PolySegment</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>gc</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>segments</emphasis>: LISTofSEGMENT
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
where:
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
SEGMENT: [x1, y1, x2, y2: INT16]
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Match</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
For each segment,
this request draws a line between [x1, y1] and [x2, y2].
The lines are drawn in the order listed.
No joining is performed at coincident endpoints.
For any given line,
no pixel is drawn more than once.
If lines intersect,
the intersecting pixels are drawn multiple times.
</para>
<para>
GC components: function, plane-mask, line-width, line-style,
cap-style, fill-style, subwindow-mode, clip-x-origin, clip-y-origin,
clip-mask
</para>
<para>
GC mode-dependent components: foreground, background, tile, stipple,
tile-stipple-x-origin, tile-stipple-y-origin, dash-offset, dashes
<!-- .sp -->
</para>
  </section>
  <section id="requests:PolyRectangle">
    <title>PolyRectangle</title>
    <indexterm zone="requests:PolyRectangle" significance="preferred"><primary>PolyRectangle</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>gc</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>rectangles</emphasis>: LISTofRECTANGLE
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Match</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request draws the outlines of the specified rectangles, as if a five-point
<link linkend="requests:PolyLine"><emphasis role='bold'>PolyLine</emphasis></link>
were specified for each rectangle:
</para>
<para>
<literallayout class="monospaced">
	[x,y] [x+width,y] [x+width,y+height] [x,y+height] [x,y]
</literallayout>
</para>
<para>
The x and y coordinates of each rectangle are relative to the drawable's origin
and define the upper-left corner of the rectangle.
</para>
<para>
The rectangles are drawn in the order listed.
For any given rectangle,
no pixel is drawn more than once.
If rectangles intersect,
the intersecting pixels are drawn multiple times.
</para>
<para>
GC components: function, plane-mask, line-width, line-style,
cap-style, join-style, fill-style, subwindow-mode, clip-x-origin,
clip-y-origin, clip-mask
</para>
<para>
GC mode-dependent components: foreground, background, tile, stipple,
tile-stipple-x-origin, tile-stipple-y-origin, dash-offset, dashes
<!-- .sp -->
</para>
  </section>
  <section id="requests:PolyArc">
    <title>PolyArc</title>
    <indexterm zone="requests:PolyArc" significance="preferred"><primary>PolyArc</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>gc</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>arcs</emphasis>: LISTofARC
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Match</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request draws circular or elliptical arcs.
Each arc is specified by a rectangle and two angles.
The angles are signed integers in degrees scaled by 64,
with positive indicating counterclockwise motion and
negative indicating clockwise motion.
The start of the arc is specified by angle1 relative to the three-o'clock
position from the center of the rectangle,
and the path and extent of the arc is specified by angle2 relative to the
start of the arc.
If the magnitude of angle2 is greater than 360 degrees,
it is truncated to 360 degrees.
The x and y coordinates of the rectangle are relative to the origin of
the drawable.
For an arc specified as [x,y,w,h,a1,a2],
the origin of the major and minor axes is at [x+(w/2),y+(h/2)],
and the infinitely thin path describing the entire circle/ellipse intersects
the horizontal axis at [x,y+(h/2)] and [x+w,y+(h/2)] and intersects the
vertical axis at [x+(w/2),y] and [x+(w/2),y+h].
These coordinates are not necessarily integral; that is,
they are not truncated to discrete coordinates.
</para>
<para>
For a wide line with line-width lw, the ideal bounding outlines for filling
are given by the two infinitely thin paths consisting of all points whose
perpendicular distance from a tangent to the path of the circle/ellipse is
equal to lw/2 (which may be a fractional value).  When the width and height
of the arc are not equal and both are nonzero, then the actual bounding
outlines are implementation dependent.  However, the computation of the
shape and position of the bounding outlines (relative to the center of the
arc) only depends on the width and height of the arc and the
line-width.
</para>
<para>
The cap-style is applied the same as for a line corresponding to the
tangent of the circle/ellipse at the endpoint.  When the angle of an arc
face is not an integral multiple of 90 degrees, and the width and height of
the arc are both are nonzero, then the shape and position of the cap at
that face is implementation dependent.  However, for a
<emphasis role='bold'>Butt</emphasis>
cap, the face
is defined by a straight line, and the computation of the position
(relative to the center of the arc) and the slope of the line only
depends on the width and height of the arc and the angle of the arc face.
For other cap styles, the computation of the position (relative to the
center of the arc) and the shape of the cap only depends on the width
and height of the arc, the line-width, the angle of the arc face, and the
direction (clockwise or counter clockwise) of the arc from the endpoint.
</para>
<para>
The join-style is applied the same as for two lines corresponding to the
tangents of the circles/ellipses at the join point.  When the width and
height of both arcs are nonzero, and the angle of either arc face is not an
integral multiple of 90 degrees, then the shape of the join is
implementation dependent.  However, the computation of the shape only
depends on the width and height of each arc, the line-width, the angles of
the two arc faces, the direction (clockwise or counter clockwise) of the
arcs from the join point, and the relative orientation of the two arc
center points.
</para>
<para>
For an arc specified as [x,y,w,h,a1,a2],
the angles must be specified in the effectively skewed coordinate system of
the ellipse (for a circle, the angles and coordinate systems are identical).
The relationship between these angles and angles expressed in the normal
coordinate system of the screen (as measured with a protractor) is as
follows:
<literallayout class="monospaced">
	skewed-angle = atan(tan(normal-angle) * w/h) + adjust
</literallayout>
</para>
<para>
The skewed-angle and normal-angle are expressed in radians (rather
than in degrees scaled by 64) in the range [0,2*PI).
The atan returns a value in the range [-PI/2,PI/2].
The adjust is:
<informaltable frame="none">
  <?dbfo keep-together="always" ?>
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
    <colspec colname='c1' colwidth='1.0*'/>
    <colspec colname='c2' colwidth='2.0*'/>
    <tbody>
      <row>
	<entry>0</entry>
	<entry>for normal-angle in the range [0,PI/2)</entry>
      </row>
      <row>
	<entry>PI</entry>
	<entry>for normal-angle in the range [PI/2,(3*PI)/2)</entry>
      </row>
      <row>
	<entry>2*PI</entry>
	<entry>for normal-angle in the range [(3*PI)/2,2*PI)</entry>
      </row>
    </tbody>
  </tgroup>
</informaltable>
</para>
<para>
The arcs are drawn in the order listed.
If the last point in one arc coincides with the first point in the following
arc,
the two arcs will join correctly.
If the first point in the first arc coincides with the last point
in the last arc,
the two arcs will join correctly.
For any given arc,
no pixel is drawn more than once.
If two arcs join correctly and the line-width is greater than zero
and the arcs intersect,
no pixel is drawn more than once.
Otherwise, the intersecting pixels of intersecting arcs are drawn multiple
times.
Specifying an arc with one endpoint and a clockwise extent draws the
same pixels as specifying the other endpoint and an equivalent
counterclockwise extent, except as it affects joins.
</para>
<para>
By specifying one axis to be zero,
a horizontal or vertical line can be drawn.
</para>
<para>
Angles are computed based solely on the coordinate system,
ignoring the aspect ratio.
</para>
<para>
GC components: function, plane-mask, line-width, line-style,
cap-style, join-style, fill-style, subwindow-mode, clip-x-origin,
clip-y-origin, clip-mask
</para>
<para>
GC mode-dependent components: foreground, background, tile, stipple,
tile-stipple-x-origin, tile-stipple-y-origin, dash-offset, dashes
<!-- .sp -->
</para>
  </section>
  <section id="requests:FillPoly">
    <title>FillPoly</title>
    <indexterm zone="requests:FillPoly" significance="preferred"><primary>FillPoly</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2in -->
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>gc</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>shape</emphasis>:
{ <emphasis role='bold'>Complex</emphasis>,
<emphasis role='bold'>Nonconvex</emphasis>,
<emphasis role='bold'>Convex</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>coordinate-mode</emphasis>:
{ <emphasis role='bold'>Origin</emphasis>,
<emphasis role='bold'>Previous</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>points</emphasis>: LISTofPOINT
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request fills the region closed by the specified path.
The path is closed automatically if the last point in the list does not
coincide with the first point.
No pixel of the region is drawn more than once.
</para>
<para>
The first point is always relative to the drawable's origin.
The rest are relative either to that origin or the previous point,
depending on the coordinate-mode.
</para>
<para>
The shape parameter may be used by the server to improve performance.
<emphasis role='bold'>Complex</emphasis>
means the path may self-intersect.
Contiguous coincident points in the path are not treated
as self-intersection.
</para>
<para>
<emphasis role='bold'>Nonconvex</emphasis>
means the path does not self-intersect,
but the shape is not wholly convex.
If known by the client,
specifying
<emphasis role='bold'>Nonconvex</emphasis>
over
<emphasis role='bold'>Complex</emphasis>
may improve performance.
If
<emphasis role='bold'>Nonconvex</emphasis>
is specified for a self-intersecting path,
the graphics results are undefined.
</para>
<para>
<emphasis role='bold'>Convex</emphasis>
means that for every pair of points inside the polygon,
the line segment connecting them does not intersect the path.
If known by the client,
specifying
<emphasis role='bold'>Convex</emphasis>
can improve performance.
If
<emphasis role='bold'>Convex</emphasis>
is specified for a path that is not convex,
the graphics results are undefined.
</para>
<para>
GC components: function, plane-mask, fill-style, fill-rule,
subwindow-mode, clip-x-origin, clip-y-origin, clip-mask
</para>
<para>
GC mode-dependent components: foreground, background, tile, stipple,
tile-stipple-x-origin, tile-stipple-y-origin
<!-- .sp -->
</para>
  </section>
  <section id="requests:PolyFillRectangle">
    <title>PolyFillRectangle</title>
    <indexterm zone="requests:PolyFillRectangle" significance="preferred"><primary>PolyFillRectangle</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>gc</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>rectangles</emphasis>: LISTofRECTANGLE
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Match</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request fills the specified rectangles, as if a four-point
<link linkend="requests:FillPoly"><emphasis role='bold'>FillPoly</emphasis></link>
were specified for each rectangle:
<literallayout class="monospaced">
	[x,y] [x+width,y] [x+width,y+height] [x,y+height]
</literallayout>
</para>
<para>
The x and y coordinates of each rectangle are relative to the drawable's origin
and define the upper-left corner of the rectangle.
</para>
<para>
The rectangles are drawn in the order listed.
For any given rectangle,
no pixel is drawn more than once.
If rectangles intersect,
the intersecting pixels are drawn multiple times.
</para>
<para>
GC components: function, plane-mask, fill-style, subwindow-mode,
clip-x-origin, clip-y-origin, clip-mask
</para>
<para>
GC mode-dependent components: foreground, background, tile, stipple,
tile-stipple-x-origin, tile-stipple-y-origin
<!-- .sp -->
</para>
  </section>
  <section id="requests:PolyFillArc">
    <title>PolyFillArc</title>
    <indexterm zone="requests:PolyFillArc" significance="preferred"><primary>PolyFillArc</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>gc</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>arcs</emphasis>: LISTofARC
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Match</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
For each arc,
this request fills the region closed by the infinitely thin path
described by the specified arc and one or two line segments,
depending on the arc-mode.
For
<emphasis role='bold'>Chord</emphasis>,
the single line segment joining the endpoints of the arc is used.
For
<emphasis role='bold'>PieSlice</emphasis>,
the two line segments joining the endpoints of the arc with the center point
are used.
</para>
<para>
For an arc specified as [x,y,w,h,a1,a2], the origin of the major and minor
axes is at [x+(w/2),y+(h/2)], and the infinitely thin path describing the
entire circle/ellipse intersects the horizontal axis at [x,y+(h/2)] and
[x+w,y+(h/2)] and intersects the vertical axis at [x+(w/2),y] and
[x+(w/2),y+h].  These coordinates are not necessarily integral; that is,
they are not truncated to discrete coordinates.
</para>
<para>
The arc angles are interpreted as specified in the
<link linkend="requests:PolyArc"><emphasis role='bold'>PolyArc</emphasis></link>
request.  When
the angle of an arc face is not an integral multiple of 90 degrees, then
the precise endpoint on the arc is implementation dependent.  However, for
<emphasis role='bold'>Chord</emphasis>
arc-mode, the computation of the pair of endpoints (relative to the
center of the arc) only depends on the width and height of the arc and
the angles of the two arc faces.  For
<emphasis role='bold'>PieSlice</emphasis>
arc-mode, the computation of
an endpoint only depends on the angle of the arc face for that
endpoint and the ratio of the arc width to arc height.
</para>
<para>
The arcs are filled in the order listed.
For any given arc,
no pixel is drawn more than once.
If regions intersect,
the intersecting pixels are drawn multiple times.
</para>
<para>
GC components: function, plane-mask, fill-style, arc-mode,
subwindow-mode, clip-x-origin, clip-y-origin, clip-mask
</para>
<para>
GC mode-dependent components: foreground, background, tile, stipple,
tile-stipple-x-origin, tile-stipple-y-origin
<!-- .sp -->
</para>
  </section>
  <section id="requests:PutImage">
    <title>PutImage</title>
    <indexterm zone="requests:PutImage" significance="preferred"><primary>PutImage</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>gc</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>depth</emphasis>: CARD8
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>width</emphasis>, <emphasis remap='I'>height</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>dst-x</emphasis>, <emphasis remap='I'>dst-y</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>left-pad</emphasis>: CARD8
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>format</emphasis>:
{ <emphasis role='bold'>Bitmap</emphasis>,
<emphasis role='bold'>XYPixmap</emphasis>,
<emphasis role='bold'>ZPixmap</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>data</emphasis>: LISTofBYTE
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request combines an image with a rectangle of the drawable.
The dst-x and dst-y coordinates are relative to the drawable's origin.
</para>
<para>
If
<emphasis role='bold'>Bitmap</emphasis>
format is used,
then depth must be one (or a
<emphasis role='bold'>Match</emphasis>
error results), and the image must be in XY format.
The foreground pixel in gc defines the source for bits set to 1 in the image,
and the background pixel defines the source for the bits set to 0.
</para>
<para>
For
<emphasis role='bold'>XYPixmap</emphasis>
and
<emphasis role='bold'>ZPixmap</emphasis>,
the depth must match the depth of the drawable (or a
<emphasis role='bold'>Match</emphasis>
error results).
For
<emphasis role='bold'>XYPixmap</emphasis>,
the image must be sent in XY format.
For
<emphasis role='bold'>ZPixmap</emphasis>,
the image must be sent in the Z format defined for the given depth.
</para>
<para>
The left-pad must be zero for
<emphasis role='bold'>ZPixmap</emphasis>
format (or a
<emphasis role='bold'>Match</emphasis>
error results).
For
<emphasis role='bold'>Bitmap</emphasis>
and
<emphasis role='bold'>XYPixmap</emphasis>
format,
left-pad must be less than bitmap-scanline-pad as given in the server
connection setup information (or a
<emphasis role='bold'>Match</emphasis>
error results).
The first left-pad bits in every scanline are to be ignored by the server.
The actual image begins that many bits into the data.
The width argument defines the width of the actual image
and does not include left-pad.
</para>
<para>
GC components: function, plane-mask, subwindow-mode, clip-x-origin,
clip-y-origin, clip-mask
</para>
<para>
GC mode-dependent components: foreground, background
<!-- .sp -->
</para>
  </section>
  <section id="requests:GetImage">
    <title>GetImage</title>
    <indexterm zone="requests:GetImage" significance="preferred"><primary>GetImage</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>x</emphasis>, <emphasis remap='I'>y</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>width</emphasis>, <emphasis remap='I'>height</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>plane-mask</emphasis>: CARD32
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>format</emphasis>:
{ <emphasis role='bold'>XYPixmap</emphasis>,
<emphasis role='bold'>ZPixmap</emphasis>}
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
depth: CARD8
      </entry>
    </row>
    <row>
      <entry>
visual: VISUALID or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
data: LISTofBYTE
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the contents of the given rectangle of the drawable in the
given format.
The x and y coordinates are relative to the drawable's origin
and define the upper-left corner of the rectangle.
If
<emphasis role='bold'>XYPixmap</emphasis>
is specified,
only the bit planes specified in plane-mask are transmitted,
with the planes appearing from most significant to least significant
in bit order.
If
<emphasis role='bold'>ZPixmap</emphasis>
is specified, then bits in all planes not specified in plane-mask are
transmitted as zero.
Range checking is not performed on plane-mask;
extraneous bits are simply ignored.
The returned depth is as specified when the drawable was created
and is the same as a depth component in a FORMAT structure (in the connection
setup), not a bits-per-pixel component.
If the drawable is a window,
its visual type is returned.
If the drawable is a pixmap,
the visual is
<emphasis role='bold'>None</emphasis>.
</para>
<para>
If the drawable is a pixmap,
then the given rectangle must be wholly contained within the pixmap (or a
<emphasis role='bold'>Match</emphasis>
error results).
If the drawable is a window,
the window must be viewable,
and it must be the case that,
if there were no inferiors or overlapping windows,
the specified rectangle of the window would be fully visible on the screen
and wholly contained within the outside edges of the window (or a
<emphasis role='bold'>Match</emphasis>
error results).
Note that the borders of the window can be included and read with this request.
If the window has a backing store,
then the backing-store contents are returned for regions of the window
that are obscured by noninferior windows;
otherwise, the returned contents of such obscured regions are undefined.
Also undefined are the returned contents of visible
regions of inferiors of different depth than the specified window.
The pointer cursor image is not included in the contents returned.
</para>
<para>
This request is not general-purpose in the same sense as other
graphics-related requests.
It is intended specifically for rudimentary hardcopy support.
<!-- .sp -->
</para>
  </section>
  <section id="requests:PolyText8">
    <title>PolyText8</title>
    <indexterm zone="requests:PolyText8" significance="preferred"><primary>PolyText8</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='3' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='1.0*'/>
  <colspec colname='c3' colwidth='1.0*'/>
  <spanspec spanname='all' namest='c1' nameend='c3'/>
  <tbody>
    <row>
      <entry spanname='all'>
<!-- .in +.2i -->
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry spanname='all'>
<emphasis remap='I'>gc</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry spanname='all'>
<emphasis remap='I'>x</emphasis>, <emphasis remap='I'>y</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry spanname='all'>
<emphasis remap='I'>items</emphasis>: LISTofTEXTITEM8
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry valign='top' morerows='3'>
<!-- .in +.2i -->
where:
      </entry>
    </row>
    <row>
      <entry>
TEXTITEM8:
      </entry>
      <entry>
TEXTELT8 or FONT
      </entry>
    </row>
    <row>
      <entry valign='top' morerows='1'>
TEXTELT8:
      </entry>
      <entry>
[delta: INT8
      </entry>
    </row>
    <row>
      <entry>
string: STRING8]
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry spanname='all'>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>Font</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Match</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
The x and y coordinates are relative to the drawable's origin
and specify the baseline starting position (the initial character origin).
Each text item is processed in turn.
A font item causes the font to be stored in gc
and to be used for subsequent text.
Switching among fonts does not affect the next character origin.
A text element delta specifies an additional change in the position
along the x axis before the string is drawn;
the delta is always added to the character origin.
Each character image, as defined by the font in gc,
is treated as an additional mask for a fill operation on the drawable.
</para>
<para>
All contained FONTs are always transmitted most significant byte first.
</para>
<para>
If a
<emphasis role='bold'>Font</emphasis>
error is generated for an item,
the previous items may have been drawn.
</para>
<para>
For fonts defined with 2-byte matrix indexing,
each STRING8 byte is interpreted as a byte2 value of a CHAR2B with a byte1
value of zero.
</para>
<para>
GC components: function, plane-mask, fill-style, font,
subwindow-mode, clip-x-origin, clip-y-origin, clip-mask
</para>
<para>
GC mode-dependent components: foreground, background, tile, stipple,
tile-stipple-x-origin, tile-stipple-y-origin
<!-- .sp -->
</para>
  </section>
  <section id="requests:PolyText16">
    <title>PolyText16</title>
    <indexterm zone="requests:PolyText16" significance="preferred"><primary>PolyText16</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='3' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='1.0*'/>
  <colspec colname='c3' colwidth='1.0*'/>
  <spanspec spanname='all' namest='c1' nameend='c3'/>
  <tbody>
    <row>
      <entry spanname='all'>
<!-- .in +.2i -->
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry spanname='all'>
<emphasis remap='I'>gc</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry spanname='all'>
<emphasis remap='I'>x</emphasis>, <emphasis remap='I'>y</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry spanname='all'>
<emphasis remap='I'>items</emphasis>: LISTofTEXTITEM16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry valign='top' morerows='3'>
<!-- .in +.2i -->
where:
      </entry>
    </row>
    <row>
      <entry>
TEXTITEM16:
      </entry>
      <entry>
TEXTELT16 or FONT
      </entry>
    </row>
    <row>
      <entry valign='top' morerows='1'>
TEXTELT16:
      </entry>
      <entry>
[delta: INT8
      </entry>
    </row>
    <row>
      <entry>
string: STRING16]
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry spanname='all'>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>Font</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Match</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request is similar to
<link linkend="requests:PolyText8"><emphasis role='bold'>PolyText8</emphasis></link>,
except 2-byte (or 16-bit) characters are used.
For fonts defined with linear indexing rather than 2-byte matrix indexing,
the server will interpret each CHAR2B as a 16-bit number that
has been transmitted most significant byte first (that is, byte1 of the
CHAR2B is taken as the most significant byte).
<!-- .sp -->
</para>
  </section>
  <section id="requests:ImageText8">
    <title>ImageText8</title>
    <indexterm zone="requests:ImageText8" significance="preferred"><primary>ImageText8</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>gc</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>x</emphasis>, <emphasis remap='I'>y</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>string</emphasis>: STRING8
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Match</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
The x and y coordinates are relative to the drawable's origin
and specify the baseline starting position (the initial character origin).
The effect is first to fill a destination rectangle with the background
pixel defined in gc and then to paint the text with the foreground pixel.
The upper-left corner of the filled rectangle is at:
<literallayout class="monospaced">
	[x, y - font-ascent]
</literallayout>
</para>
<para>
the width is:
<literallayout class="monospaced">
	overall-width
</literallayout>
</para>
<para>
and the height is:
<literallayout class="monospaced">
	font-ascent + font-descent
</literallayout>
</para>
<para>
The overall-width, font-ascent, and font-descent are as
they would be returned by a
<link linkend="requests:QueryTextExtents"><emphasis role='bold'>QueryTextExtents</emphasis></link>
call using gc and string.
</para>
<para>
The function and fill-style defined in gc are ignored for this request.
The effective function is
<emphasis role='bold'>Copy</emphasis>,
and the effective fill-style
<emphasis role='bold'>Solid</emphasis>.
</para>
<para>
For fonts defined with 2-byte matrix indexing,
each STRING8 byte is interpreted as a byte2 value of a CHAR2B with a byte1
value of zero.
</para>
<para>
GC components: plane-mask, foreground, background, font,
subwindow-mode, clip-x-origin, clip-y-origin, clip-mask
<!-- .sp -->
</para>
  </section>
  <section id="requests:ImageText16">
    <title>ImageText16</title>
    <indexterm zone="requests:ImageText16" significance="preferred"><primary>ImageText16</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>gc</emphasis>: GCONTEXT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>x</emphasis>, <emphasis remap='I'>y</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>string</emphasis>: STRING16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>GContext</emphasis>,
<emphasis role='bold'>Match</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request is similar to
<link linkend="requests:ImageText8"><emphasis role='bold'>ImageText8</emphasis></link>,
except 2-byte (or 16-bit) characters are used.
For fonts defined with linear indexing rather than 2-byte matrix indexing,
the server will interpret each CHAR2B as a 16-bit number that
has been transmitted most significant byte first (that is, byte1 of the
CHAR2B is taken as the most significant byte).
<!-- .sp -->
</para>
  </section>
  <section id="requests:CreateColormap">
    <title>CreateColormap</title>
    <indexterm zone="requests:CreateColormap" significance="preferred"><primary>CreateColormap</primary></indexterm>
    <indexterm zone="requests:CreateColormap"><primary>Colormap</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>mid</emphasis>: COLORMAP
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>visual</emphasis>: VISUALID
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>alloc</emphasis>:
{ <emphasis role='bold'>None</emphasis>,
<emphasis role='bold'>All</emphasis>}
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>IDChoice</emphasis>,
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Value</emphasis>,
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request creates a colormap of the specified visual type for the screen
on which the window resides and associates the identifier mid with it.
The visual type must be one supported by the screen (or a
<emphasis role='bold'>Match</emphasis>
error results).
The initial values of the colormap entries are undefined for classes
<emphasis role='bold'>GrayScale</emphasis>,
<emphasis role='bold'>PseudoColor</emphasis>,
and
<emphasis role='bold'>DirectColor</emphasis>.
For
<emphasis role='bold'>StaticGray</emphasis>,
<emphasis role='bold'>StaticColor</emphasis>,
and
<emphasis role='bold'>TrueColor</emphasis>,
the entries will have defined values,
but those values are specific to the visual and are not defined
by the core protocol.
For
<emphasis role='bold'>StaticGray</emphasis>,
<emphasis role='bold'>StaticColor</emphasis>,
and
<emphasis role='bold'>TrueColor</emphasis>,
alloc must be specified as
<emphasis role='bold'>None</emphasis>
(or a
<emphasis role='bold'>Match</emphasis>
error results).
For the other classes, if alloc is
<emphasis role='bold'>None</emphasis>,
the colormap initially has no allocated entries,
and clients can allocate entries.
</para>
<para>
If alloc is
<emphasis role='bold'>All</emphasis>,
then the entire colormap is allocated writable.
The initial values of all allocated entries are undefined.
For
<emphasis role='bold'>GrayScale</emphasis>
and
<emphasis role='bold'>PseudoColor</emphasis>,
the effect is as if an
<link linkend="requests:AllocColorCells"><emphasis role='bold'>AllocColorCells</emphasis></link>
request returned all pixel values from zero to N - 1,
where N is the colormap-entries value in the specified visual.
For
<emphasis role='bold'>DirectColor</emphasis>,
the effect is as if an
<link linkend="requests:AllocColorPlanes"><emphasis role='bold'>AllocColorPlanes</emphasis></link>
request returned a pixel value of zero and red-mask,
green-mask, and blue-mask values containing the same bits as the
corresponding masks in the specified visual.
However,
in all cases, none of these entries can be freed with
<link linkend="requests:FreeColors"><emphasis role='bold'>FreeColors</emphasis></link>.
<!-- .sp -->
</para>
  </section>
  <section id="requests:FreeColormap">
    <title>FreeColormap</title>
    <indexterm zone="requests:FreeColormap" significance="preferred"><primary>FreeColormap</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>cmap</emphasis>: COLORMAP
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Colormap</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request deletes the association between the resource ID and the colormap
and frees the colormap storage.
If the colormap is an installed map for a screen,
it is uninstalled (see
<link linkend="requests:UninstallColormap"><emphasis role='bold'>UninstallColormap</emphasis></link>
request).
If the colormap is defined as the colormap for a window (by means of
<link linkend="requests:CreateWindow"><emphasis role='bold'>CreateWindow</emphasis></link>
or
<link linkend="requests:ChangeWindowAttributes"><emphasis role='bold'>ChangeWindowAttributes</emphasis></link>),
the colormap for the window is changed to
<emphasis role='bold'>None</emphasis>,
and a
<link linkend="events:ColormapNotify"><emphasis role='bold'>ColormapNotify</emphasis></link>
event is generated.
The protocol does not define the colors displayed for a window with a colormap of
<emphasis role='bold'>None</emphasis>.
</para>
<para>
This request has no effect on a default colormap for a screen.
<!-- .sp -->
</para>
  </section>
  <section id="requests:CopyColormapAndFree">
    <title>CopyColormapAndFree</title>
    <indexterm zone="requests:CopyColormapAndFree" significance="preferred"><primary>CopyColormapAndFree</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>mid</emphasis>, <emphasis remap='I'>src-cmap</emphasis>: COLORMAP
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>Colormap</emphasis>,
<emphasis role='bold'>IDChoice</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request creates a colormap of the same visual type
and for the same screen as src-cmap,
and it associates identifier mid with it.
It also moves all of the client's existing allocations from src-cmap
to the new colormap with their color values intact
and their read-only or writable characteristics intact,
and it frees those entries in src-cmap.
Color values in other entries in the new colormap are undefined.
If src-cmap was created by the client with alloc
<emphasis role='bold'>All</emphasis>
(see
<link linkend="requests:CreateColormap"><emphasis role='bold'>CreateColormap</emphasis></link>
request),
then the new colormap is also created with alloc
<emphasis role='bold'>All</emphasis>,
all color values for all entries are copied from src-cmap,
and then all entries in src-cmap are freed.
If src-cmap was not created by the client with alloc
<emphasis role='bold'>All</emphasis>,
then the allocations to be moved are all those pixels and planes that have
been allocated by the client using either
<link linkend="requests:AllocColor"><emphasis role='bold'>AllocColor</emphasis></link>,
<link linkend="requests:AllocNamedColor"><emphasis role='bold'>AllocNamedColor</emphasis></link>,
<link linkend="requests:AllocColorCells"><emphasis role='bold'>AllocColorCells</emphasis></link>,
or
<link linkend="requests:AllocColorPlanes"><emphasis role='bold'>AllocColorPlanes</emphasis></link>
and that have not been freed since they were allocated.
<!-- .sp -->
</para>
  </section>
  <section id="requests:InstallColormap">
    <title>InstallColormap</title>
    <indexterm zone="requests:InstallColormap" significance="preferred"><primary>InstallColormap</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>cmap</emphasis>: COLORMAP
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Colormap</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request makes this colormap an installed map for its screen.
All windows associated with this colormap immediately display with true colors.
As a side effect,
additional colormaps might be implicitly installed
or uninstalled by the server.
Which other colormaps get installed or uninstalled is server-dependent
except that the required list must remain installed.
</para>
<para>
If cmap is not already an installed map, a
<link linkend="events:ColormapNotify"><emphasis role='bold'>ColormapNotify</emphasis></link>
event is generated on every window having cmap as an attribute.
In addition,
for every other colormap that is installed or uninstalled as a result
of the request, a
<emphasis role='bold'>ColormapNotify</emphasis>
event is generated on every window having that colormap as an attribute.
</para>
<para>
At any time, there is a subset of the installed maps that are viewed as an
ordered list and are called the required list.
The length of the required list is at most M,
where M is the min-installed-maps specified for the screen in the
connection setup.
The required list is maintained as follows.
When a colormap is an explicit argument to
<emphasis role='bold'>InstallColormap</emphasis>,
it is added to the head of the list; the list is truncated at the
tail, if necessary, to keep the length of the list to at most M.
When a colormap is an explicit argument to
<link linkend="requests:UninstallColormap"><emphasis role='bold'>UninstallColormap</emphasis></link>
and it is in the required list, it is removed from the list.
A colormap is not added to the required list when it is installed implicitly
by the server, and the server cannot implicitly uninstall a colormap that is
in the required list.
</para>
<para>
Initially the default colormap for a screen is installed (but is not in
the required list).
<!-- .sp -->
</para>
  </section>
  <section id="requests:UninstallColormap">
    <title>UninstallColormap</title>
    <indexterm zone="requests:UninstallColormap" significance="preferred"><primary>UninstallColormap</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>cmap</emphasis>: COLORMAP
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Colormap</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
If cmap is on the required list for its screen (see
<link linkend="requests:InstallColormap"><emphasis role='bold'>InstallColormap</emphasis></link>
request),
it is removed from the list.
As a side effect,
cmap might be uninstalled,
and additional colormaps might be implicitly installed or uninstalled.
Which colormaps get installed or uninstalled is server-dependent
except that the required list must remain installed.
</para>
<para>
If cmap becomes uninstalled, a
<link linkend="events:ColormapNotify"><emphasis role='bold'>ColormapNotify</emphasis></link>
event is generated on every window having cmap as an attribute.
In addition,
for every other colormap that is installed or uninstalled as a result of
the request, a
<emphasis role='bold'>ColormapNotify</emphasis>
event is generated on every window having that colormap as an attribute.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ListInstalledColormaps">
    <title>ListInstalledColormaps</title>
    <indexterm zone="requests:ListInstalledColormaps" significance="preferred"><primary>ListInstalledColormaps</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
cmaps: LISTofCOLORMAP
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Window</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns a list of the currently installed colormaps for the
screen of the specified window.
The order of colormaps is not significant,
and there is no explicit indication of the required list (see
<link linkend="requests:InstallColormap"><emphasis role='bold'>InstallColormap</emphasis></link>
request).
<!-- .sp -->
</para>
  </section>
  <section id="requests:AllocColor">
    <title>AllocColor</title>
    <indexterm zone="requests:AllocColor" significance="preferred"><primary>AllocColor</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>cmap</emphasis>: COLORMAP
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>red</emphasis>, <emphasis remap='I'>green</emphasis>, <emphasis remap='I'>blue</emphasis>: CARD16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
pixel: CARD32
      </entry>
    </row>
    <row>
      <entry>
red, green, blue: CARD16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>Colormap</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request allocates a read-only colormap entry corresponding to the closest
RGB values provided by the hardware.
It also returns the pixel and the RGB values actually used.
Multiple clients requesting the same effective RGB values can be assigned
the same read-only entry, allowing entries to be shared.
<!-- .sp -->
</para>
  </section>
  <section id="requests:AllocNamedColor">
    <title>AllocNamedColor</title>
    <indexterm zone="requests:AllocNamedColor" significance="preferred"><primary>AllocNamedColor</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>cmap</emphasis>: COLORMAP
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>name</emphasis>: STRING8
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
pixel: CARD32
      </entry>
    </row>
    <row>
      <entry>
exact-red, exact-green, exact-blue: CARD16
      </entry>
    </row>
    <row>
      <entry>
visual-red, visual-green, visual-blue: CARD16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>Colormap</emphasis>,
<emphasis role='bold'>Name</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request looks up the named color with respect to the screen associated
with the colormap.
Then, it does an
<link linkend="requests:AllocColor"><emphasis role='bold'>AllocColor</emphasis></link>
on cmap.
The name should use the ISO Latin-1 encoding,
and uppercase and lowercase do not matter.
The exact RGB values specify the true values for the color,
and the visual values specify the values actually used in the colormap.
<!-- .sp -->
</para>
  </section>
  <section id="requests:AllocColorCells">
    <title>AllocColorCells</title>
    <indexterm zone="requests:AllocColorCells" significance="preferred"><primary>AllocColorCells</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>cmap</emphasis>: COLORMAP
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>colors</emphasis>, <emphasis remap='I'>planes</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>contiguous</emphasis>: BOOL
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
pixels, masks: LISTofCARD32
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>Colormap</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
The number of colors must be positive,
and the number of planes must be nonnegative (or a
<emphasis role='bold'>Value</emphasis>
error results).
If C colors and P planes are requested,
then C pixels and P masks are returned.
No mask will have any bits in common with any other mask
or with any of the pixels.
By ORing together masks and pixels,
C*%2 sup P% distinct pixels can be produced;
all of these are allocated writable by the request.
For
<emphasis role='bold'>GrayScale</emphasis>
or
<emphasis role='bold'>PseudoColor</emphasis>,
each mask will have exactly one bit set to 1; for
<emphasis role='bold'>DirectColor</emphasis>,
each will have exactly three bits set to 1.
If contiguous is
<emphasis role='bold'>True</emphasis>
and if all masks are ORed together,
a single contiguous set of bits will be formed for
<emphasis role='bold'>GrayScale</emphasis>
or
<emphasis role='bold'>PseudoColor</emphasis>,
and three contiguous sets of bits (one within each pixel subfield) for
<emphasis role='bold'>DirectColor</emphasis>.
The RGB values of the allocated entries are undefined.
<!-- .sp -->
</para>
  </section>
  <section id="requests:AllocColorPlanes">
    <title>AllocColorPlanes</title>
    <indexterm zone="requests:AllocColorPlanes" significance="preferred"><primary>AllocColorPlanes</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>cmap</emphasis>: COLORMAP
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>colors</emphasis>, <emphasis remap='I'>reds</emphasis>, <emphasis remap='I'>greens</emphasis>, <emphasis remap='I'>blues</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>contiguous</emphasis>: BOOL
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
pixels: LISTofCARD32
      </entry>
    </row>
    <row>
      <entry>
red-mask, green-mask, blue-mask: CARD32
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>Colormap</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
The number of colors must be positive,
and the reds, greens, and blues must be nonnegative (or a
<emphasis role='bold'>Value</emphasis>
error results).
If C colors, R reds, G greens, and B blues are requested,
then C pixels are returned, and the masks have R, G, and B bits set,
respectively.
If contiguous is
<emphasis role='bold'>True</emphasis>,
then each mask will have a contiguous set of bits.
No mask will have any bits in common with any other mask
or with any of the pixels.
For
<emphasis role='bold'>DirectColor</emphasis>,
each mask will lie within the corresponding pixel subfield.
By ORing together subsets of masks with pixels,
C*%2 sup R+G+B% distinct pixels can be produced;
all of these are allocated writable by the request.
The initial RGB values of the allocated entries are undefined.
In the colormap,
there are only C*%2 sup R% independent red entries,
C*%2 sup G% independent green entries,
and C*%2 sup B% independent blue entries.
This is true even for
<emphasis role='bold'>PseudoColor</emphasis>.
When the colormap entry for a pixel value is changed using
<link linkend="requests:StoreColors"><emphasis role='bold'>StoreColors</emphasis></link>
or
<link linkend="requests:StoreNamedColor"><emphasis role='bold'>StoreNamedColor</emphasis></link>,
the pixel is decomposed according to the masks and the
corresponding independent entries are updated.
<!-- .sp -->
</para>
  </section>
  <section id="requests:FreeColors">
    <title>FreeColors</title>
    <indexterm zone="requests:FreeColors" significance="preferred"><primary>FreeColors</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>cmap</emphasis>: COLORMAP
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>pixels</emphasis>: LISTofCARD32
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>plane-mask</emphasis>: CARD32
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Access</emphasis>,
<emphasis role='bold'>Colormap</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
The plane-mask should not have any bits in common with any of the
pixels.
The set of all pixels is produced by ORing together subsets of
plane-mask with the pixels.
The request frees all of these pixels that
were allocated by the client (using
<link linkend="requests:AllocColor"><emphasis role='bold'>AllocColor</emphasis></link>,
<link linkend="requests:AllocNamedColor"><emphasis role='bold'>AllocNamedColor</emphasis></link>,
<link linkend="requests:AllocColorCells"><emphasis role='bold'>AllocColorCells</emphasis></link>,
and
<link linkend="requests:AllocColorPlanes"><emphasis role='bold'>AllocColorPlanes</emphasis></link>).
Note that freeing an
individual pixel obtained from
<link linkend="requests:AllocColorPlanes"><emphasis role='bold'>AllocColorPlanes</emphasis></link>
may not actually allow it to be reused until all of its related pixels
are also freed.
Similarly, a read-only entry is not actually freed until it has been
freed by all clients, and if a client allocates the same read-only entry
multiple times, it must free the entry that many times before the
entry is actually freed.
</para>
<para>
All specified pixels that are allocated by the client in cmap are freed,
even if one or more pixels produce an error.
A
<emphasis role='bold'>Value</emphasis>
error is generated if a specified pixel is not a valid index into cmap.
An
<emphasis role='bold'>Access</emphasis>
error is generated if a specified pixel is not allocated by the
client (that is, is unallocated or is only allocated by another client)
or if the colormap was created with all entries writable (using an alloc
value of
<emphasis role='bold'>All</emphasis>
in
<link linkend="requests:CreateColormap"><emphasis role='bold'>CreateColormap</emphasis></link>).
If more than one pixel is in error,
it is arbitrary as to which pixel is reported.
<!-- .sp -->
</para>
  </section>
  <section id="requests:StoreColors">
    <title>StoreColors</title>
    <indexterm zone="requests:StoreColors" significance="preferred"><primary>StoreColors</primary></indexterm>

<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<emphasis remap='I'>cmap</emphasis>: COLORMAP
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>items</emphasis>: LISTofCOLORITEM
      </entry>
    </row>
    <row>
      <entry>
where:
      </entry>
    </row>
    <row>
      <entry>
        <para>
        <informaltable frame='none'>
  <?dbfo keep-together="always" ?>
          <tgroup cols='2' align='left' colsep='0' rowsep='0'>
          <colspec colname='c1' colwidth='1.0*'/>
          <colspec colname='c2' colwidth='2.0*'/>
          <tbody>
          <row>
            <entry>COLORITEM:</entry>
            <entry>[pixel: CARD32</entry>
          </row>
          <row>
            <entry></entry>
            <entry>
do-red, do-green, do-blue: BOOL
            </entry>
          </row>
          <row>
            <entry></entry>
            <entry>
red, green, blue: CARD16]
            </entry>
          </row>
          </tbody>
          </tgroup>
        </informaltable>
        </para>
      </entry>
    </row>
    <row>
      <entry>
Errors:
<emphasis role='bold'>Access</emphasis>,
<emphasis role='bold'>Colormap</emphasis>,
<emphasis role='bold'>Value</emphasis>
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
This request changes the colormap entries of the specified pixels.
The do-red, do-green, and do-blue fields indicate which components
should actually be changed.
If the colormap is an installed map for its screen,
the changes are visible immediately.
</para>
<para>
All specified pixels that are allocated writable in cmap (by any client)
are changed, even if one or more pixels produce an error.
A
<emphasis role='bold'>Value</emphasis>
error is generated if a specified pixel is not a valid index into cmap, and an
<emphasis role='bold'>Access</emphasis>
error is generated if a specified pixel is unallocated or is allocated
read-only.
If more than one pixel is in error,
it is arbitrary as to which pixel is reported.
<!-- .sp -->
</para>
  </section>
  <section id="requests:StoreNamedColor">
    <title>StoreNamedColor</title>
    <indexterm zone="requests:StoreNamedColor" significance="preferred"><primary>StoreNamedColor</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>cmap</emphasis>: COLORMAP
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>pixel</emphasis>: CARD32
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>name</emphasis>: STRING8
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>do-red</emphasis>, <emphasis remap='I'>do-green</emphasis>, <emphasis remap='I'>do-blue</emphasis>: BOOL
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Access</emphasis>,
<emphasis role='bold'>Colormap</emphasis>,
<emphasis role='bold'>Name</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request looks up the named color with respect to the screen associated
with cmap and then does a
<link linkend="requests:StoreColors"><emphasis role='bold'>StoreColors</emphasis></link>
in cmap.
The name should use the ISO Latin-1 encoding,
and uppercase and lowercase do not matter.
The
<emphasis role='bold'>Access</emphasis>
and
<emphasis role='bold'>Value</emphasis>
errors are the same as in
<emphasis role='bold'>StoreColors</emphasis>.
<!-- .sp -->
</para>
  </section>
  <section id="requests:QueryColors">
    <title>QueryColors</title>
    <indexterm zone="requests:QueryColors" significance="preferred"><primary>QueryColors</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>cmap</emphasis>: COLORMAP
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>pixels</emphasis>: LISTofCARD32
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
colors: LISTofRGB
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
where:
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
RGB: [red, green, blue: CARD16]
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Colormap</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the hardware-specific color values stored in cmap for
the specified pixels.
The values returned for an unallocated entry are undefined.
A
<emphasis role='bold'>Value</emphasis>
error is generated if a pixel is not a valid index into cmap.
If more than one pixel is in error,
it is arbitrary as to which pixel is reported.
<!-- .sp -->
</para>
  </section>
  <section id="requests:LookupColor">
    <title>LookupColor</title>
    <indexterm zone="requests:LookupColor" significance="preferred"><primary>LookupColor</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>cmap</emphasis>: COLORMAP
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>name</emphasis>: STRING8
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
exact-red, exact-green, exact-blue: CARD16
      </entry>
    </row>
    <row>
      <entry>
visual-red, visual-green, visual-blue: CARD16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Colormap</emphasis>,
<emphasis role='bold'>Name</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request looks up the string name of a color with respect to the screen
associated with cmap and returns both the exact color values and
the closest values provided by the hardware with respect to the visual
type of cmap.
The name should use the ISO Latin-1 encoding,
and uppercase and lowercase do not matter.
<!-- .sp -->
</para>
  </section>
  <section id="requests:CreateCursor">
    <title>CreateCursor</title>
    <indexterm zone="requests:CreateCursor" significance="preferred"><primary>CreateCursor</primary></indexterm>
    <indexterm zone="requests:CreateCursor"><primary>Cursor</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>cid</emphasis>: CURSOR
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>source</emphasis>: PIXMAP
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>mask</emphasis>: PIXMAP or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>fore-red</emphasis>, <emphasis remap='I'>fore-green</emphasis>, <emphasis remap='I'>fore-blue</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>back-red</emphasis>, <emphasis remap='I'>back-green</emphasis>, <emphasis remap='I'>back-blue</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>x</emphasis>, <emphasis remap='I'>y</emphasis>: CARD16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>IDChoice</emphasis>,
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Pixmap</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request creates a cursor and associates identifier cid with it.
The foreground and background RGB values must be specified,
even if the server only has a
<emphasis role='bold'>StaticGray</emphasis>
or
<emphasis role='bold'>GrayScale</emphasis>
screen.
The foreground is used for the bits set to 1 in the source,
and the background is used for the bits set to 0.
Both source and mask (if specified) must have depth one (or a
<emphasis role='bold'>Match</emphasis>
error results), but they can have any root.
The mask pixmap defines the shape of the cursor.
That is,
the bits set to 1 in the mask define which source pixels will be displayed,
and where the mask has bits set to 0,
the corresponding bits of the source pixmap are ignored.
If no mask is given,
all pixels of the source are displayed.
The mask, if present, must be the same size as the source (or a
<emphasis role='bold'>Match</emphasis>
error results).
The x and y coordinates define the hotspot relative to the source's origin
and must be a point within the source (or a
<emphasis role='bold'>Match</emphasis>
error results).
</para>
<para>
The components of the cursor may be transformed arbitrarily to meet
display limitations.
</para>
<para>
The pixmaps can be freed immediately if no further explicit references
to them are to be made.
</para>
<para>
Subsequent drawing in the source or mask pixmap has an undefined effect
on the cursor.
The server might or might not make a copy of the pixmap.
<!-- .sp -->
</para>
  </section>
  <section id="requests:CreateGlyphCursor">
    <title>CreateGlyphCursor</title>
    <indexterm zone="requests:CreateGlyphCursor" significance="preferred"><primary>CreateGlyphCursor</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>cid</emphasis>: CURSOR
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>source-font</emphasis>: FONT
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>mask-font</emphasis>: FONT or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>source-char</emphasis>, <emphasis remap='I'>mask-char</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>fore-red</emphasis>, <emphasis remap='I'>fore-green</emphasis>, <emphasis remap='I'>fore-blue</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>back-red</emphasis>, <emphasis remap='I'>back-green</emphasis>, <emphasis remap='I'>back-blue</emphasis>: CARD16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>Font</emphasis>,
<emphasis role='bold'>IDChoice</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request is similar to
<link linkend="requests:CreateCursor"><emphasis role='bold'>CreateCursor</emphasis></link>,
except the source and mask bitmaps are obtained from the specified font glyphs.
The source-char must be a defined glyph in source-font,
and if mask-font is given, mask-char must be a defined glyph in mask-font
(or a
<emphasis role='bold'>Value</emphasis>
error results).
The mask font and character are optional.
The origins of the source and mask (if it is defined) glyphs
are positioned coincidently and define the hotspot.
The source and mask need not have the same bounding box metrics,
and there is no restriction on the placement of the hotspot relative
to the bounding boxes.
If no mask is given,
all pixels of the source are displayed.
Note that source-char and mask-char are CARD16, not CHAR2B.
For 2-byte matrix fonts,
the 16-bit value should be formed with byte1 in the most significant byte
and byte2 in the least significant byte.
</para>
<para>
The components of the cursor may be transformed arbitrarily to meet
display limitations.
</para>
<para>
The fonts can be freed immediately if no further explicit references to
them are to be made.
<!-- .sp -->
</para>
  </section>
  <section id="requests:FreeCursor">
    <title>FreeCursor</title>
    <indexterm zone="requests:FreeCursor" significance="preferred"><primary>FreeCursor</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>cursor</emphasis>: CURSOR
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Cursor</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request deletes the association between the resource ID and the cursor.
The cursor storage will be freed when no other resource references it.
<!-- .sp -->
</para>
  </section>
  <section id="requests:RecolorCursor">
    <title>RecolorCursor</title>
    <indexterm zone="requests:RecolorCursor" significance="preferred"><primary>RecolorCursor</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>cursor</emphasis>: CURSOR
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>fore-red</emphasis>, <emphasis remap='I'>fore-green</emphasis>, <emphasis remap='I'>fore-blue</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>back-red</emphasis>, <emphasis remap='I'>back-green</emphasis>, <emphasis remap='I'>back-blue</emphasis>: CARD16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Cursor</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request changes the color of a cursor.
If the cursor is being displayed on a screen,
the change is visible immediately.
<!-- .sp -->
</para>
  </section>
  <section id="requests:QueryBestSize">
    <title>QueryBestSize</title>
    <indexterm zone="requests:QueryBestSize" significance="preferred"><primary>QueryBestSize</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>class</emphasis>:
{ <emphasis role='bold'>Cursor</emphasis>,
<emphasis role='bold'>Tile</emphasis>,
<emphasis role='bold'>Stipple</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>width</emphasis>, <emphasis remap='I'>height</emphasis>: CARD16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
width, height: CARD16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Drawable</emphasis>,
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the best size that is closest to the argument size.
For
<emphasis role='bold'>Cursor</emphasis>,
this is the largest size that can be fully displayed.
For
<emphasis role='bold'>Tile</emphasis>,
this is the size that can be tiled fastest.
For
<emphasis role='bold'>Stipple</emphasis>,
this is the size that can be stippled fastest.
</para>
<para>
For
<emphasis role='bold'>Cursor</emphasis>,
the drawable indicates the desired screen.
For
<emphasis role='bold'>Tile</emphasis>
and
<emphasis role='bold'>Stipple</emphasis>,
the drawable indicates the screen and also possibly the window class and depth.
An
<emphasis role='bold'>InputOnly</emphasis>
window cannot be used as the drawable for
<emphasis role='bold'>Tile</emphasis>
or
<emphasis role='bold'>Stipple</emphasis>
(or a
<emphasis role='bold'>Match</emphasis>
error results).
<!-- .sp -->
</para>
  </section>
  <section id="requests:QueryExtension">
    <title>QueryExtension</title>
    <indexterm zone="requests:QueryExtension" significance="preferred"><primary>QueryExtension</primary></indexterm>
    <indexterm zone="requests:QueryExtension"><primary>Extension</primary><secondary>querying</secondary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>name</emphasis>: STRING8
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
present: BOOL
      </entry>
    </row>
    <row>
      <entry>
major-opcode: CARD8
      </entry>
    </row>
    <row>
      <entry>
first-event: CARD8
      </entry>
    </row>
    <row>
      <entry>
first-error: CARD8
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request determines if the named extension is present.
If so,
the major opcode for the extension is returned, if it has one.
Otherwise, zero is returned.
Any minor opcode and the request formats are specific to the extension.
If the extension involves additional event types,
the base event type code is returned.
Otherwise, zero is returned.
The format of the events is specific to the extension.
If the extension involves additional error codes,
the base error code is returned.
Otherwise, zero is returned.
The format of additional data in the errors is specific to the extension.
</para>
<para>
The extension name should use the ISO Latin-1 encoding,
and uppercase and lowercase matter.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ListExtensions">
    <title>ListExtensions</title>
    <indexterm zone="requests:ListExtensions" significance="preferred"><primary>ListExtensions</primary></indexterm>
    <indexterm zone="requests:ListExtensions"><primary>Extension</primary><secondary>listing</secondary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
names: LISTofSTRING8
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns a list of all extensions supported by the server.
</para>
  </section>
  <section id="requests:SetModifierMapping">
    <title>SetModifierMapping</title>
    <indexterm zone="requests:SetModifierMapping" significance="preferred"><primary>SetModifierMapping</primary></indexterm>
    <indexterm zone="requests:SetModifierMapping"><primary>Modifier keys</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>keycodes-per-modifier</emphasis>: CARD8
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>keycodes</emphasis>: LISTofKEYCODE
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
status:
{ <emphasis role='bold'>Success</emphasis>,
<emphasis role='bold'>Busy</emphasis>,
<emphasis role='bold'>Failed</emphasis>}
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request specifies the keycodes (if any) of the keys to be used as
modifiers.
The number of keycodes in the list must be 8*keycodes-per-modifier (or a
<emphasis role='bold'>Length</emphasis>
error results).
The keycodes are divided into eight sets,
with each set containing keycodes-per-modifier elements.
The sets are assigned to the modifiers
<emphasis role='bold'>Shift</emphasis>,
<emphasis role='bold'>Lock</emphasis>,
<emphasis role='bold'>Control</emphasis>,
<emphasis role='bold'>Mod1</emphasis>,
<emphasis role='bold'>Mod2</emphasis>,
<emphasis role='bold'>Mod3</emphasis>,
<emphasis role='bold'>Mod4</emphasis>,
and
<emphasis role='bold'>Mod5</emphasis>,
in order.
Only nonzero keycode values are used within each set;
zero values are ignored.
All of the nonzero keycodes must be in the range specified by min-keycode
and max-keycode in the connection setup (or a
<emphasis role='bold'>Value</emphasis>
error results).
The order of keycodes within a set does not matter.
If no nonzero values are specified in a set,
the use of the corresponding modifier is disabled,
and the modifier bit will always be zero.
Otherwise, the modifier bit will be one whenever
at least one of the keys in the corresponding set is in the down
position.
</para>
<para>
A server can impose restrictions on how modifiers can be changed (for example,
if certain keys do not generate up transitions in hardware,
if auto-repeat cannot be disabled on certain keys,
or if multiple keys per modifier are not supported).
The status reply is
<emphasis role='bold'>Failed</emphasis>
if some such restriction is violated,
and none of the modifiers is changed.
</para>
<para>
If the new nonzero keycodes specified for a modifier differ from those
currently defined and any (current or new) keys for that modifier are
logically in the down state, then the status reply is
<emphasis role='bold'>Busy</emphasis>,
and none of the modifiers is changed.
</para>
<para>
This request generates a
<link linkend="events:MappingNotify"><emphasis role='bold'>MappingNotify</emphasis></link>
event on a
<emphasis role='bold'>Success</emphasis>
status.
<!-- .sp -->
</para>
  </section>
  <section id="requests:GetModifierMapping">
    <title>GetModifierMapping</title>
    <indexterm zone="requests:GetModifierMapping" significance="preferred"><primary>GetModifierMapping</primary></indexterm>
    <indexterm zone="requests:GetModifierMapping"><primary>Modifier keys</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
keycodes-per-modifier: CARD8
      </entry>
    </row>
    <row>
      <entry>
keycodes: LISTofKEYCODE
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the keycodes of the keys being used as modifiers.
The number of keycodes in the list is 8*keycodes-per-modifier.
The keycodes are divided into eight sets,
with each set containing keycodes-per-modifier elements.
The sets are assigned to the modifiers
<emphasis role='bold'>Shift</emphasis>,
<emphasis role='bold'>Lock</emphasis>,
<emphasis role='bold'>Control</emphasis>,
<emphasis role='bold'>Mod1</emphasis>,
<emphasis role='bold'>Mod2</emphasis>,
<emphasis role='bold'>Mod3</emphasis>,
<emphasis role='bold'>Mod4</emphasis>,
and
<emphasis role='bold'>Mod5</emphasis>,
in order.
The keycodes-per-modifier value is chosen arbitrarily by the server;
zeroes are used to fill in unused elements within each set.
If only zero values are given in a set,
the use of the corresponding modifier has been disabled.
The order of keycodes within each set is chosen arbitrarily by the server.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ChangeKeyboardMapping">
    <title>ChangeKeyboardMapping</title>
    <indexterm zone="requests:ChangeKeyboardMapping" significance="preferred"><primary>ChangeKeyboardMapping</primary></indexterm>
    <indexterm zone="requests:ChangeKeyboardMapping"><primary>Keysym</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>first-keycode</emphasis>: KEYCODE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>keysyms-per-keycode</emphasis>: CARD8
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>keysyms</emphasis>: LISTofKEYSYM
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Alloc</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request defines the symbols for the specified number of keycodes,
starting with the specified keycode.
The symbols for keycodes outside this range remained unchanged.
The number of elements in the keysyms list must be a multiple of
keysyms-per-keycode (or a
<emphasis role='bold'>Length</emphasis>
error results).
The first-keycode must be greater than or equal to min-keycode as returned
in the connection setup (or a
<emphasis role='bold'>Value</emphasis>
error results) and:
<literallayout class="monospaced">
	first-keycode + (keysyms-length / keysyms-per-keycode) - 1
</literallayout>
</para>
<para>
must be less than or equal to max-keycode as returned in the connection
setup (or a
<emphasis role='bold'>Value</emphasis>
error results).
KEYSYM number N (counting from zero) for keycode K has an index
(counting from zero) of:
<literallayout class="monospaced">
	(K - first-keycode) * keysyms-per-keycode + N
</literallayout>
</para>
<para>
in keysyms.
The keysyms-per-keycode can be chosen arbitrarily by the client
to be large enough to hold all desired symbols.
A special KEYSYM value of
<keysym>NoSymbol</keysym>
should be used to fill in unused elements for individual keycodes.
It is legal for
<keysym>NoSymbol</keysym>
to appear in nontrailing positions of the effective list for a keycode.
</para>
<para>
This request generates a
<link linkend="events:MappingNotify"><emphasis role='bold'>MappingNotify</emphasis></link>
event.
</para>
<para>
There is no requirement that the server interpret this mapping;
it is merely stored for reading and writing by clients
(see <link linkend='Keyboards'>section 5</link>).
<!-- .sp -->
</para>
  </section>
  <section id="requests:GetKeyboardMapping">
    <title>GetKeyboardMapping</title>
    <indexterm zone="requests:GetKeyboardMapping" significance="preferred"><primary>GetKeyboardMapping</primary></indexterm>
    <indexterm zone="requests:GetKeyboardMapping"><primary>Keysym</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>first-keycode</emphasis>: KEYCODE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>count</emphasis>: CARD8
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
keysyms-per-keycode: CARD8
      </entry>
    </row>
    <row>
      <entry>
keysyms: LISTofKEYSYM
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the symbols for the specified number of keycodes,
starting with the specified keycode.
The first-keycode must be greater than or equal to
min-keycode as returned in the connection setup (or a
<emphasis role='bold'>Value</emphasis>
error results), and:
<literallayout class="monospaced">
	first-keycode + count - 1
</literallayout>
</para>
<para>
must be less than or equal to max-keycode as returned in the connection setup
(or a
<emphasis role='bold'>Value</emphasis>
error results).
The number of elements in the keysyms list is:
<literallayout class="monospaced">
	count * keysyms-per-keycode
</literallayout>
</para>
<para>
and KEYSYM number N (counting from zero) for keycode K has an index
(counting from zero) of:
<literallayout class="monospaced">
	(K - first-keycode) * keysyms-per-keycode + N
</literallayout>
</para>
<para>
in keysyms.
The keysyms-per-keycode value is chosen arbitrarily by the server
to be large enough to report all requested symbols.
A special KEYSYM value of
<keysym>NoSymbol</keysym>
is used to fill in unused elements for individual keycodes.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ChangeKeyboardControl">
    <title>ChangeKeyboardControl</title>
    <indexterm zone="requests:ChangeKeyboardControl" significance="preferred"><primary>ChangeKeyboardControl</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>value-mask</emphasis>: BITMASK
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>value-list</emphasis>: LISTofVALUE
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
Errors:
<emphasis role='bold'>Match</emphasis>,
<emphasis role='bold'>Value</emphasis>
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
This request controls various aspects of the keyboard.
The value-mask and value-list specify which controls are to be changed.
The possible values are:
</para>

<informaltable frame='topbot'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='2.0*'/>
  <thead>
    <row rowsep='1'>
      <entry>Control</entry>
      <entry>Type</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry>key-click-percent</entry>
      <entry>INT8</entry>
    </row>
    <row>
      <entry>bell-percent</entry>
      <entry>INT8</entry>
    </row>
    <row>
      <entry>bell-pitch</entry>
      <entry>INT16</entry>
    </row>
    <row>
      <entry>bell-duration</entry>
      <entry>INT16</entry>
    </row>
    <row>
      <entry>led</entry>
      <entry>CARD8</entry>
    </row>
    <row>
      <entry>led-mode</entry>
      <entry>
{ <emphasis role='bold'>On</emphasis>,
<emphasis role='bold'>Off</emphasis> }
      </entry>
    </row>
    <row>
      <entry>key </entry>
      <entry>KEYCODE</entry>
    </row>
    <row>
      <entry>auto-repeat-mode</entry>
      <entry>
{ <emphasis role='bold'>On</emphasis>,
<emphasis role='bold'>Off</emphasis>,
<emphasis role='bold'>Default</emphasis> }
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
The key-click-percent sets the volume for key clicks between 0 (off) and
100 (loud) inclusive, if possible.
Setting to -1 restores the default.
Other negative values generate a
<emphasis role='bold'>Value</emphasis>
error.
</para>
<para>
The bell-percent sets the base volume for the bell between 0 (off) and 100
(loud) inclusive, if possible.
Setting to -1 restores the default.
Other negative values generate a
<emphasis role='bold'>Value</emphasis>
error.
</para>
<para>
The bell-pitch sets the pitch (specified in Hz) of the bell, if possible.
Setting to -1 restores the default.
Other negative values generate a
<emphasis role='bold'>Value</emphasis>
error.
</para>
<para>
The bell-duration sets the duration of the bell (specified in milliseconds),
if possible.
Setting to -1 restores the default.
Other negative values generate a
<emphasis role='bold'>Value</emphasis>
error.
</para>
<para>
If both led-mode and led are specified,
then the state of that LED is changed, if possible.
If only led-mode is specified,
then the state of all LEDs are changed, if possible.
At most 32 LEDs, numbered from one, are supported.
No standard interpretation of LEDs is defined.
It is a
<emphasis role='bold'>Match</emphasis>
error if an led is specified without an led-mode.
</para>
<para>
If both auto-repeat-mode and key are specified,
then the auto-repeat mode of that key is changed, if possible.
If only auto-repeat-mode is specified,
then the global auto-repeat mode for the entire keyboard is changed,
if possible, without affecting the per-key settings.
It is a
<emphasis role='bold'>Match</emphasis>
error if a key is specified without an auto-repeat-mode.
Each key has an individual mode of whether or not it should auto-repeat
and a default setting for that mode.
In addition, there is a global mode of whether auto-repeat should be
enabled or not and a default setting for that mode.
When the global mode is
<emphasis role='bold'>On</emphasis>,
keys should obey their individual auto-repeat modes.
When the global mode is
<emphasis role='bold'>Off</emphasis>,
no keys should auto-repeat.
An auto-repeating key generates alternating
<link linkend="events:KeyPress"><emphasis role='bold'>KeyPress</emphasis></link>
and
<link linkend="events:KeyRelease"><emphasis role='bold'>KeyRelease</emphasis></link>
events.
When a key is used as a modifier,
it is desirable for the key not to auto-repeat,
regardless of the auto-repeat setting for that key.
</para>
<para>
A bell generator connected with the console but not directly on the
keyboard is treated as if it were part of the keyboard.
</para>
<para>
The order in which controls are verified and altered is server-dependent.
If an error is generated,
a subset of the controls may have been altered.
<!-- .sp -->
</para>
  </section>
  <section id="requests:GetKeyboardControl">
    <title>GetKeyboardControl</title>
    <indexterm zone="requests:GetKeyboardControl" significance="preferred"><primary>GetKeyboardControl</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
key-click-percent: CARD8
      </entry>
    </row>
    <row>
      <entry>
bell-percent: CARD8
      </entry>
    </row>
    <row>
      <entry>
bell-pitch: CARD16
      </entry>
    </row>
    <row>
      <entry>
bell-duration: CARD16
      </entry>
    </row>
    <row>
      <entry>
led-mask: CARD32
      </entry>
    </row>
    <row>
      <entry>
global-auto-repeat:
{ <emphasis role='bold'>On</emphasis>,
<emphasis role='bold'>Off</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
auto-repeats: LISTofCARD8
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the current control values for the keyboard.
For the LEDs,
the least significant bit of led-mask corresponds to LED one,
and each one bit in led-mask indicates an LED that is lit.
The auto-repeats is a bit vector;
each one bit indicates that auto-repeat is enabled for the corresponding key.
The vector is represented as 32 bytes.
Byte N (from 0) contains the bits for keys 8N to 8N + 7,
with the least significant bit in the byte representing key 8N.
<!-- .sp -->
</para>
  </section>
  <section id="requests:Bell">
    <title>Bell</title>
    <indexterm zone="requests:Bell" significance="preferred"><primary>Bell</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>percent</emphasis>: INT8
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request rings the bell on the keyboard at a volume relative to the
base volume for the keyboard, if possible.
Percent can range from -100 to 100 inclusive (or a
<emphasis role='bold'>Value</emphasis>
error results).
The volume at which the bell is rung when percent is nonnegative is:
<literallayout class="monospaced">
	base - [(base * percent) / 100] + percent
</literallayout>
</para>
<para>
When percent is negative, it is:
<literallayout class="monospaced">
	base + [(base * percent) / 100]
</literallayout>
<!-- .sp -->
</para>
  </section>
  <section id="requests:SetPointerMapping">
    <title>SetPointerMapping</title>
    <indexterm zone="requests:SetPointerMapping" significance="preferred"><primary>SetPointerMapping</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>map</emphasis>: LISTofCARD8
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
status:
{ <emphasis role='bold'>Success</emphasis>,
<emphasis role='bold'>Busy</emphasis>}
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request sets the mapping of the pointer.
Elements of the list are indexed starting from one.
The length of the list must be the same as
<link linkend="requests:GetPointerMapping"><emphasis role='bold'>GetPointerMapping</emphasis></link>
would return (or a
<emphasis role='bold'>Value</emphasis>
error results).
The index is a core button number,
and the element of the list defines the effective number.
</para>
<para>
A zero element disables a button.
Elements are not restricted in value by the number of physical buttons,
but no two elements can have the same nonzero value (or a
<emphasis role='bold'>Value</emphasis>
error results).
</para>
<para>
If any of the buttons to be altered are logically in the down state,
the status reply is
<emphasis role='bold'>Busy</emphasis>,
and the mapping is not changed.
</para>
<para>
This request generates a
<link linkend="events:MappingNotify"><emphasis role='bold'>MappingNotify</emphasis></link>
event on a
<emphasis role='bold'>Success</emphasis>
status.
<!-- .sp -->
</para>
  </section>
  <section id="requests:GetPointerMapping">
    <title>GetPointerMapping</title>
    <indexterm zone="requests:GetPointerMapping" significance="preferred"><primary>GetPointerMapping</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
map: LISTofCARD8
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the current mapping of the pointer.
Elements of the list are indexed starting from one.
The length of the list indicates the number of physical buttons.
</para>
<para>
The nominal mapping for a pointer is the identity mapping: map[i]=i.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ChangePointerControl">
    <title>ChangePointerControl</title>
    <indexterm zone="requests:ChangePointerControl" significance="preferred"><primary>ChangePointerControl</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>do-acceleration</emphasis>, <emphasis remap='I'>do-threshold</emphasis>: BOOL
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>acceleration-numerator</emphasis>, <emphasis remap='I'>acceleration-denominator</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>threshold</emphasis>: INT16
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request defines how the pointer moves.
The acceleration is a multiplier for movement expressed as a fraction.
For example,
specifying 3/1 means the pointer moves three times as fast as normal.
The fraction can be rounded arbitrarily by the server.
Acceleration only takes effect if the pointer moves more than threshold
number of pixels at once and only applies to the amount beyond the threshold.
Setting a value to -1 restores the default.
Other negative values generate a
<emphasis role='bold'>Value</emphasis>
error, as does a zero value for acceleration-denominator.
<!-- .sp -->
</para>
  </section>
  <section id="requests:GetPointerControl">
    <title>GetPointerControl</title>
    <indexterm zone="requests:GetPointerControl" significance="preferred"><primary>GetPointerControl</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
acceleration-numerator, acceleration-denominator: CARD16
      </entry>
    </row>
    <row>
      <entry>
threshold: CARD16
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the current acceleration and threshold for the pointer.
<!-- .sp -->
</para>
  </section>
  <section id="requests:SetScreenSaver">
    <title>SetScreenSaver</title>
    <indexterm zone="requests:SetScreenSaver" significance="preferred"><primary>SetScreenSaver</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>timeout</emphasis>, <emphasis remap='I'>interval</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>prefer-blanking</emphasis>:
{ <emphasis role='bold'>Yes</emphasis>,
<emphasis role='bold'>No</emphasis>,
<emphasis role='bold'>Default</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>allow-exposures</emphasis>:
{ <emphasis role='bold'>Yes</emphasis>,
<emphasis role='bold'>No</emphasis>,
<emphasis role='bold'>Default</emphasis>}
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
The timeout and interval are specified in seconds;
setting a value to -1 restores the default.
Other negative values generate a
<emphasis role='bold'>Value</emphasis>
error.
If the timeout value is zero,
screen-saver is disabled (but an activated screen-saver is not deactivated).
If the timeout value is nonzero,
screen-saver is enabled.
Once screen-saver is enabled,
if no input from the keyboard or pointer is generated for timeout seconds,
screen-saver is activated.
For each screen,
if blanking is preferred and the hardware supports video blanking,
the screen will simply go blank.
Otherwise,
if either exposures are allowed or the screen can be regenerated without
sending exposure events to clients,
the screen is changed in a server-dependent fashion to avoid phosphor burn.
Otherwise,
the state of the screens does not change, and screen-saver is not activated.
At the next keyboard or pointer input or at the next
<link linkend="requests:ForceScreenSaver"><emphasis role='bold'>ForceScreenSaver</emphasis></link>
with mode
<emphasis role='bold'>Reset</emphasis>,
screen-saver is deactivated, and all screen states are restored.
</para>
<para>
If the server-dependent screen-saver method is amenable to periodic change,
interval serves as a hint about how long the change period should be,
with zero hinting that no periodic change should be made.
Examples of ways to change the screen include scrambling the color map
periodically, moving an icon image about the screen periodically, or
tiling the screen with the root window background tile,
randomly reorigined periodically.
<!-- .sp -->
</para>
  </section>
  <section id="requests:GetScreenSaver">
    <title>GetScreenSaver</title>
    <indexterm zone="requests:GetScreenSaver" significance="preferred"><primary>GetScreenSaver</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
timeout, interval: CARD16
      </entry>
    </row>
    <row>
      <entry>
prefer-blanking:
{ <emphasis role='bold'>Yes</emphasis>,
<emphasis role='bold'>No</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
allow-exposures:
{ <emphasis role='bold'>Yes</emphasis>,
<emphasis role='bold'>No</emphasis>}
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the current screen-saver control values.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ForceScreenSaver">
    <title>ForceScreenSaver</title>
    <indexterm zone="requests:ForceScreenSaver" significance="preferred"><primary>ForceScreenSaver</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>mode</emphasis>:
{ <emphasis role='bold'>Activate</emphasis>,
<emphasis role='bold'>Reset</emphasis>}
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
If the mode is
<emphasis role='bold'>Activate</emphasis>
and screen-saver is currently deactivated,
then screen-saver is activated (even if screen-saver has been disabled with
a timeout value of zero).
If the mode is
<emphasis role='bold'>Reset</emphasis>
and screen-saver is currently enabled,
then screen-saver is deactivated (if it was activated),
and the activation timer is reset to its initial state
as if device input had just been received.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ChangeHosts">
    <title>ChangeHosts</title>
    <indexterm zone="requests:ChangeHosts" significance="preferred"><primary>ChangeHosts</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>mode</emphasis>:
{ <emphasis role='bold'>Insert</emphasis>,
<emphasis role='bold'>Delete</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>host</emphasis>: HOST
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Access</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request adds or removes the specified host from the access control list.
<indexterm zone="requests:ChangeHosts"><primary>Access control list</primary></indexterm>
When the access control mechanism is enabled and a client attempts to
establish a connection to the server,
the host on which the client resides must be in the access control list,
or the client must have been granted permission by a server-dependent
method, or the server will refuse the connection.
</para>
<para>
The client must reside on the same host as the server and/or have been granted
permission by a server-dependent method to execute this request (or an
<emphasis role='bold'>Access</emphasis>
error results).
</para>
<para>
An initial access control list can usually be specified,
typically by naming a file that the server reads at startup and reset.
</para>
<para id="address_families">
The following address families are defined.
<indexterm zone="address_families"><primary>Types</primary><secondary>HOST</secondary></indexterm>
A server is not required to support these families
and may support families not listed here.
Use of an unsupported family, an improper address format,
or an improper address length within a supported family results in a
<emphasis role='bold'>Value</emphasis>
error.
</para>
<para>
For the Internet family,
the address must be four bytes long.
The address bytes are in standard IP order;
the server performs no automatic swapping on the address bytes.
The Internet family supports IP version 4 addresses only.
</para>
<para>
For the InternetV6 family, the address must be sixteen bytes
long.  The address bytes are in standard IP order; the
server performs no automatic swapping on the address bytes.
The InternetV6 family supports IP version 6 addresses only.
</para>
<para>
For the DECnet family,
the server performs no automatic swapping on the address bytes.
A Phase IV address is two bytes long:
the first byte contains the least significant eight bits of the node number,
and the second byte contains the most significant two bits of the node number in
the least significant two bits of the byte and the area in the most
significant six bits of the byte.
</para>
<para>
For the Chaos family,
the address must be two bytes long.
The host number is always the first byte in the address,
and the subnet number is always the second byte.
The server performs no automatic swapping on the address bytes.
</para>
<para>
For the ServerInterpreted family, the address may be of any
length up to 65535 bytes.  The address consists of two strings
of ASCII characters, separated by a byte with a value of 0.
The first string represents the type of address, and the second
string contains the address value.  Address types and the syntax
for their associated values will be registered via the X.Org Registry.
Implementors who wish to add implementation specific types may register
a unique prefix with the X.Org registry to prevent namespace
collisions.
</para>
<para>
Use of a host address in the ChangeHosts request is deprecated.  It is
only useful when a host has a unique, constant address, a requirement
that is increasingly unmet as sites adopt dynamically assigned
addresses, network address translation gateways, IPv6 link local
addresses, and various other technologies.  It also assumes all users of
a host share equivalent access rights, and as such has never been
suitable for many multi-user machine environments.  Instead, more
secure forms of authentication, such as those based on shared secrets
or public key encryption, are recommended.
<!-- .sp -->
</para>
  </section>
  <section id="requests:ListHosts">
    <title>ListHosts</title>
    <indexterm zone="requests:ListHosts" significance="preferred"><primary>ListHosts</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
   ▶
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
mode:
{ <emphasis role='bold'>Enabled</emphasis>,
<emphasis role='bold'>Disabled</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
hosts: LISTofHOST
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request returns the hosts on the access control list
and whether use of the list at connection setup is currently
enabled or disabled.
</para>
<para>
Each HOST is padded to a multiple of four bytes.
<!-- .sp -->
</para>
  </section>
  <section id="requests:SetAccessControl">
    <title>SetAccessControl</title>
    <indexterm zone="requests:SetAccessControl" significance="preferred"><primary>SetAccessControl</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>mode</emphasis>:
{ <emphasis role='bold'>Enable</emphasis>,
<emphasis role='bold'>Disable</emphasis>}
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Access</emphasis>,
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request enables or disables the use of the access control list
at connection setups.
</para>
<para>
The client must reside on the same host as the server
and/or have been granted permission by a server-dependent method
to execute this request (or an
<emphasis role='bold'>Access</emphasis>
error results).
<!-- .sp -->
</para>
  </section>
  <section id="requests:SetCloseDownMode">
    <title>SetCloseDownMode</title>
    <indexterm zone="requests:SetCloseDownMode" significance="preferred"><primary>SetCloseDownMode</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>mode</emphasis>:
{ <emphasis role='bold'>Destroy</emphasis>,
<emphasis role='bold'>RetainPermanent</emphasis>,
<emphasis role='bold'>RetainTemporary</emphasis>}
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This request defines what will happen to the client's resources
at connection close.
A connection starts in
<emphasis role='bold'>Destroy</emphasis>
mode.
The meaning of the close-down mode is described
in <link linkend='Connection_Close'>section 10</link>.
<!-- .sp -->
</para>
  </section>
  <section id="requests:KillClient">
    <title>KillClient</title>
    <indexterm zone="requests:KillClient" significance="preferred"><primary>KillClient</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>resource</emphasis>: CARD32 or
<emphasis role='bold'>AllTemporary</emphasis>
<!-- .in -.2i -->
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
Errors:
<emphasis role='bold'>Value</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
If a valid resource is specified,
<link linkend="requests:KillClient"><emphasis role='bold'>KillClient</emphasis></link>
forces a close-down of the client that created the resource.
If the client has already terminated in either
<emphasis role='bold'>RetainPermanent</emphasis>
or
<emphasis role='bold'>RetainTemporary</emphasis>
mode, all of the client's resources are destroyed
(see <link linkend='Connection_Close'>section 10</link>).
If
<emphasis role='bold'>AllTemporary</emphasis>
is specified,
then the resources of all clients that have terminated in
<emphasis role='bold'>RetainTemporary</emphasis>
are destroyed.
<!-- .sp -->
</para>
  </section>
  <section id="requests:NoOperation">
    <title>NoOperation</title>
    <indexterm zone="requests:NoOperation" significance="preferred"><primary>NoOperation</primary></indexterm>
<para>
This request has no arguments and no results,
but the request length field
allows the request to be any multiple of four bytes in length.
The bytes contained in the request are uninterpreted by the server.
</para>
<para>
This request can be used in its minimum four byte form as padding where
necessary by client libraries that find it convenient to force requests
to begin on 64-bit boundaries.
</para>
  </section>
</chapter>

<chapter id='Connection_Close'>
<title>Connection Close</title>
  <indexterm zone="Connection_Close"><primary>Connection</primary><secondary>closing</secondary></indexterm>
<!-- .XS -->
<!-- \*(SN Connection Close -->
<!-- .XE -->
<para>
At connection close,
all event selections made by the client are discarded.
If the client has the pointer actively grabbed, an
<link linkend="requests:UngrabPointer"><emphasis role='bold'>UngrabPointer</emphasis></link>
is performed.
If the client has the keyboard actively grabbed, an
<link linkend="requests:UngrabKeyboard"><emphasis role='bold'>UngrabKeyboard</emphasis></link>
is performed.
All passive grabs by the client are released.
If the client has the server grabbed, an
<link linkend="requests:UngrabServer"><emphasis role='bold'>UngrabServer</emphasis></link>
is performed.
All selections (see
<link linkend="requests:SetSelectionOwner"><emphasis role='bold'>SetSelectionOwner</emphasis></link>
request)
owned by the client are disowned.
If close-down mode (see
<link linkend="requests:SetCloseDownMode"><emphasis role='bold'>SetCloseDownMode</emphasis></link>
request) is
<emphasis role='bold'>RetainPermanent</emphasis>
or
<emphasis role='bold'>RetainTemporary</emphasis>,
then all resources (including colormap entries)
allocated by the client are marked as permanent or temporary,
respectively (but this does not prevent other clients from explicitly
destroying them).
If the mode is
<emphasis role='bold'>Destroy</emphasis>,
all of the client's resources are destroyed.
</para>
<para>
When a client's resources are destroyed,
for each window in the client's save-set,
if the window is an inferior of a window created by the client,
the save-set window is reparented to the closest ancestor such that
the save-set window is not an inferior of a window created by the client.
If the save-set window is unmapped, a
<link linkend="requests:MapWindow"><emphasis role='bold'>MapWindow</emphasis></link>
request is performed on it (even if it was not an inferior
of a window created by the client).
The reparenting leaves unchanged the absolute coordinates
(with respect to the root window) of the upper-left outer corner of the
save-set window.
After save-set processing,
all windows created by the client are destroyed.
For each nonwindow resource created by the client,
the appropriate
<emphasis role='bold'>Free</emphasis>
request is performed.
All colors and colormap entries allocated by the client are freed.
</para>
<para>
A server goes through a cycle of having no connections and having some
connections.
At every transition to the state of having no connections
as a result of a connection closing with a
<emphasis role='bold'>Destroy</emphasis>
close-down mode,
the server resets its state as if it had just been started.
This starts by destroying all lingering resources from clients
that have terminated in
<emphasis role='bold'>RetainPermanent</emphasis>
or
<emphasis role='bold'>RetainTemporary</emphasis>
mode.
It additionally includes deleting all but the predefined atom identifiers,
deleting all properties on all root windows, resetting all device maps and
attributes (key click, bell volume, acceleration), resetting the access
control list, restoring the standard root tiles and cursors, restoring
the default font path, and restoring the input focus to state
<emphasis role='bold'>PointerRoot</emphasis>.
</para>
<para>
Note that closing a connection with a close-down mode of
<emphasis role='bold'>RetainPermanent</emphasis>
or
<emphasis role='bold'>RetainTemporary</emphasis>
will not cause the server to reset.
</para>
</chapter>
<!-- .NH 1 -->
<chapter id='Events'>
<title>Events</title>
<!-- .XS -->
<!-- \*(SN Events -->
<!-- .XE -->
<para>
<indexterm zone="Events"><primary>Event</primary></indexterm>
When a button press is processed with the pointer in some window W
and no active pointer grab is in progress,
the ancestors of W are searched from the root down,
looking for a passive grab to activate.
If no matching passive grab on the button exists,
then an active grab is started automatically for the client receiving the event,
and the last-pointer-grab time is set to the current server time.
The effect is essentially equivalent to a
<link linkend="requests:GrabButton"><emphasis role='bold'>GrabButton</emphasis></link>
with arguments:
</para>

<informaltable frame='topbot'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='2.0*'/>
  <thead>
    <row rowsep='1'>
      <entry>Argument</entry>
      <entry>Value</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry>event-window</entry>
      <entry>Event window</entry>
    </row>
    <row>
      <entry>event-mask</entry>
      <entry>
Client's selected pointer events on the event window
      </entry>
    </row>
    <row>
      <entry>pointer-mode and keyboard-mode</entry>
      <entry>
<emphasis role='bold'>Asynchronous</emphasis>
      </entry>
    </row>
    <row>
      <entry>owner-events</entry>
      <entry>
<emphasis role='bold'>True</emphasis>
if the client has
<emphasis role='bold'>OwnerGrabButton</emphasis>
selected on the event window, otherwise
<emphasis role='bold'>False</emphasis>
      </entry>
    </row>
    <row>
      <entry>confine-to</entry>
      <entry>
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>cursor</entry>
      <entry>
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>



<para>
The grab is terminated automatically when the logical state of the pointer
has all buttons released.
<link linkend="requests:UngrabPointer"><emphasis role='bold'>UngrabPointer</emphasis></link>
and
<link linkend="requests:ChangeActivePointerGrab"><emphasis role='bold'>ChangeActivePointerGrab</emphasis></link>
can both be used to modify the active grab.
<!-- .sp -->
</para>

  <section id="events:input">
    <title>Input Device events</title>
    <indexterm zone="events:input" significance="preferred"><primary>Input device</primary><secondary>events</secondary></indexterm>

<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry id="events:KeyPress">
<indexterm zone="events:KeyPress" significance="preferred"><primary>KeyPress</primary></indexterm>
<emphasis role='bold'>KeyPress</emphasis>
      </entry>
    </row>
    <row>
      <entry id="events:KeyRelease">
<indexterm zone="events:KeyRelease" significance="preferred"><primary>KeyRelease</primary></indexterm>
<emphasis role='bold'>KeyRelease</emphasis>
      </entry>
    </row>
    <row>
      <entry id="events:ButtonPress">
<indexterm zone="events:ButtonPress" significance="preferred"><primary>ButtonPress</primary></indexterm>
<emphasis role='bold'>ButtonPress</emphasis>
      </entry>
    </row>
    <row>
      <entry id="events:ButtonRelease">
<indexterm zone="events:ButtonRelease" significance="preferred"><primary>ButtonRelease</primary></indexterm>
<emphasis role='bold'>ButtonRelease</emphasis>
      </entry>
    </row>
    <row>
      <entry id="events:MotionNotify">
<indexterm zone="events:MotionNotify" significance="preferred"><primary>MotionNotify</primary></indexterm>
<emphasis role='bold'>MotionNotify</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>root</emphasis>, <emphasis remap='I'>event</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>child</emphasis>: WINDOW or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>same-screen</emphasis>: BOOL
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>root-x</emphasis>, <emphasis remap='I'>root-y</emphasis>, <emphasis remap='I'>event-x</emphasis>, <emphasis remap='I'>event-y</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>detail</emphasis>: &lt;see below&gt;
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>state</emphasis>: SETofKEYBUTMASK
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>time</emphasis>: TIMESTAMP
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
These events are generated either when a key or button logically changes state
or when the pointer logically moves.
The generation of these logical changes may lag the physical changes
if device event processing is frozen.
Note that
<emphasis role='bold'>KeyPress</emphasis>
and
<emphasis role='bold'>KeyRelease</emphasis>
are generated for all keys, even those mapped to modifier bits.
<phrase id="events:input:source">
The source of the event is the window the pointer is in.
</phrase>
<indexterm zone="events:input:source"><primary>Event</primary><secondary>source</secondary></indexterm>
The window the event is reported with respect to is called the event window.
The event window is found by starting with the source window and
looking up the hierarchy for the first window on which any client has selected
interest in the event (provided no intervening window prohibits event
generation by including the event type in its do-not-propagate-mask).
The actual window used for reporting can be modified by active grabs and,
in the case of keyboard events, can be modified by the focus window.
</para>
<para>
The root is the root window of the source window,
and root-x and root-y are the pointer coordinates relative to root's origin
at the time of the event.
Event is the event window.
If the event window is on the same screen as root,
then event-x and event-y are the pointer coordinates relative to the
event window's origin.
Otherwise, event-x and event-y are zero.
If the source window is an inferior of the event window,
then child is set to the child of the event window that is an
ancestor of (or is) the source window.
Otherwise, it is set to
<emphasis role='bold'>None</emphasis>.
The state component gives the logical state of the buttons and modifier keys
just before the event.
The detail component type varies with the event type:
</para>

<informaltable frame='topbot'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='2' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <colspec colname='c2' colwidth='2.0*'/>
  <thead>
    <row rowsep='1'>
      <entry>Event</entry>
      <entry>Component</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry>
<emphasis role='bold'>KeyPress</emphasis>,
<emphasis role='bold'>KeyRelease</emphasis>
      </entry>
      <entry>KEYCODE</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>ButtonPress</emphasis>,
<emphasis role='bold'>ButtonRelease</emphasis>
      </entry>
      <entry>BUTTON</entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>MotionNotify</emphasis>
      </entry>
      <entry>
{ <emphasis role='bold'>Normal</emphasis>
<emphasis role='bold'>Hint</emphasis> }
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
<emphasis role='bold'>MotionNotify</emphasis>
events are only generated when the motion begins and ends in the window.
The granularity of motion events is not guaranteed,
but a client selecting for motion events is guaranteed to get at least one
event when the pointer moves and comes to rest.
Selecting
<emphasis role='bold'>PointerMotion</emphasis>
receives events independent of the state of the pointer buttons.
By selecting some subset of
<emphasis role='bold'>Button[1-5]Motion</emphasis>
instead,
<emphasis role='bold'>MotionNotify</emphasis>
events will only be received when one or more of the
specified buttons are pressed.
By selecting
<emphasis role='bold'>ButtonMotion</emphasis>,
<emphasis role='bold'>MotionNotify</emphasis>
events will be received only when at least one button is pressed.
The events are always of type
<emphasis role='bold'>MotionNotify</emphasis>,
independent of the selection.
If
<emphasis role='bold'>PointerMotionHint</emphasis>
is selected,
the server is free to send only one
<emphasis role='bold'>MotionNotify</emphasis>
event (with detail
<emphasis role='bold'>Hint</emphasis>)
to the client for the event window until
either the key or button state changes,
the pointer leaves the event window,
or the client issues a
<link linkend="requests:QueryPointer"><emphasis role='bold'>QueryPointer</emphasis></link>
or
<link linkend="requests:GetMotionEvents"><emphasis role='bold'>GetMotionEvents</emphasis></link>
request.
<!-- .sp -->
</para>
  </section>
  <section id="events:pointer_window">
    <title>Pointer Window events</title>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry id="events:EnterNotify">
<indexterm zone="events:EnterNotify" significance="preferred"><primary>EnterNotify</primary></indexterm>
<emphasis role='bold'>EnterNotify</emphasis>
      </entry>
    </row>
    <row>
      <entry id="events:LeaveNotify">
<indexterm zone="events:LeaveNotify" significance="preferred"><primary>LeaveNotify</primary></indexterm>
<emphasis role='bold'>LeaveNotify</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>root</emphasis>, <emphasis remap='I'>event</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>child</emphasis>: WINDOW or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>same-screen</emphasis>: BOOL
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>root-x</emphasis>, <emphasis remap='I'>root-y</emphasis>, <emphasis remap='I'>event-x</emphasis>, <emphasis remap='I'>event-y</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>mode</emphasis>:
{ <emphasis role='bold'>Normal</emphasis>,
<emphasis role='bold'>Grab</emphasis>,
<emphasis role='bold'>Ungrab</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>detail</emphasis>:
{ <emphasis role='bold'>Ancestor</emphasis>,
<emphasis role='bold'>Virtual</emphasis>,
<emphasis role='bold'>Inferior</emphasis>,
<emphasis role='bold'>Nonlinear</emphasis>,
<emphasis role='bold'>NonlinearVirtual</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>focus</emphasis>: BOOL
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>state</emphasis>: SETofKEYBUTMASK
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>time</emphasis>: TIMESTAMP
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
If pointer motion or window hierarchy change causes the pointer to be
in a different window than before,
<emphasis role='bold'>EnterNotify</emphasis>
and
<emphasis role='bold'>LeaveNotify</emphasis>
events are generated instead of a
<link linkend="events:MotionNotify"><emphasis role='bold'>MotionNotify</emphasis></link>
event.
Only clients selecting
<emphasis role='bold'>EnterWindow</emphasis>
on a window receive
<emphasis role='bold'>EnterNotify</emphasis>
events, and only clients selecting
<emphasis role='bold'>LeaveWindow</emphasis>
receive
<emphasis role='bold'>LeaveNotify</emphasis>
events.
The pointer position reported in the event is always the final position,
not the initial position of the pointer.
The root is the root window for this position,
and root-x and root-y are the pointer coordinates relative to root's
origin at the time of the event.
Event is the event window.
If the event window is on the same screen as root,
then event-x and event-y are the pointer coordinates relative
to the event window's origin.
Otherwise, event-x and event-y are zero.
In a
<emphasis role='bold'>LeaveNotify</emphasis>
event, if a child of the event window contains the initial position of the
pointer, then the child component is set to that child.
Otherwise, it is
<emphasis role='bold'>None</emphasis>.
For an
<emphasis role='bold'>EnterNotify</emphasis>
event, if a child of the event window contains the final pointer position,
then the child component is set to that child.
Otherwise, it is
<emphasis role='bold'>None</emphasis>.
If the event window is the focus window or an inferior of the focus window,
then focus is
<emphasis role='bold'>True</emphasis>.
Otherwise, focus is
<emphasis role='bold'>False</emphasis>.
</para>
<para>
Normal pointer motion events have mode
<emphasis role='bold'>Normal</emphasis>.
Pseudo-motion events when a grab activates have mode
<emphasis role='bold'>Grab</emphasis>,
and pseudo-motion events when a grab deactivates have mode
<emphasis role='bold'>Ungrab</emphasis>.
</para>
<para>
All
<emphasis role='bold'>EnterNotify</emphasis>
and
<emphasis role='bold'>LeaveNotify</emphasis>
events caused by a hierarchy change are generated after any hierarchy event
caused by that change (that is,
<link linkend="events:UnmapNotify"><emphasis role='bold'>UnmapNotify</emphasis></link>,
<link linkend="events:MapNotify"><emphasis role='bold'>MapNotify</emphasis></link>,
<link linkend="events:ConfigureNotify"><emphasis role='bold'>ConfigureNotify</emphasis></link>,
<link linkend="events:GravityNotify"><emphasis role='bold'>GravityNotify</emphasis></link>,
<link linkend="events:CirculateNotify"><emphasis role='bold'>CirculateNotify</emphasis></link>),
but the ordering of
<emphasis role='bold'>EnterNotify</emphasis>
and
<emphasis role='bold'>LeaveNotify</emphasis>
events with respect to
<link linkend="events:FocusOut"><emphasis role='bold'>FocusOut</emphasis></link>,
<link linkend="events:VisibilityNotify"><emphasis role='bold'>VisibilityNotify</emphasis></link>,
and
<link linkend="events:Expose"><emphasis role='bold'>Expose</emphasis></link>
events is not constrained.
</para>
<para>
Normal events are generated as follows:
</para>

<para>
When the pointer moves from window A to window B and A is an inferior
of B:
</para>

<itemizedlist>
  <listitem>
    <para>
<emphasis role='bold'>LeaveNotify</emphasis>
with detail
<emphasis role='bold'>Ancestor</emphasis>
is generated on A.
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>LeaveNotify</emphasis>
with detail
<emphasis role='bold'>Virtual</emphasis>
is generated on each window between A and B exclusive (in that order).
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>EnterNotify</emphasis>
with detail
<emphasis role='bold'>Inferior</emphasis>
is generated on B.
    </para>
  </listitem>
</itemizedlist>

<para>
When the pointer moves from window A to window B and B is an inferior
of A:
</para>

<itemizedlist>
  <listitem>
    <para>
<!-- .IP bu 5 -->
<emphasis role='bold'>LeaveNotify</emphasis>
with detail
<emphasis role='bold'>Inferior</emphasis>
is generated on A.
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>EnterNotify</emphasis>
with detail
<emphasis role='bold'>Virtual</emphasis>
is generated on each window between A and B exclusive (in that order).
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>EnterNotify</emphasis>
with detail
<emphasis role='bold'>Ancestor</emphasis>
is generated on B.
    </para>
  </listitem>
</itemizedlist>

<para>
When the pointer moves from window A to window B and window C is
their least common ancestor:
</para>

<itemizedlist>
  <listitem>
    <para>
<emphasis role='bold'>LeaveNotify</emphasis>
with detail
<emphasis role='bold'>Nonlinear</emphasis>
is generated on A.
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>LeaveNotify</emphasis>
with detail
<emphasis role='bold'>NonlinearVirtual</emphasis>
is generated on each window between A and C exclusive (in that order).
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>EnterNotify</emphasis>
with detail
<emphasis role='bold'>NonlinearVirtual</emphasis>
is generated on each window between C and B exclusive (in that order).
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>EnterNotify</emphasis>
with detail
<emphasis role='bold'>Nonlinear</emphasis>
is generated on B.
    </para>
  </listitem>
</itemizedlist>

<para>
When the pointer moves from window A to window B on different screens:
</para>

<itemizedlist>
  <listitem>
    <para>
<emphasis role='bold'>LeaveNotify</emphasis>
with detail
<emphasis role='bold'>Nonlinear</emphasis>
is generated on A.
    </para>
  </listitem>
  <listitem>
    <para>
If A is not a root window,
<emphasis role='bold'>LeaveNotify</emphasis>
with detail
<emphasis role='bold'>NonlinearVirtual</emphasis>
is generated on each window above A up to and including its root (in order).
    </para>
  </listitem>
  <listitem>
    <para>
If B is not a root window,
<emphasis role='bold'>EnterNotify</emphasis>
with detail
<emphasis role='bold'>NonlinearVirtual</emphasis>
is generated on each window from B's root down to but not including B
(in order).
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>EnterNotify</emphasis>
with detail
<emphasis role='bold'>Nonlinear</emphasis>
is generated on B.
    </para>
  </listitem>
</itemizedlist>

<para>
When a pointer grab activates (but after any initial warp into a confine-to
window and before generating any actual
<link linkend="events:ButtonPress"><emphasis role='bold'>ButtonPress</emphasis></link>
event that activates the grab),
G is the grab-window for the grab, and P is the window the pointer is in:
</para>

<itemizedlist>
  <listitem>
    <para>
<emphasis role='bold'>EnterNotify</emphasis>
and
<emphasis role='bold'>LeaveNotify</emphasis>
events with mode
<emphasis role='bold'>Grab</emphasis>
are generated (as for
<emphasis role='bold'>Normal</emphasis>
above) as if the pointer were to suddenly warp from its current
position in P to some position in G.
However, the pointer does not warp,
and the pointer position is used as both the initial
and final positions for the events.
    </para>
  </listitem>
</itemizedlist>

<para>
When a pointer grab deactivates (but after generating any actual
<link linkend="events:ButtonRelease"><emphasis role='bold'>ButtonRelease</emphasis></link>
event that deactivates the grab), G is the grab-window for
the grab, and P is the window the pointer is in:
</para>

<itemizedlist>
  <listitem>
    <para>
<emphasis role='bold'>EnterNotify</emphasis>
and
<emphasis role='bold'>LeaveNotify</emphasis>
events with mode
<emphasis role='bold'>Ungrab</emphasis>
are generated (as for
<emphasis role='bold'>Normal</emphasis>
above) as if the pointer were to suddenly warp from
some position in G to its current position in P.
However, the pointer does not warp,
and the current pointer position is used as both the initial
and final positions for the events.
    </para>
  </listitem>
</itemizedlist>

  </section>
  <section id="events:input_focus">
    <title>Input Focus events</title>
    <indexterm zone="events:input_focus" significance="preferred"><primary>Input focus</primary><secondary>events</secondary></indexterm>

<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry id="events:FocusIn">
<indexterm zone="events:FocusIn" significance="preferred"><primary>FocusIn</primary></indexterm>
<emphasis role='bold'>FocusIn</emphasis>
      </entry>
    </row>
    <row>
      <entry id="events:FocusOut">
<indexterm zone="events:FocusOut" significance="preferred"><primary>FocusOut</primary></indexterm>
<emphasis role='bold'>FocusOut</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>event</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>mode</emphasis>:
{ <emphasis role='bold'>Normal</emphasis>,
<emphasis role='bold'>WhileGrabbed</emphasis>,
<emphasis role='bold'>Grab</emphasis>,
<emphasis role='bold'>Ungrab</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>detail</emphasis>:
{ <emphasis role='bold'>Ancestor</emphasis>,
<emphasis role='bold'>Virtual</emphasis>,
<emphasis role='bold'>Inferior</emphasis>,
<emphasis role='bold'>Nonlinear</emphasis>,
<emphasis role='bold'>NonlinearVirtual</emphasis>,
<emphasis role='bold'>Pointer</emphasis>,
      </entry>
    </row>
    <row>
      <entry>
<emphasis role='bold'>PointerRoot</emphasis>,
<emphasis role='bold'>None</emphasis> }
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
These events are generated when the input focus changes
and are reported to clients selecting
<emphasis role='bold'>FocusChange</emphasis>
on the window.
Events generated by
<link linkend="requests:SetInputFocus"><emphasis role='bold'>SetInputFocus</emphasis></link>
when the keyboard is not grabbed have mode
<emphasis role='bold'>Normal</emphasis>.
Events generated by
<emphasis role='bold'>SetInputFocus</emphasis>
when the keyboard is grabbed have mode
<emphasis role='bold'>WhileGrabbed</emphasis>.
Events generated when a keyboard grab activates have mode
<emphasis role='bold'>Grab</emphasis>,
and events generated when a keyboard grab deactivates have mode
<emphasis role='bold'>Ungrab</emphasis>.
</para>
<para>
All
<emphasis role='bold'>FocusOut</emphasis>
events caused by a window unmap are generated after any
<link linkend="events:UnmapNotify"><emphasis role='bold'>UnmapNotify</emphasis></link>
event, but the ordering of
<emphasis role='bold'>FocusOut</emphasis>
with respect to generated
<link linkend="events:EnterNotify"><emphasis role='bold'>EnterNotify</emphasis></link>,
<link linkend="events:LeaveNotify"><emphasis role='bold'>LeaveNotify</emphasis></link>,
<link linkend="events:VisibilityNotify"><emphasis role='bold'>VisibilityNotify</emphasis></link>,
and
<link linkend="events:Expose"><emphasis role='bold'>Expose</emphasis></link>
events is not constrained.
</para>
<para>
<emphasis role='bold'>Normal</emphasis>
and
<emphasis role='bold'>WhileGrabbed</emphasis>
events are generated as follows:
</para>
<para>
When the focus moves from window A to window B, A is an inferior of B,
and the pointer is in window P:
</para>

<itemizedlist>
  <listitem>
    <para>
<emphasis role='bold'>FocusOut</emphasis>
with detail
<emphasis role='bold'>Ancestor</emphasis>
is generated on A.
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>FocusOut</emphasis>
with detail
<emphasis role='bold'>Virtual</emphasis>
is generated on each window between A and B exclusive (in order).
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>FocusIn</emphasis>
with detail
<emphasis role='bold'>Inferior</emphasis>
is generated on B.
    </para>
  </listitem>
  <listitem>
    <para>
If P is an inferior of B
but P is not A or an inferior of A or an ancestor of A,
<emphasis role='bold'>FocusIn</emphasis>
with detail
<emphasis role='bold'>Pointer</emphasis>
is generated on each window below B down to and including P (in order).
    </para>
  </listitem>
</itemizedlist>


<para>
When the focus moves from window A to window B, B is an inferior of A,
and the pointer is in window P:
</para>

<itemizedlist>
  <listitem>
    <para>
If P is an inferior of A
but P is not an inferior of B or an ancestor of B,
<emphasis role='bold'>FocusOut</emphasis>
with detail
<emphasis role='bold'>Pointer</emphasis>
is generated on each window from P up to but not including A (in order).
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>FocusOut</emphasis>
with detail
<emphasis role='bold'>Inferior</emphasis>
is generated on A.
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>FocusIn</emphasis>
with detail
<emphasis role='bold'>Virtual</emphasis>
is generated on each window between A and B exclusive (in order).
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>FocusIn</emphasis>
with detail
<emphasis role='bold'>Ancestor</emphasis>
is generated on B.
    </para>
  </listitem>
</itemizedlist>

<para>
When the focus moves from window A to window B, window C is their
least common ancestor, and the pointer is in window P:
</para>

<itemizedlist>
  <listitem>
    <para>
If P is an inferior of A,
<emphasis role='bold'>FocusOut</emphasis>
with detail
<emphasis role='bold'>Pointer</emphasis>
is generated on each window from P up to but not including A (in order).
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>FocusOut</emphasis>
with detail
<emphasis role='bold'>Nonlinear</emphasis>
is generated on A.
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>FocusOut</emphasis>
with detail
<emphasis role='bold'>NonlinearVirtual</emphasis>
is generated on each window between A and C exclusive (in order).
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>FocusIn</emphasis>
with detail
<emphasis role='bold'>NonlinearVirtual</emphasis>
is generated on each window between C and B exclusive (in order).
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>FocusIn</emphasis>
with detail
<emphasis role='bold'>Nonlinear</emphasis>
is generated on B.
    </para>
  </listitem>
  <listitem>
    <para>
If P is an inferior of B,
<emphasis role='bold'>FocusIn</emphasis>
with detail
<emphasis role='bold'>Pointer</emphasis>
is generated on each window below B down to and including P (in order).
    </para>
  </listitem>
</itemizedlist>

<para>
When the focus moves from window A to window B on different screens
and the pointer is in window P:
</para>


<itemizedlist>
  <listitem>
    <para>
If P is an inferior of A,
<emphasis role='bold'>FocusOut</emphasis>
with detail
<emphasis role='bold'>Pointer</emphasis>
is generated on each window from P up to but not including A (in order).
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>FocusOut</emphasis>
with detail
<emphasis role='bold'>Nonlinear</emphasis>
is generated on A.
    </para>
  </listitem>
  <listitem>
    <para>
If A is not a root window,
<emphasis role='bold'>FocusOut</emphasis>
with detail
<emphasis role='bold'>NonlinearVirtual</emphasis>
is generated on each window above A up to and including its root (in order).
    </para>
  </listitem>
  <listitem>
    <para>
If B is not a root window,
<emphasis role='bold'>FocusIn</emphasis>
with detail
<emphasis role='bold'>NonlinearVirtual</emphasis>
is generated on each window from B's root down to but not including B
(in order).
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>FocusIn</emphasis>
with detail
<emphasis role='bold'>Nonlinear</emphasis>
is generated on B.
    </para>
  </listitem>
  <listitem>
    <para>
If P is an inferior of B,
<emphasis role='bold'>FocusIn</emphasis>
with detail
<emphasis role='bold'>Pointer</emphasis>
is generated on each window below B down to and including P (in order).
    </para>
  </listitem>
</itemizedlist>

<para>
When the focus moves from window A to
<emphasis role='bold'>PointerRoot</emphasis>
(or
<emphasis role='bold'>None</emphasis>)
and the pointer is in window P:
</para>


<itemizedlist>
  <listitem>
    <para>
If P is an inferior of A,
<emphasis role='bold'>FocusOut</emphasis>
with detail
<emphasis role='bold'>Pointer</emphasis>
is generated on each window from P up to but not including A (in order).
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>FocusOut</emphasis>
with detail
<emphasis role='bold'>Nonlinear</emphasis>
is generated on A.
    </para>
  </listitem>
  <listitem>
    <para>
If A is not a root window,
<emphasis role='bold'>FocusOut</emphasis>
with detail
<emphasis role='bold'>NonlinearVirtual</emphasis>
is generated on each window above A up to and including its root (in order).
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>FocusIn</emphasis>
with detail
<emphasis role='bold'>PointerRoot</emphasis>
(or
<emphasis role='bold'>None</emphasis>)
is generated on all root windows.
    </para>
  </listitem>
  <listitem>
    <para>
If the new focus is
<emphasis role='bold'>PointerRoot</emphasis>,
<emphasis role='bold'>FocusIn</emphasis>
with detail
<emphasis role='bold'>Pointer</emphasis>
is generated on each window from P's root down to and including P (in order).
    </para>
  </listitem>
</itemizedlist>

<para>
When the focus moves from
<emphasis role='bold'>PointerRoot</emphasis>
(or
<emphasis role='bold'>None</emphasis>)
to window A and the pointer is in window P:
</para>


<itemizedlist>
  <listitem>
    <para>
If the old focus is
<emphasis role='bold'>PointerRoot</emphasis>,
<emphasis role='bold'>FocusOut</emphasis>
with detail
<emphasis role='bold'>Pointer</emphasis>
is generated on each window from P up to and including P's root (in order).
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>FocusOut</emphasis>
with detail
<emphasis role='bold'>PointerRoot</emphasis>
(or
<emphasis role='bold'>None</emphasis>)
is generated on all root windows.
    </para>
  </listitem>
  <listitem>
    <para>
If A is not a root window,
<emphasis role='bold'>FocusIn</emphasis>
with detail
<emphasis role='bold'>NonlinearVirtual</emphasis>
is generated on each window from A's root down to but not including A
(in order).
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>FocusIn</emphasis>
with detail
<emphasis role='bold'>Nonlinear</emphasis>
is generated on A.
    </para>
  </listitem>
  <listitem>
    <para>
If P is an inferior of A,
<emphasis role='bold'>FocusIn</emphasis>
with detail
<emphasis role='bold'>Pointer</emphasis>
is generated on each window below A down to and including P (in order).
    </para>
  </listitem>
</itemizedlist>

<para>
When the focus moves from
<emphasis role='bold'>PointerRoot</emphasis>
to
<emphasis role='bold'>None</emphasis>
(or vice versa) and the pointer is in window P:
</para>

<itemizedlist>
  <listitem>
    <para>
If the old focus is
<emphasis role='bold'>PointerRoot</emphasis>,
<emphasis role='bold'>FocusOut</emphasis>
with detail
<emphasis role='bold'>Pointer</emphasis>
is generated on each window from P up to and including P's root (in order).
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>FocusOut</emphasis>
with detail
<emphasis role='bold'>PointerRoot</emphasis>
(or
<emphasis role='bold'>None</emphasis>)
is generated on all root windows.
    </para>
  </listitem>
  <listitem>
    <para>
<emphasis role='bold'>FocusIn</emphasis>
with detail
<emphasis role='bold'>None</emphasis>
(or
<emphasis role='bold'>PointerRoot</emphasis>)
is generated on all root windows.
    </para>
  </listitem>
  <listitem>
    <para>
If the new focus is
<emphasis role='bold'>PointerRoot</emphasis>,
<emphasis role='bold'>FocusIn</emphasis>
with detail
<emphasis role='bold'>Pointer</emphasis>
is generated on each window from P's root down to and including P (in order).
    </para>
  </listitem>
</itemizedlist>


<para>
When a keyboard grab activates (but before generating any actual
<emphasis role='bold'>KeyPress</emphasis>
event that activates the grab), G is the grab-window for the grab,
and F is the current focus:
</para>

<itemizedlist>
  <listitem>
    <para>
<emphasis role='bold'>FocusIn</emphasis>
and
<emphasis role='bold'>FocusOut</emphasis>
events with mode
<emphasis role='bold'>Grab</emphasis>
are generated (as for
<emphasis role='bold'>Normal</emphasis>
above) as if the focus were to change from F to G.
    </para>
  </listitem>
</itemizedlist>

<para>
When a keyboard grab deactivates (but after generating any actual
<emphasis role='bold'>KeyRelease</emphasis>
event that deactivates the grab), G is the grab-window for the grab,
and F is the current focus:
</para>

<itemizedlist>
  <listitem>
    <para>
<emphasis role='bold'>FocusIn</emphasis>
and
<emphasis role='bold'>FocusOut</emphasis>
events with mode
<emphasis role='bold'>Ungrab</emphasis>
are generated (as for
<emphasis role='bold'>Normal</emphasis>
above) as if the focus were to change from G to F.
    </para>
  </listitem>
</itemizedlist>

  </section>
  <section id="events:KeymapNotify">
    <title>KeymapNotify</title>
    <indexterm zone="events:KeymapNotify" significance="preferred"><primary>KeymapNotify</primary></indexterm>

<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>KeymapNotify</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>keys</emphasis>: LISTofCARD8
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
The value is a bit vector as described in
<link linkend="requests:QueryKeymap"><emphasis role='bold'>QueryKeymap</emphasis></link>.
This event is reported to clients selecting
<emphasis role='bold'>KeymapState</emphasis>
on a window and is generated immediately after every
<link linkend="events:EnterNotify"><emphasis role='bold'>EnterNotify</emphasis></link>
and
<link linkend="events:FocusIn"><emphasis role='bold'>FocusIn</emphasis></link>.
<!-- .sp -->
</para>
  </section>
  <section id="events:Expose">
    <title>Expose</title>
    <indexterm zone="events:Expose" significance="preferred"><primary>Expose</primary></indexterm>
    <indexterm zone="events:Expose"><primary>Event</primary><secondary>Exposure</secondary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>Expose</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>x</emphasis>,
<emphasis remap='I'>y</emphasis>,
<emphasis remap='I'>width</emphasis>,
<emphasis remap='I'>height</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>count</emphasis>: CARD16
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<para>
This event is reported to clients selecting
<emphasis role='bold'>Exposure</emphasis>
on the window.
It is generated when no valid contents are available for regions of a window,
and either the regions are visible, the regions are viewable
and the server is (perhaps newly) maintaining backing store on the window,
or the window is not viewable but the server is (perhaps newly) honoring
window's backing-store attribute of
<emphasis role='bold'>Always</emphasis>
or
<emphasis role='bold'>WhenMapped</emphasis>.
The regions are decomposed into an arbitrary set of rectangles,
and an
<emphasis role='bold'>Expose</emphasis>
event is generated for each rectangle.
</para>
<para>
For a given action causing exposure events,
the set of events for a given window are guaranteed to be reported contiguously.
If count is zero,
then no more
<emphasis role='bold'>Expose</emphasis>
events for this window follow.
If count is nonzero,
then at least that many more
<emphasis role='bold'>Expose</emphasis>
events for this window follow (and possibly more).
</para>
<para>
The x and y coordinates are relative to window's origin
and specify the upper-left corner of a rectangle.
The width and height specify the extent of the rectangle.
</para>
<para>
<emphasis role='bold'>Expose</emphasis>
events are never generated on
<emphasis role='bold'>InputOnly</emphasis>
windows.
</para>
<para>
All
<emphasis role='bold'>Expose</emphasis>
events caused by a hierarchy change are generated after any
hierarchy event caused by that change (for example,
<link linkend="events:UnmapNotify"><emphasis role='bold'>UnmapNotify</emphasis></link>,
<link linkend="events:MapNotify"><emphasis role='bold'>MapNotify</emphasis></link>,
<link linkend="events:ConfigureNotify"><emphasis role='bold'>ConfigureNotify</emphasis></link>,
<link linkend="events:GravityNotify"><emphasis role='bold'>GravityNotify</emphasis></link>,
<link linkend="events:CirculateNotify"><emphasis role='bold'>CirculateNotify</emphasis></link>).
All
<emphasis role='bold'>Expose</emphasis>
events on a given window are generated after any
<link linkend="events:VisibilityNotify"><emphasis role='bold'>VisibilityNotify</emphasis></link>
event on that window,
but it is not required that all
<emphasis role='bold'>Expose</emphasis>
events on all windows be generated after all
<emphasis role='bold'>Visibilitity</emphasis>
events on all windows.
The ordering of
<emphasis role='bold'>Expose</emphasis>
events with respect to
<link linkend="events:FocusOut"><emphasis role='bold'>FocusOut</emphasis></link>,
<link linkend="events:EnterNotify"><emphasis role='bold'>EnterNotify</emphasis></link>,
and
<link linkend="events:LeaveNotify"><emphasis role='bold'>LeaveNotify</emphasis></link>
events is not constrained.
<!-- .sp -->
</para>
  </section>
  <section id="events:GraphicsExposure">
    <title>GraphicsExposure</title>
    <indexterm zone="events:GraphicsExposure" significance="preferred"><primary>GraphicsExposure</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>GraphicsExposure</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>x</emphasis>, <emphasis remap='I'>y</emphasis>, <emphasis remap='I'>width</emphasis>, <emphasis remap='I'>height</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>count</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>major-opcode</emphasis>: CARD8
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>minor-opcode</emphasis>: CARD16
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is reported to a client using a graphics context
with graphics-exposures selected
and is generated when a destination region could not be computed due
to an obscured or out-of-bounds source region.
All of the regions exposed by a given graphics request
are guaranteed to be reported contiguously.
If count is zero then no more
<emphasis role='bold'>GraphicsExposure</emphasis>
events for this window follow.
If count is nonzero,
then at least that many more
<emphasis role='bold'>GraphicsExposure</emphasis>
events for this window follow (and possibly more).
</para>
<para>
The x and y coordinates are relative to drawable's origin
and specify the upper-left corner of a rectangle.
The width and height specify the extent of the rectangle.
</para>
<para>
The major and minor opcodes identify the graphics request used.
For the core protocol,
major-opcode is always
<link linkend="requests:CopyArea"><emphasis role='bold'>CopyArea</emphasis></link>
or
<link linkend="requests:CopyPlane"><emphasis role='bold'>CopyPlane</emphasis></link>,
and minor-opcode is always zero.
<!-- .sp -->
</para>
  </section>
  <section id="events:NoExposure">
    <title>NoExposure</title>
    <indexterm zone="events:NoExposure" significance="preferred"><primary>NoExposure</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
     <row>
      <entry>
	<emphasis role='bold'>NoExposure</emphasis>
      </entry>
    </row>
   <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>drawable</emphasis>: DRAWABLE
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>major-opcode</emphasis>: CARD8
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>minor-opcode:</emphasis> CARD16
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is reported to a client using a graphics context
with graphics-exposures selected
and is generated when a graphics request
that might produce
<link linkend="events:GraphicsExposure"><emphasis role='bold'>GraphicsExposure</emphasis></link>
events does not produce any.
The drawable specifies the destination used for the graphics request.
</para>
<para>
The major and minor opcodes identify the graphics request used.
For the core protocol,
major-opcode is always
<link linkend="requests:CopyArea"><emphasis role='bold'>CopyArea</emphasis></link>
or
<link linkend="requests:CopyPlane"><emphasis role='bold'>CopyPlane</emphasis></link>,
and the minor-opcode is always zero.
<!-- .sp -->
</para>
  </section>
  <section id="events:VisibilityNotify">
    <title>VisibilityNotify</title>
    <indexterm zone="events:VisibilityNotify" significance="preferred"><primary>VisibilityNotify</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>VisibilityNotify</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>state</emphasis>:
{ <emphasis role='bold'>Unobscured</emphasis>,
<emphasis role='bold'>PartiallyObscured</emphasis>,
<emphasis role='bold'>FullyObscured</emphasis>}
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is reported to clients selecting
<emphasis role='bold'>VisibilityChange</emphasis>
on the window.
In the following,
the state of the window is calculated ignoring all of the window's subwindows.
When a window changes state from partially or fully obscured or
not viewable to viewable and completely unobscured,
an event with
<emphasis role='bold'>Unobscured</emphasis>
is generated.
When a window changes state from viewable and completely unobscured,
from viewable and completely obscured,
or from not viewable, to viewable and partially obscured,
an event with
<emphasis role='bold'>PartiallyObscured</emphasis>
is generated.
When a window changes state from viewable and completely unobscured,
from viewable and partially obscured,
or from not viewable to viewable and fully obscured,
an event with
<emphasis role='bold'>FullyObscured</emphasis>
is generated.
</para>
<para>
<emphasis role='bold'>VisibilityNotify</emphasis>
events are never generated on
<emphasis role='bold'>InputOnly</emphasis>
windows.
</para>
<para>
All
<emphasis role='bold'>VisibilityNotify</emphasis>
events caused by a hierarchy change are generated after any hierarchy event
caused by that change (for example,
<link linkend="events:UnmapNotify"><emphasis role='bold'>UnmapNotify</emphasis></link>,
<link linkend="events:MapNotify"><emphasis role='bold'>MapNotify</emphasis></link>,
<link linkend="events:ConfigureNotify"><emphasis role='bold'>ConfigureNotify</emphasis></link>,
<link linkend="events:GravityNotify"><emphasis role='bold'>GravityNotify</emphasis></link>,
<link linkend="events:CirculateNotify"><emphasis role='bold'>CirculateNotify</emphasis></link>).
Any
<emphasis role='bold'>VisibilityNotify</emphasis>
event on a given window is generated before any
<link linkend="events:Expose"><emphasis role='bold'>Expose</emphasis></link>
events on that window,
but it is not required that all
<emphasis role='bold'>VisibilityNotify</emphasis>
events on all windows be generated before all
<emphasis role='bold'>Expose</emphasis>
events on all windows.
The ordering of
<emphasis role='bold'>VisibilityNotify</emphasis>
events with respect to
<link linkend="events:FocusOut"><emphasis role='bold'>FocusOut</emphasis></link>,
<link linkend="events:EnterNotify"><emphasis role='bold'>EnterNotify</emphasis></link>,
and
<link linkend="events:LeaveNotify"><emphasis role='bold'>LeaveNotify</emphasis></link>
events is not constrained.
<!-- .sp -->
</para>
  </section>
  <section id="events:CreateNotify">
    <title>CreateNotify</title>
    <indexterm zone="events:CreateNotify" significance="preferred"><primary>CreateNotify</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>CreateNotify</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>parent</emphasis>, <emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>x</emphasis>, <emphasis remap='I'>y</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>width</emphasis>, <emphasis remap='I'>height</emphasis>, <emphasis remap='I'>border-width</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>override-redirect</emphasis>: BOOL
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is reported to clients selecting
<emphasis role='bold'>SubstructureNotify</emphasis>
on the parent
and is generated when the window is created.
The arguments are as in the
<link linkend="requests:CreateWindow"><emphasis role='bold'>CreateWindow</emphasis></link>
request.
<!-- .sp -->
</para>
  </section>
  <section id="events:DestroyNotify">
    <title>DestroyNotify</title>
    <indexterm zone="events:DestroyNotify" significance="preferred"><primary>DestroyNotify</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>DestroyNotify</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>event</emphasis>, <emphasis remap='I'>window</emphasis>: WINDOW
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is reported to clients selecting
<emphasis role='bold'>StructureNotify</emphasis>
on the window and to clients selecting
<emphasis role='bold'>SubstructureNotify</emphasis>
on the parent.
It is generated when the window is destroyed.
The event is the window on which the event was generated,
and the window is the window that is destroyed.
</para>
<para>
The ordering of the
<emphasis role='bold'>DestroyNotify</emphasis>
events is such that for any given window,
<emphasis role='bold'>DestroyNotify</emphasis>
is generated on all inferiors of the window
before being generated on the window itself.
The ordering among siblings and across subhierarchies is not
otherwise constrained.
<!-- .sp -->
</para>
  </section>
  <section id="events:UnmapNotify">
    <title>UnmapNotify</title>
    <indexterm zone="events:UnmapNotify" significance="preferred"><primary>UnmapNotify</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>UnmapNotify</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>event</emphasis>, <emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>from-configure</emphasis>: BOOL
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is reported to clients selecting
<emphasis role='bold'>StructureNotify</emphasis>
on the window and to clients selecting
<emphasis role='bold'>SubstructureNotify</emphasis>
on the parent.
It is generated when the window changes state from mapped to unmapped.
The event is the window on which the event was generated,
and the window is the window that is unmapped.
The from-configure flag is
<emphasis role='bold'>True</emphasis>
if the event was generated as a result of the window's parent being resized
when the window itself had a win-gravity of
<emphasis role='bold'>Unmap</emphasis>.
<!-- .sp -->
</para>
  </section>
  <section id="events:MapNotify">
    <title>MapNotify</title>
    <indexterm zone="events:MapNotify" significance="preferred"><primary>MapNotify</primary></indexterm>
    <indexterm zone="events:MapNotify"><primary>Mapped window</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>MapNotify</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>event</emphasis>, <emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>override-redirect</emphasis>: BOOL
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is reported to clients selecting
<emphasis role='bold'>StructureNotify</emphasis>
on the window and to clients selecting
<emphasis role='bold'>SubstructureNotify</emphasis>
on the parent.
It is generated when the window changes state from unmapped to mapped.
The event is the window on which the event was generated,
and the window is the window that is mapped.
The override-redirect flag is from the window's attribute.
<!-- .sp -->
</para>
  </section>
  <section id="events:MapRequest">
    <title>MapRequest</title>
    <indexterm zone="events:MapRequest" significance="preferred"><primary>MapRequest</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>MapRequest</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>parent</emphasis>, <emphasis remap='I'>window</emphasis>: WINDOW
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is reported to the client selecting
<emphasis role='bold'>SubstructureRedirect</emphasis>
on the parent and is generated when a
<link linkend="requests:MapWindow"><emphasis role='bold'>MapWindow</emphasis></link>
request is issued on an unmapped window with an override-redirect attribute of
<emphasis role='bold'>False</emphasis>.
<!-- .sp -->
</para>
  </section>
  <section id="events:ReparentNotify">
    <title>ReparentNotify</title>
    <indexterm zone="events:ReparentNotify" significance="preferred"><primary>ReparentNotify</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>ReparentNotify</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>event</emphasis>, <emphasis remap='I'>window</emphasis>, <emphasis remap='I'>parent</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>x</emphasis>, <emphasis remap='I'>y</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>override-redirect</emphasis>: BOOL
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is reported to clients selecting
<emphasis role='bold'>SubstructureNotify</emphasis>
on either the old or the new parent and to clients selecting
<emphasis role='bold'>StructureNotify</emphasis>
on the window.
It is generated when the window is reparented.
The event is the window on which the event was generated.
The window is the window that has been rerooted.
The parent specifies the new parent.
The x and y coordinates are relative to the new parent's origin
and specify the position of the upper-left outer corner of the window.
The override-redirect flag is from the window's attribute.
<!-- .sp -->
</para>
  </section>
  <section id="events:ConfigureNotify">
    <title>ConfigureNotify</title>
    <indexterm zone="events:ConfigureNotify" significance="preferred"><primary>ConfigureNotify</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>ConfigureNotify</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>event</emphasis>, <emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>x</emphasis>, <emphasis remap='I'>y</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>width</emphasis>, <emphasis remap='I'>height</emphasis>, <emphasis remap='I'>border-width</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>above-sibling</emphasis>: WINDOW or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>override-redirect</emphasis>: BOOL
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is reported to clients selecting
<emphasis role='bold'>StructureNotify</emphasis>
on the window and to clients selecting
<emphasis role='bold'>SubstructureNotify</emphasis>
on the parent.
It is generated when a
<link linkend="requests:ConfigureWindow"><emphasis role='bold'>ConfigureWindow</emphasis></link>
request actually changes the state of the window.
The event is the window on which the event was generated,
and the window is the window that is changed.
The x and y coordinates are relative to the new parent's origin
and specify the position of the upper-left outer corner of the window.
The width and height specify the inside size, not including the border.
If above-sibling is
<emphasis role='bold'>None</emphasis>,
then the window is on the bottom of the stack with respect to siblings.
Otherwise, the window is immediately on top of the specified sibling.
The override-redirect flag is from the window's attribute.
<!-- .sp -->
</para>
  </section>
  <section id="events:GravityNotify">
    <title>GravityNotify</title>
    <indexterm zone="events:GravityNotify" significance="preferred"><primary>GravityNotify</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>GravityNotify</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>event</emphasis>, <emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>x</emphasis>, <emphasis remap='I'>y</emphasis>: INT16
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is reported to clients selecting
<emphasis role='bold'>SubstructureNotify</emphasis>
on the parent and to clients selecting
<emphasis role='bold'>StructureNotify</emphasis>
on the window.
It is generated when a window is moved because of a change in size
of the parent.
The event is the window on which the event was generated,
and the window is the window that is moved.
The x and y coordinates are relative to the new parent's origin
and specify the position of the upper-left outer corner of the window.
<!-- .sp -->
</para>
  </section>
  <section id="events:ResizeRequest">
    <title>ResizeRequest</title>
    <indexterm zone="events:ResizeRequest" significance="preferred"><primary>ResizeRequest</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>ResizeRequest</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>width</emphasis>, <emphasis remap='I'>height</emphasis>: CARD16
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is reported to the client selecting
<emphasis role='bold'>ResizeRedirect</emphasis>
on the window and is generated when a
<link linkend="requests:ConfigureWindow"><emphasis role='bold'>ConfigureWindow</emphasis></link>
request by some other client on the window attempts to change the size
of the window.
The width and height are the requested inside size, not including the border.
<!-- .sp -->
</para>
  </section>
  <section id="events:ConfigureRequest">
    <title>ConfigureRequest</title>
    <indexterm zone="events:ConfigureRequest" significance="preferred"><primary>ConfigureRequest</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>ConfigureRequest</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>parent</emphasis>, <emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>x</emphasis>, <emphasis remap='I'>y</emphasis>: INT16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>width</emphasis>, <emphasis remap='I'>height</emphasis>, <emphasis remap='I'>border-width</emphasis>: CARD16
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>sibling</emphasis>: WINDOW or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>stack-mode</emphasis>:
{ <emphasis role='bold'>Above</emphasis>,
<emphasis role='bold'>Below</emphasis>,
<emphasis role='bold'>TopIf</emphasis>,
<emphasis role='bold'>BottomIf</emphasis>,
<emphasis role='bold'>Opposite</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>value-mask</emphasis>: BITMASK
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is reported to the client selecting
<emphasis role='bold'>SubstructureRedirect</emphasis>
on the parent and is generated when a
<link linkend="requests:ConfigureWindow"><emphasis role='bold'>ConfigureWindow</emphasis></link>
request is issued on the window by some other client.
The value-mask indicates which components were specified in the request.
The value-mask and the corresponding values are reported as given
in the request.
The remaining values are filled in from the current geometry of the window,
except in the case of sibling and stack-mode,
which are reported as
<emphasis role='bold'>None</emphasis>
and
<emphasis role='bold'>Above</emphasis>
(respectively) if not given in the request.
<!-- .sp -->
</para>
  </section>
  <section id="events:CirculateNotify">
    <title>CirculateNotify</title>
    <indexterm zone="events:CirculateNotify" significance="preferred"><primary>CirculateNotify</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>CirculateNotify</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>event</emphasis>, <emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>place</emphasis>:
{ <emphasis role='bold'>Top</emphasis>,
<emphasis role='bold'>Bottom</emphasis>}
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is reported to clients selecting
<emphasis role='bold'>StructureNotify</emphasis>
on the window and to clients selecting
<emphasis role='bold'>SubstructureNotify</emphasis>
on the parent.
It is generated when the window is actually restacked from a
<link linkend="requests:CirculateWindow"><emphasis role='bold'>CirculateWindow</emphasis></link>
request.
The event is the window on which the event was generated,
and the window is the window that is restacked.
If place is
<emphasis role='bold'>Top</emphasis>,
the window is now on top of all siblings.
Otherwise, it is below all siblings.
<!-- .sp -->
</para>
  </section>
  <section id="events:CirculateRequest">
    <title>CirculateRequest</title>
    <indexterm zone="events:CirculateRequest" significance="preferred"><primary>CirculateRequest</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>CirculateRequest</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>parent</emphasis>, <emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>place</emphasis>:
{ <emphasis role='bold'>Top</emphasis>,
<emphasis role='bold'>Bottom</emphasis>}
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is reported to the client selecting
<emphasis role='bold'>SubstructureRedirect</emphasis>
on the parent and is generated when a
<link linkend="requests:CirculateWindow"><emphasis role='bold'>CirculateWindow</emphasis></link>
request is issued on the parent and a window actually needs to be restacked.
The window specifies the window to be restacked,
and the place specifies what the new position in the stacking order should be.
<!-- .sp -->
</para>
  </section>
  <section id="events:PropertyNotify">
    <title>PropertyNotify</title>
    <indexterm zone="events:PropertyNotify" significance="preferred"><primary>PropertyNotify</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>PropertyNotify</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>atom</emphasis>: ATOM
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>state</emphasis>:
{ <emphasis role='bold'>NewValue</emphasis>,
<emphasis role='bold'>Deleted</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>time</emphasis>: TIMESTAMP
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is reported to clients selecting
<emphasis role='bold'>PropertyChange</emphasis>
on the window and is generated with state
<emphasis role='bold'>NewValue</emphasis>
when a property of the window is changed using
<link linkend="requests:ChangeProperty"><emphasis role='bold'>ChangeProperty</emphasis></link>
or
<link linkend="requests:RotateProperties"><emphasis role='bold'>RotateProperties</emphasis></link>,
even when adding zero-length data using
<emphasis role='bold'>ChangeProperty</emphasis>
and when replacing all or part of a property with identical data using
<emphasis role='bold'>ChangeProperty</emphasis>
or
<emphasis role='bold'>RotateProperties</emphasis>.
It is generated with state
<emphasis role='bold'>Deleted</emphasis>
when a property of the
window is deleted using request
<link linkend="requests:DeleteProperty"><emphasis role='bold'>DeleteProperty</emphasis></link>
or
<link linkend="requests:GetProperty"><emphasis role='bold'>GetProperty</emphasis></link>.
The timestamp indicates the server time when the property was changed.
<!-- .sp -->
</para>
  </section>
  <section id="events:SelectionClear">
    <title>SelectionClear</title>
    <indexterm zone="events:SelectionClear" significance="preferred"><primary>SelectionClear</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>SelectionClear</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>owner</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>selection</emphasis>: ATOM
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>time</emphasis>: TIMESTAMP
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is reported to the current owner of a selection
and is generated when a new owner is being defined by means of
<link linkend="requests:SetSelectionOwner"><emphasis role='bold'>SetSelectionOwner</emphasis></link>.
The timestamp is the last-change time recorded for the selection.
The owner argument is the window that was specified by the current owner in its
<emphasis role='bold'>SetSelectionOwner</emphasis>
request.
<!-- .sp -->
</para>
  </section>
  <section id="events:SelectionRequest">
    <title>SelectionRequest</title>
    <indexterm zone="events:SelectionRequest" significance="preferred"><primary>SelectionRequest</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>SelectionRequest</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>owner</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>selection</emphasis>: ATOM
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>target</emphasis>: ATOM
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>property</emphasis>: ATOM or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>requestor</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>time</emphasis>: TIMESTAMP or
<emphasis role='bold'>CurrentTime</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is reported to the owner of a selection
and is generated when a client issues a
<link linkend="requests:ConvertSelection"><emphasis role='bold'>ConvertSelection</emphasis></link>
request.
The owner argument is the window that was specified in the
<link linkend="requests:SetSelectionOwner"><emphasis role='bold'>SetSelectionOwner</emphasis></link>
request.
The remaining arguments are as in the
<emphasis role='bold'>ConvertSelection</emphasis>
request.
</para>
<para>
The owner should convert the selection based on the specified target type
and send a
<emphasis role='bold'>SelectionNotify</emphasis>
back to the requestor.
A complete specification for using selections is given in the X.Org
standard <citetitle>Inter-Client Communication Conventions Manual</citetitle>.
<!-- .sp -->
</para>
  </section>
  <section id="events:SelectionNotify">
    <title>SelectionNotify</title>
    <indexterm zone="events:SelectionNotify" significance="preferred"><primary>SelectionNotify</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>SelectionNotify</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>requestor</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>selection</emphasis>, <emphasis remap='I'>target</emphasis>: ATOM
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>property</emphasis>: ATOM or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>time</emphasis>: TIMESTAMP or
<emphasis role='bold'>CurrentTime</emphasis>
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is generated by the server in response to a
<link linkend="requests:ConvertSelection"><emphasis role='bold'>ConvertSelection</emphasis></link>
request when there is no owner for the selection.
When there is an owner,
it should be generated by the owner using
<link linkend="requests:SendEvent"><emphasis role='bold'>SendEvent</emphasis></link>.
The owner of a selection should send this event to a requestor either
when a selection has been converted and stored as a property
or when a selection conversion could not be performed (indicated with property
<emphasis role='bold'>None</emphasis>).
<!-- .sp -->
</para>
  </section>
  <section id="events:ColormapNotify">
    <title>ColormapNotify</title>
    <indexterm zone="events:ColormapNotify" significance="preferred"><primary>ColormapNotify</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>ColormapNotify</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>colormap</emphasis>: COLORMAP or
<emphasis role='bold'>None</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>new</emphasis>: BOOL
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>state</emphasis>:
{ <emphasis role='bold'>Installed</emphasis>,
<emphasis role='bold'>Uninstalled</emphasis>}
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is reported to clients selecting
<emphasis role='bold'>ColormapChange</emphasis>
on the window.
It is generated with value
<emphasis role='bold'>True</emphasis>
for new when the colormap attribute of the window is changed
and is generated with value
<emphasis role='bold'>False</emphasis>
for new when the colormap of a window is installed or uninstalled.
In either case,
the state indicates whether the colormap is currently installed.
<!-- .sp -->
</para>
  </section>
  <section id="events:MappingNotify">
    <title>MappingNotify</title>
    <indexterm zone="events:MappingNotify" significance="preferred"><primary>MappingNotify</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>MappingNotify</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>request</emphasis>:
{ <emphasis role='bold'>Modifier</emphasis>,
<emphasis role='bold'>Keyboard</emphasis>,
<emphasis role='bold'>Pointer</emphasis>}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>first-keycode</emphasis>, <emphasis remap='I'>count</emphasis>: CARD8
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is sent to all clients.
There is no mechanism to express disinterest in this event.
The detail indicates the kind of change that occurred:
<emphasis role='bold'>Modifiers</emphasis>
for a successful
<link linkend="requests:SetModifierMapping"><emphasis role='bold'>SetModifierMapping</emphasis></link>,
<emphasis role='bold'>Keyboard</emphasis>
for a successful
<link linkend="requests:ChangeKeyboardMapping"><emphasis role='bold'>ChangeKeyboardMapping</emphasis></link>,
and
<emphasis role='bold'>Pointer</emphasis>
for a successful
<link linkend="requests:SetPointerMapping"><emphasis role='bold'>SetPointerMapping</emphasis></link>.
If the detail is
<emphasis role='bold'>Keyboard</emphasis>,
then first-keycode and count indicate the range of altered keycodes.
<!-- .sp -->
</para>
  </section>
  <section id="events:ClientMessage">
    <title>ClientMessage</title>
    <indexterm zone="events:ClientMessage" significance="preferred"><primary>ClientMessage</primary></indexterm>
<informaltable frame='none'>
  <?dbfo keep-together="always" ?>
  <tgroup cols='1' align='left' colsep='0' rowsep='0'>
  <colspec colname='c1' colwidth='1.0*'/>
  <tbody>
    <row>
      <entry>
	<emphasis role='bold'>ClientMessage</emphasis>
      </entry>
    </row>
    <row>
      <entry>
<!-- .in +.2i -->
<emphasis remap='I'>window</emphasis>: WINDOW
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>type</emphasis>: ATOM
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>format</emphasis>: {8, 16, 32}
      </entry>
    </row>
    <row>
      <entry>
<emphasis remap='I'>data</emphasis>: LISTofINT8 or LISTofINT16 or LISTofINT32
<!-- .in -.2i -->
<!-- .eM -->
      </entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>
<!-- .eM -->
<para>
This event is only generated by clients using
<link linkend="requests:SendEvent"><emphasis role='bold'>SendEvent</emphasis></link>.
The type specifies how the data is to be interpreted by the receiving client;
the server places no interpretation on the type or the data.
The format specifies whether the data should be viewed as a list of 8-bit,
16-bit, or 32-bit quantities, so that the server can correctly
byte-swap, as necessary.
The data always consists of either 20 8-bit values or 10 16-bit values
or 5 32-bit values, although particular message types might not make use
of all of these values.
</para>
  </section>
</chapter>
<chapter id='Flow_Control_and_Concurrency'>
<title>Flow Control and Concurrency</title>
<!-- .XS -->
<!-- \*(SN Flow Control and Concurrency -->
<!-- .XE -->
<para>
Whenever the server is writing to a given connection,
it is permissible for the server to stop reading from that connection
(but if the writing would block, it must continue to service other connections).
The server is not required to buffer more than a single request per connection
at one time.
For a given connection to the server,
a client can block while reading from the connection
but should undertake to read (events and errors) when writing would block.
Failure on the part of a client to obey this rule could result
in a deadlocked connection,
although deadlock is probably unlikely unless either
the transport layer has very little buffering or the client attempts to
send large numbers of requests without ever reading replies or checking for
errors and events.
</para>
<para>
Whether or not a server is implemented with internal concurrency,
the overall effect must be as if individual requests are executed to completion
in some serial order,
and requests from a given connection must be executed in delivery order
(that is, the total execution order is a shuffle of the individual streams).
The execution of a request includes validating all arguments,
collecting all data for any reply,
and generating and queueing all required events.
However,
it does not include the actual transmission of the reply and the events.
In addition, the effect of any other cause that can generate multiple events
(for example, activation of a grab or pointer motion) must effectively generate
and queue all required events indivisibly with respect to all other causes
and requests.
For a request from a given client,
any events destined for that client that are caused by executing the request
must be sent to the client before any reply or error is sent.
</para>
</chapter>
</part>
