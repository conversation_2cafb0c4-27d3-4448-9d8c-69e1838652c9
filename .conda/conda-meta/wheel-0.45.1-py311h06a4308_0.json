{"build": "py311h06a4308_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/linux-64", "constrains": [], "depends": ["python >=3.11,<3.12.0a0"], "extracted_package_dir": "/data1/home/<USER>/anaconda3/pkgs/wheel-0.45.1-py311h06a4308_0", "files": ["bin/wheel", "lib/python3.11/site-packages/wheel-0.45.1.dist-info/LICENSE.txt", "lib/python3.11/site-packages/wheel-0.45.1.dist-info/METADATA", "lib/python3.11/site-packages/wheel-0.45.1.dist-info/RECORD", "lib/python3.11/site-packages/wheel-0.45.1.dist-info/WHEEL", "lib/python3.11/site-packages/wheel-0.45.1.dist-info/entry_points.txt", "lib/python3.11/site-packages/wheel/__init__.py", "lib/python3.11/site-packages/wheel/__main__.py", "lib/python3.11/site-packages/wheel/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/wheel/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/wheel/__pycache__/_bdist_wheel.cpython-311.pyc", "lib/python3.11/site-packages/wheel/__pycache__/_setuptools_logging.cpython-311.pyc", "lib/python3.11/site-packages/wheel/__pycache__/bdist_wheel.cpython-311.pyc", "lib/python3.11/site-packages/wheel/__pycache__/macosx_libfile.cpython-311.pyc", "lib/python3.11/site-packages/wheel/__pycache__/metadata.cpython-311.pyc", "lib/python3.11/site-packages/wheel/__pycache__/util.cpython-311.pyc", "lib/python3.11/site-packages/wheel/__pycache__/wheelfile.cpython-311.pyc", "lib/python3.11/site-packages/wheel/_bdist_wheel.py", "lib/python3.11/site-packages/wheel/_setuptools_logging.py", "lib/python3.11/site-packages/wheel/bdist_wheel.py", "lib/python3.11/site-packages/wheel/cli/__init__.py", "lib/python3.11/site-packages/wheel/cli/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/wheel/cli/__pycache__/convert.cpython-311.pyc", "lib/python3.11/site-packages/wheel/cli/__pycache__/pack.cpython-311.pyc", "lib/python3.11/site-packages/wheel/cli/__pycache__/tags.cpython-311.pyc", "lib/python3.11/site-packages/wheel/cli/__pycache__/unpack.cpython-311.pyc", "lib/python3.11/site-packages/wheel/cli/convert.py", "lib/python3.11/site-packages/wheel/cli/pack.py", "lib/python3.11/site-packages/wheel/cli/tags.py", "lib/python3.11/site-packages/wheel/cli/unpack.py", "lib/python3.11/site-packages/wheel/macosx_libfile.py", "lib/python3.11/site-packages/wheel/metadata.py", "lib/python3.11/site-packages/wheel/util.py", "lib/python3.11/site-packages/wheel/vendored/__init__.py", "lib/python3.11/site-packages/wheel/vendored/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/LICENSE", "lib/python3.11/site-packages/wheel/vendored/packaging/LICENSE.APACHE", "lib/python3.11/site-packages/wheel/vendored/packaging/LICENSE.BSD", "lib/python3.11/site-packages/wheel/vendored/packaging/__init__.py", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_elffile.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_manylinux.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_musllinux.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_parser.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_structures.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/markers.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/requirements.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/specifiers.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/tags.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/utils.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/version.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/_elffile.py", "lib/python3.11/site-packages/wheel/vendored/packaging/_manylinux.py", "lib/python3.11/site-packages/wheel/vendored/packaging/_musllinux.py", "lib/python3.11/site-packages/wheel/vendored/packaging/_parser.py", "lib/python3.11/site-packages/wheel/vendored/packaging/_structures.py", "lib/python3.11/site-packages/wheel/vendored/packaging/_tokenizer.py", "lib/python3.11/site-packages/wheel/vendored/packaging/markers.py", "lib/python3.11/site-packages/wheel/vendored/packaging/requirements.py", "lib/python3.11/site-packages/wheel/vendored/packaging/specifiers.py", "lib/python3.11/site-packages/wheel/vendored/packaging/tags.py", "lib/python3.11/site-packages/wheel/vendored/packaging/utils.py", "lib/python3.11/site-packages/wheel/vendored/packaging/version.py", "lib/python3.11/site-packages/wheel/vendored/vendor.txt", "lib/python3.11/site-packages/wheel/wheelfile.py"], "fn": "wheel-0.45.1-py311h06a4308_0.conda", "license": "MIT", "link": {"source": "/data1/home/<USER>/anaconda3/pkgs/wheel-0.45.1-py311h06a4308_0", "type": 1}, "md5": "7b4e94840008c93c945377afd3629a1c", "name": "wheel", "package_tarball_full_path": "/data1/home/<USER>/anaconda3/pkgs/wheel-0.45.1-py311h06a4308_0.conda", "paths_data": {"paths": [{"_path": "bin/wheel", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/croot/wheel_1737990206319/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_p", "sha256": "9a454821f0d68ed345128df773742cc9dd845c1d02fe9d12726aec29a74e058b", "sha256_in_prefix": "6e69c5ee3e5be666cc4d581a1f2d85e9cd287b27aaf6c460e00916d9308b219b", "size_in_bytes": 462}, {"_path": "lib/python3.11/site-packages/wheel-0.45.1.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "sha256_in_prefix": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "size_in_bytes": 1107}, {"_path": "lib/python3.11/site-packages/wheel-0.45.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "b04e96136b82c11487b79640c093a7344af76607ec497a24f4b87b2518590a60", "sha256_in_prefix": "b04e96136b82c11487b79640c093a7344af76607ec497a24f4b87b2518590a60", "size_in_bytes": 2313}, {"_path": "lib/python3.11/site-packages/wheel-0.45.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "5327908535969578df40fd8fc7948997b01a7742b52266d566476425504e4fb2", "sha256_in_prefix": "5327908535969578df40fd8fc7948997b01a7742b52266d566476425504e4fb2", "size_in_bytes": 3180}, {"_path": "lib/python3.11/site-packages/wheel-0.45.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "lib/python3.11/site-packages/wheel-0.45.1.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "sha256_in_prefix": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "size_in_bytes": 104}, {"_path": "lib/python3.11/site-packages/wheel/__init__.py", "path_type": "hardlink", "sha256": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "sha256_in_prefix": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "size_in_bytes": 59}, {"_path": "lib/python3.11/site-packages/wheel/__main__.py", "path_type": "hardlink", "sha256": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "sha256_in_prefix": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "size_in_bytes": 455}, {"_path": "lib/python3.11/site-packages/wheel/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "d988d2f218f3bd9baaa90bf7cefc76b2023af81cdb4d13acec46c915f80fdb2a", "sha256_in_prefix": "d988d2f218f3bd9baaa90bf7cefc76b2023af81cdb4d13acec46c915f80fdb2a", "size_in_bytes": 243}, {"_path": "lib/python3.11/site-packages/wheel/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "4e15dde9c09e09ca03d0bb68bebd795bcb39f4bb9d410d8026f77339311093e9", "sha256_in_prefix": "4e15dde9c09e09ca03d0bb68bebd795bcb39f4bb9d410d8026f77339311093e9", "size_in_bytes": 1048}, {"_path": "lib/python3.11/site-packages/wheel/__pycache__/_bdist_wheel.cpython-311.pyc", "path_type": "hardlink", "sha256": "7b93f3d85de4550532cdda54a9d053420add758f86fff2d994bc1f09a99921b2", "sha256_in_prefix": "7b93f3d85de4550532cdda54a9d053420add758f86fff2d994bc1f09a99921b2", "size_in_bytes": 28529}, {"_path": "lib/python3.11/site-packages/wheel/__pycache__/_setuptools_logging.cpython-311.pyc", "path_type": "hardlink", "sha256": "32f4e180e2a30712e3be8faac16cd8f3e4b6a60be8d57a155bf448a300e294d1", "sha256_in_prefix": "32f4e180e2a30712e3be8faac16cd8f3e4b6a60be8d57a155bf448a300e294d1", "size_in_bytes": 1460}, {"_path": "lib/python3.11/site-packages/wheel/__pycache__/bdist_wheel.cpython-311.pyc", "path_type": "hardlink", "sha256": "853d88c8877b2b586fbc93386af3dda8e7b055cd908207fe5e4bd6f94f1d7a64", "sha256_in_prefix": "853d88c8877b2b586fbc93386af3dda8e7b055cd908207fe5e4bd6f94f1d7a64", "size_in_bytes": 876}, {"_path": "lib/python3.11/site-packages/wheel/__pycache__/macosx_libfile.cpython-311.pyc", "path_type": "hardlink", "sha256": "218a681c42dbf2ee2110d927cb66f779b8dbfabaf2d8831d2c7458af1d3c63f9", "sha256_in_prefix": "218a681c42dbf2ee2110d927cb66f779b8dbfabaf2d8831d2c7458af1d3c63f9", "size_in_bytes": 17803}, {"_path": "lib/python3.11/site-packages/wheel/__pycache__/metadata.cpython-311.pyc", "path_type": "hardlink", "sha256": "0102c0d52089129fab9af457d78e33c08a74fc235db900a34112b937153cb59e", "sha256_in_prefix": "0102c0d52089129fab9af457d78e33c08a74fc235db900a34112b937153cb59e", "size_in_bytes": 9698}, {"_path": "lib/python3.11/site-packages/wheel/__pycache__/util.cpython-311.pyc", "path_type": "hardlink", "sha256": "fff6a77e4fdf5e58e4cf57b9cbbcf9bbd883990d2b5f3d092463239ce4c02f42", "sha256_in_prefix": "fff6a77e4fdf5e58e4cf57b9cbbcf9bbd883990d2b5f3d092463239ce4c02f42", "size_in_bytes": 1007}, {"_path": "lib/python3.11/site-packages/wheel/__pycache__/wheelfile.cpython-311.pyc", "path_type": "hardlink", "sha256": "72fb9a85063307442624ee5edb8efea0b1c4c97846a2e9e7e02dab55d9a0f209", "sha256_in_prefix": "72fb9a85063307442624ee5edb8efea0b1c4c97846a2e9e7e02dab55d9a0f209", "size_in_bytes": 12542}, {"_path": "lib/python3.11/site-packages/wheel/_bdist_wheel.py", "path_type": "hardlink", "sha256": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "sha256_in_prefix": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "size_in_bytes": 21694}, {"_path": "lib/python3.11/site-packages/wheel/_setuptools_logging.py", "path_type": "hardlink", "sha256": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "sha256_in_prefix": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "size_in_bytes": 781}, {"_path": "lib/python3.11/site-packages/wheel/bdist_wheel.py", "path_type": "hardlink", "sha256": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "sha256_in_prefix": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "size_in_bytes": 1107}, {"_path": "lib/python3.11/site-packages/wheel/cli/__init__.py", "path_type": "hardlink", "sha256": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "sha256_in_prefix": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "size_in_bytes": 4402}, {"_path": "lib/python3.11/site-packages/wheel/cli/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "583b97b2d3d92f3a5f4e6c1b7d571ce58f464c71bf811342285aff826950ebd5", "sha256_in_prefix": "583b97b2d3d92f3a5f4e6c1b7d571ce58f464c71bf811342285aff826950ebd5", "size_in_bytes": 7766}, {"_path": "lib/python3.11/site-packages/wheel/cli/__pycache__/convert.cpython-311.pyc", "path_type": "hardlink", "sha256": "0fca97af7a954b003f970fe1199e68431f6df920045c77c9117ced7f1a9a7c5d", "sha256_in_prefix": "0fca97af7a954b003f970fe1199e68431f6df920045c77c9117ced7f1a9a7c5d", "size_in_bytes": 18549}, {"_path": "lib/python3.11/site-packages/wheel/cli/__pycache__/pack.cpython-311.pyc", "path_type": "hardlink", "sha256": "45426c909a721b7f2de4f99806afbb61fd5b86f3a7c21e4529b500aa0f5734fd", "sha256_in_prefix": "45426c909a721b7f2de4f99806afbb61fd5b86f3a7c21e4529b500aa0f5734fd", "size_in_bytes": 5824}, {"_path": "lib/python3.11/site-packages/wheel/cli/__pycache__/tags.cpython-311.pyc", "path_type": "hardlink", "sha256": "17f0ef684f1d98bf5473ace8033607283d993ad5d6c73e96b07e7b82ebc33845", "sha256_in_prefix": "17f0ef684f1d98bf5473ace8033607283d993ad5d6c73e96b07e7b82ebc33845", "size_in_bytes": 7920}, {"_path": "lib/python3.11/site-packages/wheel/cli/__pycache__/unpack.cpython-311.pyc", "path_type": "hardlink", "sha256": "fab75ac62166b7277579b323dbdf12f27afca7190b3db8ad519ef774351be0dd", "sha256_in_prefix": "fab75ac62166b7277579b323dbdf12f27afca7190b3db8ad519ef774351be0dd", "size_in_bytes": 1748}, {"_path": "lib/python3.11/site-packages/wheel/cli/convert.py", "path_type": "hardlink", "sha256": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "sha256_in_prefix": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "size_in_bytes": 12634}, {"_path": "lib/python3.11/site-packages/wheel/cli/pack.py", "path_type": "hardlink", "sha256": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "sha256_in_prefix": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "size_in_bytes": 3103}, {"_path": "lib/python3.11/site-packages/wheel/cli/tags.py", "path_type": "hardlink", "sha256": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "sha256_in_prefix": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "size_in_bytes": 4760}, {"_path": "lib/python3.11/site-packages/wheel/cli/unpack.py", "path_type": "hardlink", "sha256": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "sha256_in_prefix": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "size_in_bytes": 1021}, {"_path": "lib/python3.11/site-packages/wheel/macosx_libfile.py", "path_type": "hardlink", "sha256": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "sha256_in_prefix": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "size_in_bytes": 16572}, {"_path": "lib/python3.11/site-packages/wheel/metadata.py", "path_type": "hardlink", "sha256": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "sha256_in_prefix": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "size_in_bytes": 6171}, {"_path": "lib/python3.11/site-packages/wheel/util.py", "path_type": "hardlink", "sha256": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "sha256_in_prefix": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "size_in_bytes": 423}, {"_path": "lib/python3.11/site-packages/wheel/vendored/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/wheel/vendored/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "55055623b9c7af9f928deb13c0b38061e5e19a4ae7ee448d125af0fb0a6edf1a", "sha256_in_prefix": "55055623b9c7af9f928deb13c0b38061e5e19a4ae7ee448d125af0fb0a6edf1a", "size_in_bytes": 164}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "a489bfee027c8b640f292cefd1556575e5c094bdbdcf3695a03d54b58c6c0790", "sha256_in_prefix": "a489bfee027c8b640f292cefd1556575e5c094bdbdcf3695a03d54b58c6c0790", "size_in_bytes": 174}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_elffile.cpython-311.pyc", "path_type": "hardlink", "sha256": "3913ab3c46a0e7fb0b9685945f6bb47d7c9d627a76b3e263a80b02f38335d928", "sha256_in_prefix": "3913ab3c46a0e7fb0b9685945f6bb47d7c9d627a76b3e263a80b02f38335d928", "size_in_bytes": 5454}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_manylinux.cpython-311.pyc", "path_type": "hardlink", "sha256": "1c82ea8887f851628139bdda796d395537442f6c93017042166da5808024f069", "sha256_in_prefix": "1c82ea8887f851628139bdda796d395537442f6c93017042166da5808024f069", "size_in_bytes": 11050}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_musllinux.cpython-311.pyc", "path_type": "hardlink", "sha256": "d26432d63490d9f5f217a84eb9b4dc903e320a38aa681f2ea90ca42b82613f48", "sha256_in_prefix": "d26432d63490d9f5f217a84eb9b4dc903e320a38aa681f2ea90ca42b82613f48", "size_in_bytes": 5265}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_parser.cpython-311.pyc", "path_type": "hardlink", "sha256": "3da57cf7e022720c932f2b186e989195fcc120dec99a36407ce27b467fc006d0", "sha256_in_prefix": "3da57cf7e022720c932f2b186e989195fcc120dec99a36407ce27b467fc006d0", "size_in_bytes": 16288}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_structures.cpython-311.pyc", "path_type": "hardlink", "sha256": "58e8ac7ae591ba9f27f2f4cab1f2ee31af5650575c0ec0077f7d2b55807f931a", "sha256_in_prefix": "58e8ac7ae591ba9f27f2f4cab1f2ee31af5650575c0ec0077f7d2b55807f931a", "size_in_bytes": 3658}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-311.pyc", "path_type": "hardlink", "sha256": "e6a050ae9f343606d97e18105172efe9abfd83471799306c4f372db243a3f918", "sha256_in_prefix": "e6a050ae9f343606d97e18105172efe9abfd83471799306c4f372db243a3f918", "size_in_bytes": 8635}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/markers.cpython-311.pyc", "path_type": "hardlink", "sha256": "bd43f1a311b8534231c092cb5fd46b1eeb1b0c9ce6427e97e6acce2aac35d400", "sha256_in_prefix": "bd43f1a311b8534231c092cb5fd46b1eeb1b0c9ce6427e97e6acce2aac35d400", "size_in_bytes": 12023}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/requirements.cpython-311.pyc", "path_type": "hardlink", "sha256": "ba98c698daa35dba63838b87cda0257bb3654900c0463ca49945f27cdf7429bf", "sha256_in_prefix": "ba98c698daa35dba63838b87cda0257bb3654900c0463ca49945f27cdf7429bf", "size_in_bytes": 4693}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/specifiers.cpython-311.pyc", "path_type": "hardlink", "sha256": "0db4dbb4b7d29e3d90b65cc7264d79e3214fb38ab4fb44e66af6a3661ab65f74", "sha256_in_prefix": "0db4dbb4b7d29e3d90b65cc7264d79e3214fb38ab4fb44e66af6a3661ab65f74", "size_in_bytes": 42008}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/tags.cpython-311.pyc", "path_type": "hardlink", "sha256": "76eacc5cbad7653a2bc4d2410f3c519c01e9406ea914c4f042e7ddba9800aa31", "sha256_in_prefix": "76eacc5cbad7653a2bc4d2410f3c519c01e9406ea914c4f042e7ddba9800aa31", "size_in_bytes": 24604}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "d775993dc79e70dbdbc7312777765400b98fea67a6455638f8bed54042d86709", "sha256_in_prefix": "d775993dc79e70dbdbc7312777765400b98fea67a6455638f8bed54042d86709", "size_in_bytes": 8249}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/version.cpython-311.pyc", "path_type": "hardlink", "sha256": "929ce76920a992731e7a1ed9458072f988636531a7987fd2aa634e73086d0db6", "sha256_in_prefix": "929ce76920a992731e7a1ed9458072f988636531a7987fd2aa634e73086d0db6", "size_in_bytes": 21426}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/_elffile.py", "path_type": "hardlink", "sha256": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "sha256_in_prefix": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "size_in_bytes": 3266}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "sha256_in_prefix": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "size_in_bytes": 9588}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "sha256_in_prefix": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "size_in_bytes": 2674}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/_parser.py", "path_type": "hardlink", "sha256": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "sha256_in_prefix": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "size_in_bytes": 10347}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "sha256_in_prefix": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "size_in_bytes": 5292}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/markers.py", "path_type": "hardlink", "sha256": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "sha256_in_prefix": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "size_in_bytes": 8232}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/requirements.py", "path_type": "hardlink", "sha256": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "sha256_in_prefix": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "size_in_bytes": 2933}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/specifiers.py", "path_type": "hardlink", "sha256": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "sha256_in_prefix": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "size_in_bytes": 39778}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/tags.py", "path_type": "hardlink", "sha256": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "sha256_in_prefix": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "size_in_bytes": 18950}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/utils.py", "path_type": "hardlink", "sha256": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "sha256_in_prefix": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "size_in_bytes": 5268}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/version.py", "path_type": "hardlink", "sha256": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "sha256_in_prefix": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "size_in_bytes": 16234}, {"_path": "lib/python3.11/site-packages/wheel/vendored/vendor.txt", "path_type": "hardlink", "sha256": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "sha256_in_prefix": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "size_in_bytes": 16}, {"_path": "lib/python3.11/site-packages/wheel/wheelfile.py", "path_type": "hardlink", "sha256": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "sha256_in_prefix": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "size_in_bytes": 8411}], "paths_version": 1}, "requested_spec": "None", "sha256": "cc9ee44ce39eba34faff69cd0cfbe6cdcef314eea5273099febcbafd0fbaf101", "size": 154981, "subdir": "linux-64", "timestamp": 1737990272000, "url": "https://repo.anaconda.com/pkgs/main/linux-64/wheel-0.45.1-py311h06a4308_0.conda", "version": "0.45.1"}