
<chapter id='XKB_Protocol_Requests'>
<title>XKB Protocol Requests</title>

<para>
This document uses the syntactic conventions and common types defined by the
specification of the core X protocol with a number of additions, which are
detailed below.
</para>

<sect1 id='Errors'>
<title>Errors</title>

<para>
If a client attempts to use any other XKB request except <emphasis>
XkbUseExtension</emphasis>
 before the extension is properly initialized, XKB reports an <emphasis>
Access</emphasis>
 error and ignores the request. XKB is properly initialized once <emphasis>
XkbUseExtension</emphasis>
 reports that the client has asked for a supported or compatible version of the
extension.
</para>


<sect2 id='Keyboard_Errors'>
<title>Keyboard Errors</title>

<para>
In addition to all of the errors defined by the core protocol, the X Keyboard
Extension defines a single error, <emphasis>
Keyboard</emphasis>
, which indicates that some request specified an illegal device identifier or
an extension device that is not a member of an appropriate. Unless otherwise
noted, any request with an argument of type KB_DEVICESPEC can cause <emphasis>
Keyboard</emphasis>
 errors if an illegal or inappropriate device is specified.
</para>


<para>
When the extension reports a Keyboard error, the most significant byte of the
<emphasis>
resource_id</emphasis>
 is a further refinement of the error cause, as defined in the table below. The
least significant byte contains the device, class, or feedback id as indicated:
</para>

<informaltable frame='topbot'>
<?dbfo keep-together="always" ?>
<tgroup cols='4' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='2.0*'/>
<colspec colname='c2' colwidth='1.0*'/>
<colspec colname='c3' colwidth='3.0*'/>
<colspec colname='c4' colwidth='2.0*'/>
<thead>
  <row rowsep='1'>
    <entry>high-order byte</entry>
    <entry>value</entry>
    <entry>meaning</entry>
    <entry>low-order byte</entry>
  </row>
</thead>
<tbody>
  <row>
    <entry>XkbErr_BadDevice</entry>
    <entry>0xff</entry>
    <entry>device not found</entry>
    <entry>device id</entry>
  </row>
  <row>
    <entry>XkbErr_BadClass</entry>
    <entry>0xfe</entry>
    <entry>device found, but is the wrong class</entry>
    <entry>class id</entry>
  </row>
  <row>
    <entry>XkbErr_BadId</entry>
    <entry>0xfd</entry>
    <entry>device found, class ok, but device does not have a feedback with the
indicated id</entry>
    <entry>feedback id</entry>
  </row>
</tbody>
</tgroup>
</informaltable>

</sect2>
<sect2 id='Side_Effects_of_Errors'>
<title>Side-Effects of Errors</title>

<para>
With the exception of <emphasis>
Alloc</emphasis>
 or <emphasis>
Implementation</emphasis>
 errors, which might result in an inconsistent internal state, no XKB request
that reports an error condition has any effect. Unless otherwise stated,
requests which update some aspect of the keyboard description will not apply
only part of a request — if part of a request fails, the whole thing is
ignored.
</para>


</sect2>
</sect1>
<sect1 id='Common_Types'>
<title>Common Types</title>

<para>
The following types are used in the request and event definitions in subsequent
sections:
</para>

<informaltable frame='topbot'>
<?dbfo keep-together="auto" ?>
<tgroup cols='2' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
<colspec colname='c2' colwidth='2.0*'/>
<thead>
  <row rowsep='1'>
    <entry>Name</entry>
    <entry>Value</entry>
  </row>
</thead>
<tbody>
  <row>
    <entry>LISTofITEMs</entry>
    <entry>	The type LISTofITEMs is special. It is similar to the
LISTofVALUE defined by the core protocol, but the elements of a LISTofITEMs are
not necessarily all the same size. The use of a BITMASK to indicate which
members are present is optional for a LISTofITEMs — it is possible for the
set of elements to be derived from one or more fields of the request.</entry>
  </row>
  <row>
    <entry>KB_DEVICESPEC</entry>
    <entry>8 bit unsigned integer, <emphasis>
UseCoreKbd, or UseCorePtr</emphasis>
</entry>
  </row>
  <row>
    <entry>KB_LEDCLASSSPEC</entry>
    <entry>{	<emphasis>
KbdFeedbackClass</emphasis>
, <emphasis>
LedFeedbackClass</emphasis>
, <emphasis>
DfltXIClass</emphasis>
, <emphasis>
AllXIClasses</emphasis>
, <emphasis>
XINone</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_BELLCLASSSPEC</entry>
    <entry>{	<emphasis>
KbdFeedbackClass</emphasis>
, <emphasis>
BellFeedbackClass</emphasis>
, <emphasis>
DfltXIClass</emphasis>
, <emphasis>
AllXIClasses</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_IDSPEC</entry>
    <entry>8 bit unsigned integer or <emphasis>
DfltXIId</emphasis>
</entry>
  </row>
  <row>
    <entry>KB_VMODMASK</entry>
    <entry>	CARD16, each bit corresponds to a virtual modifier</entry>
  </row>
  <row>
    <entry>KB_GROUPMASK</entry>
    <entry>{	<emphasis>
Group1</emphasis>
, <emphasis>
Group2</emphasis>
, <emphasis>
Group3</emphasis>
, <emphasis>
Group4</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_GROUPSWRAP</entry>
    <entry>{	<emphasis>
WrapIntoRange</emphasis>
, <emphasis>
ClampIntoRange</emphasis>
, <emphasis>
RedirectIntoRange</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_GROUPINFO</entry>
    <entry>{	groupsWrap: KB_GROUPSWRAP
redirectGroup: 1…4,
numGroups: 1…4 }</entry>
  </row>
  <row>
    <entry>KB_NKNDETAILSMASK</entry>
    <entry>{	<emphasis>
NKN_Keycodes</emphasis>
, NKN_Geometry, <emphasis>
NKN_DeviceID</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_STATEMASK</entry>
    <entry>	KEYBUTMASK or KB_GROUPMASK</entry>
  </row>
  <row>
    <entry>KB_STATEPARTMASK</entry>
    <entry>{	<emphasis>
ModifierState</emphasis>
, <emphasis>
ModifierBase</emphasis>
, <emphasis>
ModifierLatch</emphasis>
, <emphasis>
ModifierLock</emphasis>
, <emphasis>
GroupState</emphasis>
, <emphasis>
GroupBase</emphasis>
, <emphasis>
GroupLatch</emphasis>
, <emphasis>
GroupLock</emphasis>
, <emphasis>
CompatState</emphasis>
, <emphasis>
GrabMods</emphasis>
, <emphasis>
CompatGrabMods</emphasis>
, <emphasis>
LookupMods</emphasis>
, <emphasis>
CompatLookupMods</emphasis>
, <emphasis>
PointerButtons</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_BOOLCTRLMASK</entry>
    <entry>{	<emphasis>
RepeatKeys</emphasis>
, <emphasis>
SlowKeys</emphasis>
, <emphasis>
BounceKeys</emphasis>
, <emphasis>
StickyKeys</emphasis>
, <emphasis>
MouseKeys</emphasis>
, <emphasis>
MouseKeysAccel</emphasis>
, <emphasis>
AccessXKeys</emphasis>
, <emphasis>
AccessXTimeout</emphasis>
, <emphasis>
AccessXFeedback</emphasis>
, <emphasis>
AudibleBell</emphasis>
, <emphasis>
Overlay1</emphasis>
, <emphasis>
Overlay2</emphasis>
, <emphasis>
IgnoreGroupLock</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_CONTROLSMASK</entry>
    <entry>{	<emphasis>
GroupsWrap, InternalMods</emphasis>
, <emphasis>
IgnoreLockMods</emphasis>
, <emphasis>
PerKeyRepeat</emphasis>
, <emphasis>
ControlsEnabled</emphasis>
 } or KB_BOOLCTRLMASK</entry>
  </row>
  <row>
    <entry>KB_MAPPARTMASK</entry>
    <entry>{	<emphasis>
KeyTypes</emphasis>
, <emphasis>
KeySyms</emphasis>
, <emphasis>
ModifierMap</emphasis>
, <emphasis>
ExplicitComponents</emphasis>
, <emphasis>
KeyActions</emphasis>
, <emphasis>
KeyBehaviors</emphasis>
, <emphasis>
VirtualMods</emphasis>
, <emphasis>
VirtualModMap</emphasis>
}</entry>
  </row>
  <row>
    <entry>KB_CMDETAILMASK</entry>
    <entry>{	<emphasis>
SymInterp</emphasis>
, <emphasis>
GroupCompat</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_NAMEDETAILMASK</entry>
    <entry>{	<emphasis>
KeycodesName</emphasis>
, <emphasis>
GeometryName</emphasis>
, <emphasis>
SymbolsName</emphasis>
,
	<emphasis>
PhysSymbolsName</emphasis>
, <emphasis>
TypesName</emphasis>
, <emphasis>
CompatName</emphasis>
, <emphasis>
KeyTypeNames</emphasis>
, <emphasis>
KTLevelNames</emphasis>
, <emphasis>
IndicatorNames</emphasis>
, <emphasis>
KeyNames</emphasis>
, <emphasis>
KeyAliases</emphasis>
, <emphasis>
VirtualModNames</emphasis>
, <emphasis>
GroupNames</emphasis>
, <emphasis>
RGNames</emphasis>
}</entry>
  </row>
  <row>
    <entry>KB_AXNDETAILMASK</entry>
    <entry>{	<emphasis>
AXN_SKPress</emphasis>
, <emphasis>
AXN_SKAccept</emphasis>
, <emphasis>
AXN_SKReject</emphasis>
, <emphasis>
AXN_SKRelease, AXN_BKAccept, AXN_BKReject, AXN_AXKWarning </emphasis>
}</entry>
  </row>
  <row>
    <entry>KB_AXSKOPTSMASK</entry>
    <entry>{	<emphasis>
AX_TwoKeys</emphasis>
, <emphasis>
AX_LatchToLock</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_AXFBOPTSMASK</entry>
    <entry>{	<emphasis>
AX_SKPressFB</emphasis>
, <emphasis>
AX_SKAcceptFB</emphasis>
, <emphasis>
AX_FeatureFB</emphasis>
, <emphasis>
AX_SlowWarnFB</emphasis>
, <emphasis>
AX_IndicatorFB</emphasis>
, <emphasis>
AX_StickyKeysFB</emphasis>
, <emphasis>
AX_SKReleaseFB</emphasis>
,<emphasis>
 AX_SKRejectFB</emphasis>
, <emphasis>
AX_BKRejectFB</emphasis>
, <emphasis>
AX_DumbBellFB</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_AXOPTIONSMASK</entry>
    <entry>	KB_AXFBOPTSMASK or KB_AXSKOPTSMASK</entry>
  </row>
  <row>
    <entry>KB_GBNDETAILMASK</entry>
    <entry>{	<emphasis>
GBN_Types</emphasis>
, <emphasis>
GBN_CompatMap</emphasis>
, <emphasis>
GBN_ClientSymbols</emphasis>
, <emphasis>
GBN_ServerSymbols</emphasis>
, <emphasis>
GBN_IndicatorMap</emphasis>
, <emphasis>
GBN_KeyNames</emphasis>
, <emphasis>
GBN_Geometry</emphasis>
, <emphasis>
GBN_OtherNames</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_BELLDETAILMASK</entry>
    <entry>{	<emphasis>
XkbAllBellNotifyEvents</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_MSGDETAILMASK</entry>
    <entry>{	<emphasis>
XkbAllActionMessages</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_EVENTTYPE</entry>
    <entry>{	<emphasis>
XkbNewKeyboardNotify</emphasis>
, <emphasis>
XkbMapNotify</emphasis>
, <emphasis>
XkbStateNotify</emphasis>
, <emphasis>
XkbControlsNotify</emphasis>
, <emphasis>
XkbIndicatorStateNotify</emphasis>
, <emphasis>
XkbIndicatorMapNotify</emphasis>
, <emphasis>
XkbNamesNotify</emphasis>
, <emphasis>
XkbCompatMapNotify</emphasis>
, <emphasis>
XkbBellNotify</emphasis>
, <emphasis>
XkbActionMessage</emphasis>
, <emphasis>
XkbAccessXNotify</emphasis>
, <emphasis>
XkbExtensionDeviceNotify</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_ACTION</entry>
    <entry>[	type: CARD8
data: LISTofCARD8 ]</entry>
  </row>
  <row>
    <entry>KB_BEHAVIOR</entry>
    <entry>[	type: CARD8, data: CARD 8 ]</entry>
  </row>
  <row>
    <entry>KB_MODDEF</entry>
    <entry>[	mask: KEYMASK,
mods: KEYMASK,
vmods: KB_VMODMASK ]</entry>
  </row>
  <row>
    <entry>KB_KTMAPENTRY</entry>
    <entry>[	active: BOOL,
level: CARD8,
mods: KB_MODDEF ]</entry>
  </row>
  <row>
    <entry>KB_KTSETMAPENTRY</entry>
    <entry>[	level: CARD8,
mods: KB_MODDEF ]</entry>
  </row>
  <row>
    <entry>KB_KEYTYPE</entry>
    <entry>[	mods: KB_MODDEF,
numLevels: CARD8,
map: LISTofKB_KTMAPENTRY,
preserve: LISTofKB_MODDEF ]</entry>
  </row>
  <row>
    <entry>KB_SETKEYTYPE</entry>
    <entry>[	 realMods: KEYMASK,
vmods: CARD16,
numLevels: CARD8,
map: LISTofKB_KTSETMAPENTRY,
preserve: LISTofKB_MODDEF ]</entry>
  </row>
  <row>
    <entry>KB_KEYSYMMAP</entry>
    <entry>[	 ktIndex: LISTofCARD8, width: CARD8
	 numGroups: 0…4,
	 groupsWrap: KB_GROUPSWRAP,
	 redirectGroup: 0…3,
	syms: LISTofKEYSYM ]</entry>
  </row>
  <row>
    <entry>KB_KEYVMODMAP</entry>
    <entry>[	key: KEYCODE, vmods: CARD16 ]</entry>
  </row>
  <row>
    <entry>KB_KEYMODMAP</entry>
    <entry>[	key: KEYCODE, mods: KEYMASK ]</entry>
  </row>
  <row>
    <entry>KB_EXPLICITMASK</entry>
    <entry>{	<emphasis>
ExplicitKeyType1</emphasis>
, <emphasis>
ExplicitKeyType2</emphasis>
, <emphasis>
ExplicitKeyType3</emphasis>
, <emphasis>
ExplicitKeyType4</emphasis>
, <emphasis>
ExplicitInterpret</emphasis>
, <emphasis>
ExplicitAutoRepeat</emphasis>
, <emphasis>
ExplicitBehavior</emphasis>
, <emphasis>
ExplicitVModMap</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_INDICATORMASK</entry>
    <entry>	CARD32, each bit corresponds to an indicator</entry>
  </row>
  <row>
    <entry>KB_IMFLAGS</entry>
    <entry>{	<emphasis>
IM_NoExplicit</emphasis>
, <emphasis>
IM_NoAutomatic</emphasis>
, <emphasis>
IM_LEDDrivesKB</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_IMMODSWHICH</entry>
    <entry>{	<emphasis>
IM_UseNone</emphasis>
, <emphasis>
IM_UseBase</emphasis>
, <emphasis>
IM_UseLatched</emphasis>
, <emphasis>
IM_UseLocked</emphasis>
, <emphasis>
IM_UseEffective</emphasis>
, <emphasis>
IM_UseCompat</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_IMGROUPSWHICH</entry>
    <entry>{	<emphasis>
IM_UseNone</emphasis>
, <emphasis>
IM_UseBase</emphasis>
, <emphasis>
IM_UseLatched</emphasis>
, <emphasis>
IM_UseLocked</emphasis>
, <emphasis>
IM_UseEffective</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_INDICATORMAP</entry>
    <entry>[	flags: CARD8,
mods: KB_MODDEF,
whichMods:
groups: KB_GROUPMASK,
whichGroups:
ctrls: KB_BOOLCTRLMASK ]</entry>
  </row>
  <row>
    <entry>KB_SYMINTERPMATCH</entry>
    <entry>{	<emphasis>
SI_NoneOf</emphasis>
, <emphasis>
SI_AnyOfOrNone</emphasis>
, <emphasis>
SI_AnyOf</emphasis>
, <emphasis>
SI_AllOf</emphasis>
, <emphasis>
SI_Exactly</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_SYMINTERP</entry>
    <entry>[	sym: KEYSYM,
	mods; KEYMASK,
	levelOneOnly: BOOL,
	match: KB_SYMINTERPMATCH,
	virtualMod: CARD8,
	autoRepeat: BOOL,
	lockingKey: BOOL ]</entry>
  </row>
  <row>
    <entry>KB_PCFMASK</entry>
    <entry>{	<emphasis>
PCF_DetectableAutorepeat</emphasis>
, <emphasis>
PCF_GrabsUseXkbState</emphasis>
, <emphasis>
PCF_AutoResetControls</emphasis>
, <emphasis>
PCF_LookupStateWhenGrabbed</emphasis>
, <emphasis>
PCF_SendEventUsesXKBState</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_LCFLAGSMASK</entry>
    <entry>{	<emphasis>
LC_Hidden</emphasis>
, <emphasis>
LC_Default</emphasis>
, <emphasis>
LC_Partial</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_LCSYMFLAGSMASK</entry>
    <entry>{	<emphasis>
LC_AlphanumericKeys</emphasis>
, <emphasis>
LC_ModifierKeys</emphasis>
, <emphasis>
LC_KeypadKeys</emphasis>
, <emphasis>
LC_FunctionKeys</emphasis>
, <emphasis>
LC_AlternateGroup</emphasis>
 }</entry>
  </row>
</tbody>
</tgroup>
</informaltable>

<para>
These types are used by the <emphasis>
XkbGetGeometry</emphasis>
 and <emphasis>
XkbSetGeometry</emphasis>
 requests:
</para>

<informaltable frame='topbot'>
<?dbfo keep-together="auto" ?>
<tgroup cols='2' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
<colspec colname='c2' colwidth='2.0*'/>
<thead>
  <row rowsep='1'>
    <entry>Name</entry>
    <entry>Value</entry>
  </row>
</thead>
<tbody>
  <row>
    <entry>KB_PROPERTY</entry>
    <entry>[	name, value: STRING8 ]</entry>
  </row>
  <row>
    <entry>KB_POINT</entry>
    <entry>[	x, y: CARD16 ]</entry>
  </row>
  <row>
    <entry>KB_OUTLINE</entry>
    <entry>[	cornerRadius: CARD8, points: LISTofKB_POINT ]</entry>
  </row>
  <row>
    <entry>KB_SHAPE</entry>
    <entry>[	name: ATOM, outlines: LISTofKB_OUTLINE
	primaryNdx, approxNdx: CARD8 ]</entry>
  </row>
  <row>
    <entry>KB_KEYNAME</entry>
    <entry>[ name: LISTofCHAR ]</entry>
  </row>
  <row>
    <entry>KB_KEYALIAS</entry>
    <entry>[	real: LISTofCHAR, alias: LISTofCHAR ]</entry>
  </row>
  <row>
    <entry>KB_KEY</entry>
    <entry>[	name: KB_KEYNAME, gap: INT16,
	shapeNdx, colorNdx: CARD8 ]</entry>
  </row>
  <row>
    <entry>KB_ROW</entry>
    <entry>[	top, left: INT16, vertical: BOOL, keys LISTofKB_KEY ]</entry>
  </row>
  <row>
    <entry>KB_OVERLAYKEY</entry>
    <entry>[	over, under: KB_KEYNAME ]</entry>
  </row>
  <row>
    <entry>KB_OVERLAYROW</entry>
    <entry>[	rowUnder: CARD8, keys: LISTofKB_OVERLAYKEY ]</entry>
  </row>
  <row>
    <entry>KB_OVERLAY</entry>
    <entry>[	sectionUnder: CARD8,
rows: LISTofKB_OVERLAYROW ]</entry>
  </row>
  <row>
    <entry>KB_SHAPEDOODAD</entry>
    <entry>[	name: ATOM, priority: CARD8, top, left: INT16,
	type: { SolidDoodad, OutlineDoodad },
	angle: INT16, width, height: CARD16
	colorNdx, shapeNdx: CARD8 ]</entry>
  </row>
  <row>
    <entry>KB_TEXTDOODAD</entry>
    <entry>[	name: ATOM, priority: CARD8, top, left: INT16,
	angle: INT16, width, height: CARD16,
	colorNdx: CARD8, text: STRING8, font: STRING8 ]</entry>
  </row>
  <row>
    <entry>KB_INDICATORDOODAD</entry>
    <entry>[	name: ATOM, priority: CARD8, top, left: INT16,
angle: INT16,
shapeNdx, onColorNdx, offColorNdx: CARD8 ]</entry>
  </row>
  <row>
    <entry>KB_LOGODOODAD</entry>
    <entry>[	name: ATOM, priority: CARD8, top, left: INT16,
	angle: INT16, colorNdx, shapeNdx: CARD8,
	logoName: STRING8 ]</entry>
  </row>
  <row>
    <entry>KB_DOODAD</entry>
    <entry>KB_SHAPEDOODAD, or KB_TEXTDOODAD, or KB_INDICATORDOODAD, or
KB_LOGODOODAD</entry>
  </row>
  <row>
    <entry>KB_SECTION</entry>
    <entry>[	name: ATOM,
	top, left, angle: INT16,
	width, height: CARD16,
	priority: CARD8,
	rows: LISTofKB_ROW,
	doodads: LISTofKB_DOODAD,
	overlays: LISTofKB_OVERLAY ]</entry>
  </row>
</tbody>
</tgroup>
</informaltable>

<para>
These types are used by <emphasis>
XkbGetDeviceInfo</emphasis>
 and <emphasis>
XkbSetDeviceInfo</emphasis>
:
</para>

<informaltable frame='topbot'>
<?dbfo keep-together="always" ?>
<tgroup cols='2' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
<colspec colname='c2' colwidth='2.0*'/>
<thead>
  <row rowsep='1'>
    <entry>Name</entry>
    <entry>Value</entry>
  </row>
</thead>
<tbody>
  <row>
    <entry>KB_XIDEVFEATUREMASK</entry>
    <entry>{	<emphasis>
XI_ButtonActions</emphasis>
, <emphasis>
XI_IndicatorNames</emphasis>
, <emphasis>
XI_IndicatorMaps</emphasis>
, <emphasis>
XI_IndicatorState</emphasis>
 }</entry>
  </row>
  <row>
    <entry>KB_XIFEATUREMASK</entry>
    <entry>{	 KB_XIDEVFEATURES or <emphasis>
XI_Keyboards</emphasis>
</entry>
  </row>
  <row>
    <entry>KB_XIDETAILMASK</entry>
    <entry>	{ KB_XIFEATURES or <emphasis>
XI_UnsupportedFeature</emphasis>
 <emphasis>
}</emphasis>
</entry>
  </row>
  <row>
    <entry>KB_DEVICELEDINFO</entry>
    <entry>[	ledClass: KB_LEDCLASSSPEC,
ledID: KB_IDSPEC,
physIndicators: CARD32,
state: CARD32,
names: LISTofATOM,
maps: LISTofKB_INDICATORMAP ]</entry>
  </row>
</tbody>
</tgroup>
</informaltable>

</sect1>
<sect1 id='Requests'>
<title>Requests</title>

<para>
This section lists all of the requests supported by the X Keyboard Extension,
separated into categories of related requests.
</para>


<sect2 id='Initializing_the_X_Keyboard_Extension'>
<title>Initializing the X Keyboard Extension</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbUseExtension</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>wantedMajor, wantedMinor:	 CARD16</entry>
    </row>
    <row>
      <entry role='protoreply'>
supported: BOOL
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
serverMajor, serverMinor: CARD16</entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
This request enables XKB extension capabilities for the client that issues the
request; the <emphasis>
wantedMajor</emphasis>
 and <emphasis>
wantedMinor</emphasis>
 fields specify the extension version in use by the requesting client. The
<emphasis>
supported</emphasis>
 field is <emphasis>
True</emphasis>
 if the server supports a compatible version, <emphasis>
False</emphasis>
 otherwise. The <emphasis>
serverMajor</emphasis>
 and <emphasis>
serverMinor</emphasis>
 fields return the actual version supported by the server.
</para>


<para>
Until a client explicitly and successfully requests the XKB extension, an XKB
capable server reports compatibility state in all core protocol events and
requests. Once a client asks for XKB extension semantics by issuing this
request, the server reports the extended XKB keyboard state in some core
protocol events and requests, as described in the overview section of this
specification.
</para>


<para>
Clients should issue an <emphasis>
XkbUseExtension</emphasis>
 request before using any other extension requests.
</para>


</sect2>
<sect2 id='Selecting_Events'>
<title>Selecting Events</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbSelectEvents</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec:	KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
affectWhich, clear, selectAll: KB_EVENTTYPE</entry>
    </row>
    <row>
      <entry role='protoname'>
affectMap, map: KB_MAPPARTMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
details: LISTofITEMs</entry>
    </row>

    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
, <emphasis>
Match</emphasis>
, <emphasis>
Value</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
This request updates the event masks of the keyboard indicated by <emphasis>
deviceSpec</emphasis>
 for this client. If <emphasis>
deviceSpec</emphasis>
 specifies an illegal device, a <emphasis>
Keyboard</emphasis>
 error results.
</para>


<para>
The <emphasis>
affectMap</emphasis>
 and <emphasis>
map</emphasis>
 fields specify changes to the event details mask for the <emphasis>
XkbMapNotify</emphasis>
 event. If any map components are set in <emphasis>
map</emphasis>
 but not in <emphasis>
affectMap</emphasis>
, a <emphasis>
Match</emphasis>
 error results. Otherwise, any map components that are set in <emphasis>
affectMap</emphasis>
 are set or cleared in the map notify details mask, depending on the value of
the corresponding field in <emphasis>
map</emphasis>
.
</para>


<para>
The <emphasis>
affectWhich</emphasis>
, <emphasis>
clear</emphasis>
, and <emphasis>
selectAll</emphasis>
 fields specify changes to any other event details masks. If any event types
are set in both <emphasis>
clear</emphasis>
 and <emphasis>
selectAll</emphasis>
, a <emphasis>
Match</emphasis>
 error results; if any event types are specified in either <emphasis>
clear</emphasis>
 or <emphasis>
selectAll</emphasis>
 but not in <emphasis>
affectWhich</emphasis>
, a <emphasis>
Match</emphasis>
 error results. Otherwise, the detail masks for any event types specified in
the <emphasis>
affectWhich</emphasis>
 field of this request are changed as follows:
</para>

<itemizedlist>
<listitem>
  <para>If the event type is also set in <emphasis>
clear</emphasis>
, the detail mask for the corresponding event is set to <emphasis>
0</emphasis>
 or <emphasis>
False</emphasis>
, as appropriate.
  </para>
</listitem>
<listitem>
  <para>If the event type is also set in <emphasis>
selectAll</emphasis>
, the detail mask for the corresponding event is set to include all legal
detail values for that type.
  </para>
</listitem>
<listitem>
  <para>If the event type is not set in either <emphasis>
clear</emphasis>
 or <emphasis>
selectAll</emphasis>
, the corresponding element of <emphasis>
details</emphasis>
 lists a set of explicit changes to the details mask for the event, as
described below.
  </para>
</listitem>
</itemizedlist>

<para>
Each entry of the <emphasis>
details</emphasis>
 list specifies changes to the event details mask for a single type of event,
and consists of an <emphasis>
affects</emphasis>
 mask and a <emphasis>
values</emphasis>
 mask. All details that are specified in <emphasis>
affects</emphasis>
 are set to the corresponding value from <emphasis>
values</emphasis>
; if any details are listed in <emphasis>
values</emphasis>
 but not in <emphasis>
affects</emphasis>
, a <emphasis>
Match</emphasis>
 error results.
</para>


<para>
The details list contains entries only for those event types, if any, that are
listed in the <emphasis>
affectWhich</emphasis>
 mask and not in either <emphasis>
clear</emphasis>
 or <emphasis>
selectAll</emphasis>
. When present, the items of the <emphasis>
details</emphasis>
 list appear in the following order:
</para>

<informaltable frame='none'>
<?dbfo keep-together="auto" ?>
<tgroup cols='3' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
<colspec colname='c2' colwidth='1.0*'/>
<colspec colname='c3' colwidth='1.0*'/>
<thead>
  <row rowsep='1'>
    <entry>Event Type</entry>
    <entry>Legal Details</entry>
    <entry>Type</entry>
  </row>
</thead>
<tbody>
  <row>
    <entry><emphasis>
XkbNewKeyboardNotify</emphasis>
</entry>
    <entry><emphasis>
KB_NKNDETAILSMASK</emphasis>
</entry>
    <entry><emphasis>
CARD16</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbStateNotify</emphasis>
</entry>
    <entry><emphasis>
KB_STATEPARTMASK</emphasis>
</entry>
    <entry><emphasis>
CARD16</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbControlsNotify</emphasis>
</entry>
    <entry><emphasis>
KB_CONTROLMASK</emphasis>
</entry>
    <entry><emphasis>
CARD32</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbIndicatorMapNotify</emphasis>
</entry>
    <entry><emphasis>
KB_INDICATORMASK</emphasis>
</entry>
    <entry><emphasis>
CARD32</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbIndicatorStateNotify</emphasis>
</entry>
    <entry><emphasis>
KB_INDICATORMASK</emphasis>
</entry>
    <entry><emphasis>
CARD32</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbNamesNotify</emphasis>
</entry>
    <entry><emphasis>
KB_NAMEDETAILMASK</emphasis>
</entry>
    <entry><emphasis>
CARD16</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbCompatMapNotify</emphasis>
</entry>
    <entry><emphasis>
KB_CMDETAILMASK</emphasis>
</entry>
    <entry><emphasis>
CARD8</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbBellNotify</emphasis>
</entry>
    <entry><emphasis>
KB_BELLDETAILMASK</emphasis>
</entry>
    <entry><emphasis>
CARD8</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbActionMessage</emphasis>
</entry>
    <entry><emphasis>
KB_MSGDETAILMASK</emphasis>
</entry>
    <entry><emphasis>
CARD8</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbAccessXNotify</emphasis>
</entry>
    <entry><emphasis>
KB_AXNDETAILMASK</emphasis>
</entry>
    <entry><emphasis>
CARD16</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbExtensionDeviceNotify</emphasis>
</entry>
    <entry><emphasis>
KB_XIDETAILMASK</emphasis>
</entry>
    <entry><emphasis>
CARD16</emphasis>
</entry>
  </row>
</tbody>
</tgroup>
</informaltable>

<para>
Detail masks for event types that are not specified in <emphasis>
affectWhich</emphasis>
 are not changed.
</para>


<para>
If any components are specified in a client’s event masks, the X server sends
the client an appropriate event whenever any of those components change state.
Unless explicitly modified, all event detail masks are empty. <link linkend='Events'>Events</link> describes all XKB events
and the conditions under which the server generates them.
</para>


</sect2>
<sect2 id='Generating_Named_Keyboard_Bells'>
<title>Generating Named Keyboard Bells</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbBell</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
bellClass: KB_BELLCLASSSPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
bellID: KB_IDSPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
percent: INT8</entry>
    </row>
    <row>
      <entry role='protoname'>
forceSound: BOOL</entry>
    </row>
    <row>
      <entry role='protoname'>
eventOnly: BOOL</entry>
    </row>
    <row>
      <entry role='protoname'>
pitch, duration: INT16</entry>
    </row>
    <row>
      <entry role='protoname'>
name: ATOM</entry>
    </row>
    <row>
      <entry role='protoname'>
window: WINDOW</entry>
    </row>

    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
, <emphasis>
Value</emphasis>
, <emphasis>
Match</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
This request generates audible bells and/or <emphasis>
XkbBellNotify</emphasis>
 events for the bell specified by the <emphasis>
bellClass</emphasis>
 and <emphasis>
bellID</emphasis>
 on the device specified by <emphasis>
deviceSpec</emphasis>
 at the specified <emphasis>
pitch</emphasis>
, <emphasis>
duration</emphasis>
 and volume (<emphasis>
percent</emphasis>
). If deviceSpec specifies a device that does not have a bell or keyboard
feedback, a <emphasis>
Keyboard</emphasis>
 error results.
</para>


<para>
If both <emphasis>
forceSound</emphasis>
 and <emphasis>
eventOnly</emphasis>
 are set, this request yields a <emphasis>
Match</emphasis>
 error. Otherwise, if <emphasis>
forceSound</emphasis>
 is <emphasis>
True</emphasis>
, this request always generates a sound and never generates an event; if
<emphasis>
eventOnly</emphasis>
 is <emphasis>
True</emphasis>
, it causes an event but no sound. If neither <emphasis>
forceSound</emphasis>
 nor <emphasis>
eventOnly</emphasis>
 are <emphasis>
True</emphasis>
, this request always generates an event; if the keyboard’s global <emphasis>
AudibleBell</emphasis>
 control is enabled, it also generates a sound.
</para>


<para>
Any bell event generated by this request contains all of the information about
the bell that was requested, including the symbolic name specified by <emphasis>
name</emphasis>
 and the event window specified by window. The <emphasis>
name</emphasis>
 and <emphasis>
window</emphasis>
 are not directly interpreted by XKB, but they must have the value <emphasis>
None</emphasis>
 or specify a legal Atom or Window, respectively. <emphasis>
XkbBellNotify</emphasis>
 events generated in response to core protocol or X input extension bell
requests always report <emphasis>
None</emphasis>
 as their <emphasis>
name</emphasis>
.
</para>


<para>
The <emphasis>
bellClass</emphasis>
, <emphasis>
bellID</emphasis>
, and <emphasis>
percent</emphasis>
 fields are interpreted as for the X input extension <emphasis>
DeviceBell</emphasis>
 request. If <emphasis>
pitch</emphasis>
 and <emphasis>
duration</emphasis>
 are zero, the server uses the corresponding values for that bell from the core
protocol or input extension, otherwise <emphasis>
pitch</emphasis>
 and <emphasis>
duration</emphasis>
 are interpreted as for the core protocol <emphasis>
ChangeKeyboardControl</emphasis>
 request; if they do not include legal values, a <emphasis>
Value</emphasis>
 error results. The <emphasis>
window</emphasis>
 field must specify a legal Window or have the value <emphasis>
None</emphasis>
, or a <emphasis>
Value</emphasis>
 error results. The name field must specify a legal Atom or have the value
<emphasis>
None</emphasis>
, or an <emphasis>
Atom</emphasis>
 error results. If an error occurs, this request has no other effect (i.e. does
not cause a sound or generate an event).
</para>


<para>
The <emphasis>
pitch</emphasis>
, <emphasis>
volume</emphasis>
, and <emphasis>
duration</emphasis>
 are suggested values for the bell, but XKB does not require the server to
honor them.
</para>


</sect2>
<sect2 id='Querying_and_Changing_Keyboard_State'>
<title>Querying and Changing Keyboard State</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbGetState</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
deviceID: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
mods, baseMods, latchedMods, lockedMods: KEYMASK
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
group, lockedGroup: KB_GROUP
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
baseGroup, latchedGroup: INT16
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
compatState: KEYMASK
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
grabMods, compatGrabMods: KB_GROUP
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
lookupMods, compatLookupMods: KEYMASK
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
ptrBtnState: BUTMASK</entry>
    </row>
    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
This request returns a detailed description of the current state of the
keyboard specified by <emphasis>
deviceSpec</emphasis>
.
</para>


<para>
The <emphasis>
deviceID</emphasis>
 return value contains the input extension identifier for the specified device,
or <emphasis>
0</emphasis>
 if the server does not support the input extension.
</para>


<para>
The <emphasis>
baseMods</emphasis>
 return value reports the modifiers that are set because one or more modifier
keys are logically down. The <emphasis>
latchedMods</emphasis>
 and <emphasis>
lockedMods</emphasis>
 return values report the modifiers that are latched or locked respectively.
The <emphasis>
mods</emphasis>
 return value reports the effective modifier mask which results from the
current combination of base, latched and locked modifiers.
</para>


<para>
The <emphasis>
baseGroup</emphasis>
 return value reports the group state selected by group shift keys that are
logically down. The <emphasis>
latchedGroup</emphasis>
 and <emphasis>
lockedGroup</emphasis>
 return values detail the effects of latching or locking group shift keys and
<emphasis>
XkbLatchLockState</emphasis>
 requests. The <emphasis>
group</emphasis>
 return value reports the effective keyboard group which results from the
current combination of base, latched and locked group values.
</para>


<para>
The <emphasis>
lookupMods</emphasis>
 return value reports the lookup modifiers, which consist of the current
effective modifiers minus any server internal modifiers. The <emphasis>
grabMods</emphasis>
 return value reports the grab modifiers, which consist of the lookup modifiers
minus any members of the ignore locks mask that are not either latched or
logically depressed. <link linkend='Keyboard_State'>Keyboard
State</link> describes the lookup modifiers and grab modifiers in more detail.
</para>


<para>
The <emphasis>
ptrBtnState</emphasis>
 return value reports the current logical state of up to five buttons on the
core pointer device.
</para>


<para>
The <emphasis>
compatState</emphasis>
 return value reports the compatibility state that corresponds to the effective
keyboard group and modifier state. The <emphasis>
compatLookupMods</emphasis>
 and <emphasis>
compatGrabMods</emphasis>
 return values report the core protocol compatibility states that correspond to
the XKB lookup and grab state. All of the compatibility states are computed by
applying the group compatibility mapping to the corresponding XKB modifier and
group states, as described in <link linkend='Group_Compatibility_Map'>
Group Compatibility Map</link>.
</para>



<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbLatchLockState</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
affectModLocks, modLocks: KEYMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
lockGroup: BOOL</entry>
    </row>
    <row>
      <entry role='protoname'>
groupLock: KB_GROUP</entry>
    </row>
    <row>
      <entry role='protoname'>
affectModLatches,modLatches: KEYMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
latchGroup: BOOL</entry>
    </row>
    <row>
      <entry role='protoname'>
groupLatch: INT16</entry>
    </row>

    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
, <emphasis>
Value</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
This request locks or latches keyboard modifiers and group state for the device
specified by <emphasis>
deviceSpec</emphasis>
. If <emphasis>
deviceSpec</emphasis>
 specifies an illegal or non-keyboard device, a <emphasis>
Keyboard</emphasis>
 error occurs.
</para>


<para>
The locked state of any modifier specified in the <emphasis>
affectModLocks</emphasis>
 mask is set to the corresponding value from <emphasis>
modLocks</emphasis>
. If <emphasis>
lockGroup</emphasis>
 is <emphasis>
True</emphasis>
, the locked keyboard group is set to the group specified by <emphasis>
groupLock</emphasis>
. If any modifiers are set in <emphasis>
modLocks</emphasis>
 but not <emphasis>
affectModLocks</emphasis>
, a <emphasis>
Match</emphasis>
 error occurs.
</para>


<para>
The latched state of any modifier specified in the <emphasis>
affectModLatches</emphasis>
 mask is set to the corresponding value from <emphasis>
modLatches</emphasis>
. If <emphasis>
latchGroup</emphasis>
 is <emphasis>
True</emphasis>
, the latched keyboard group is set to the group specified by <emphasis>
groupLatch</emphasis>
. if any modifiers are set in <emphasis>
modLatches</emphasis>
 but not in <emphasis>
affectModLatches</emphasis>
, a <emphasis>
Match</emphasis>
 error occurs.
</para>


<para>
If the locked group exceeds the maximum number of groups permitted for the
specified keyboard, it is wrapped or truncated back into range as specified by
the global <emphasis>
GroupsWrap</emphasis>
<emphasis>
 </emphasis>
control. No error results from an out-of-range group specification.
</para>


<para>
After changing the locked and latched modifiers and groups as specified, the X
server recalculates the effective and compatibility keyboard state and
generates <emphasis>
XkbStateNotify</emphasis>
 events as appropriate if any state components have changed. Changing the
keyboard state might also turn indicators on or off which can cause <emphasis>
XkbIndicatorStateNotify</emphasis>
 events as well.
</para>


<para>
If any errors occur, this request has no effect.
</para>


</sect2>
<sect2 id='Querying_and_Changing_Keyboard_Controls'>
<title>Querying and Changing Keyboard Controls</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbGetControls</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
deviceID: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
mouseKeysDfltBtn: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
numGroups: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
groupsWrap: KB_GROUPINFO
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
internalMods,ignoreLockMods: KB_MODDEF
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
repeatDelay,repeatInterval: CARD16
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
slowKeysDelay, debounceDelay: CARD16
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
mouseKeysDelay, mouseKeysInterval: CARD16
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
mouseKeysTimeToMax, mouseKeysMaxSpeed: CARD16
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
mouseKeysCurve: INT16
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
accessXOptions: KB_AXOPTIONMASK
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
accessXTimeout: CARD16
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
accessXTimeoutOptionsMask, accessXTimeoutOptionValues: CARD16
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
accessXTimeoutMask,accessXTimeoutValues: CARD32
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
enabledControls: KB_BOOLCTRLMASK
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
perKeyRepeat: LISTofCARD8</entry>
    </row>

    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
This request returns the current values and status of all controls for the
keyboard specified by <emphasis>
deviceSpec</emphasis>
. If <emphasis>
deviceSpec</emphasis>
 specifies an illegal device a <emphasis>
Keyboard</emphasis>
 error results. On return, the <emphasis>
deviceID</emphasis>
 specifies the identifier of the requested device or zero if the server does
not support the input extension.
</para>


<para>
The <emphasis>
numGroups</emphasis>
 return value reports the current number of groups, and <emphasis>
groupsWrap</emphasis>
 reports the treatment of out-of-range groups, as described in <link linkend='Key_Symbol_Map'>Key Symbol Map</link>. The <emphasis>
internalMods</emphasis>
 and <emphasis>
ignoreLockMods</emphasis>
 return values report the current values of the server internal and ignore
locks modifiers as described in <link linkend='Keyboard_State'>
Keyboard State</link>. Both are modifier definitions (
<link linkend='Modifier_Definitions'>Modifier Definitions</link>) which
report the real modifiers, virtual modifiers, and the resulting combination of
real modifiers that are bound to the corresponding control.
</para>


<para>
The <emphasis>
repeatDelay</emphasis>
, <emphasis>
repeatInterval</emphasis>
, <emphasis>
slowKeysDelay</emphasis>
 and <emphasis>
debounceDelay</emphasis>
 fields report the current values of the for the autorepeat delay, autorepeat
interval, slow keys delay and bounce keys timeout, respectively. The <emphasis>
mouseKeysDelay</emphasis>
, <emphasis>
mouseKeysInterval</emphasis>
, <emphasis>
mouseKeysTimeToMax</emphasis>
 and <emphasis>
mouseKeysMaxSpeed</emphasis>
 and <emphasis>
mouseKeysCurve</emphasis>
 return values report the current acceleration applied to mouse keys, as
described in <link linkend='The_MouseKeysAccel_Control'>The MouseKeysAccel
Control</link>. All times are reported in milliseconds.
</para>


<para>
The <emphasis>
mouseKeysDfltBtn</emphasis>
 return value reports the current default pointer button for which events are
synthesized by the mouse keys server actions.
</para>


<para>
The <emphasis>
accessXOptions</emphasis>
 return value reports the current settings of the various AccessX options flags
which govern the behavior of the <emphasis>
StickyKeys</emphasis>
 control and of AccessX feedback.
</para>


<para>
The <emphasis>
accessXTimeout</emphasis>
 return value reports the length of time, in seconds, that the keyboard must
remain idle before AccessX controls are automatically changed; an <emphasis>
accessXTimeout</emphasis>
 of <emphasis>
0</emphasis>
 indicates that AccessX controls are not automatically changed. The <emphasis>
accessXTimeoutMask</emphasis>
 specifies the boolean controls to be changed if the AccessX timeout expires;
the <emphasis>
accessXTimeoutValues</emphasis>
 field specifies new values for all of the controls in the timeout mask. The
<emphasis>
accessXTimeoutOptionsMask</emphasis>
 field specifies the AccessX options to be changed when the AccessX timeout
expires; the <emphasis>
accessXTimeoutOptionValues</emphasis>
 return value reports the values to which they will be set.
</para>


<para>
The <emphasis>
enabledControls</emphasis>
 return value reports the current state of all of the global boolean controls.
</para>


<para>
The <emphasis>
perKeyRepeat</emphasis>
 array consists of one bit per key and reports the current autorepeat behavior
of each keyboard key; if a bit is set in <emphasis>
perKeyRepeat</emphasis>
, the corresponding key repeats if it is held down while global keyboard
autorepeat is enabled. This array parallels the core protocol and input
extension keyboard controls, if the autorepeat behavior of a key is changed via
the core protocol or input extension, those changes are automatically reflected
in the <emphasis>
perKeyRepeat</emphasis>
 array.
</para>



<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbSetControls</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
affectInternalRealMods, internalRealMods: KEYMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
affectInternalVirtualMods,internalVirtualMods: KB_VMODMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
affectIgnoreLockRealMods,ignoreLockRealMods: KB_MODMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
affectIgnoreLockVirtualMods,ignoreLockVirtualMods: KB_VMODMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
mouseKeysDfltBtn: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
groupsWrap: KB_GROUPINFO</entry>
    </row>
    <row>
      <entry role='protoname'>
accessXOptions: CARD16</entry>
    </row>
    <row>
      <entry role='protoname'>
affectEnabledControls: KB_BOOLCTRLMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
enabledControls: KB_BOOLCTRLMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
changeControls: KB_CONTROLMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
repeatDelay,repeatInterval: CARD16</entry>
    </row>
    <row>
      <entry role='protoname'>
slowKeysDelay, debounceDelay: CARD16</entry>
    </row>
    <row>
      <entry role='protoname'>
mouseKeysDelay, mouseKeysInterval: CARD16</entry>
    </row>
    <row>
      <entry role='protoname'>
mouseKeysTimeToMax, mouseKeysMaxSpeed: CARD16</entry>
    </row>
    <row>
      <entry role='protoname'>
mouseKeysCurve: INT16</entry>
    </row>
    <row>
      <entry role='protoname'>
accessXTimeout: CARD16</entry>
    </row>
    <row>
      <entry role='protoname'>
accessXTimeoutMask, accessXTimeoutValues: KB_BOOLCTRLMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
accessXTimeoutOptionsMask,accessXTimeoutOptionsValues: CARD16</entry>
    </row>
    <row>
      <entry role='protoname'>
perKeyRepeat: LISTofCARD8</entry>
    </row>

    <row>
      <entry role='protoerror'>Errors:<emphasis>
 Keyboard</emphasis>
, <emphasis>
Value</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
This request sets the keyboard controls indicated in <emphasis>
changeControls</emphasis>
 for the keyboard specified by <emphasis>
deviceSpec</emphasis>
. Each bit that is set in <emphasis>
changeControls</emphasis>
 indicates that one or more of the other request fields should be applied, as
follows:
</para>

<informaltable frame='topbot'>
<?dbfo keep-together="always" ?>
<tgroup cols='2' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
<colspec colname='c2' colwidth='2.0*'/>
<thead>
  <row rowsep='1'>
    <entry>Bit in changeControls</entry>
    <entry>Field(s) to be Applied</entry>
  </row>
</thead>
<tbody>
  <row>
    <entry><emphasis>
XkbRepeatKeysMask</emphasis>
</entry>
    <entry><emphasis>
repeatDelay</emphasis>
, <emphasis>
repeatInterval</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbSlowKeysMask</emphasis>
</entry>
    <entry><emphasis>
slowKeysDelay</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbStickyKeysMask</emphasis>
</entry>
    <entry><emphasis>
accessXOptions</emphasis>
 (only the <emphasis>
XkbAX_TwoKeys</emphasis>
<emphasis>
 </emphasis>
and the <emphasis>
XkbAX_LatchToLock</emphasis>
 options are affected)</entry>
  </row>
  <row>
    <entry><emphasis>
XkbBounceKeysMask</emphasis>
</entry>
    <entry><emphasis>
debounceDelay</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbMouseKeysMask</emphasis>
</entry>
    <entry><emphasis>
mouseKeysDfltBtn</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbMouseKeysAccelMask</emphasis>
</entry>
    <entry><emphasis>
mouseKeysDelay</emphasis>
, <emphasis>
mouseKeysInterval</emphasis>
, <emphasis>
mouseKeysCurve</emphasis>
, <emphasis>
mouseKeysTimeToMax</emphasis>
, <emphasis>
mouseKeysMaxSpeed</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbAccessXKeysMask</emphasis>
</entry>
    <entry><emphasis>
accessXOptions (all options)</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbAccessXTimeoutMask</emphasis>
</entry>
    <entry><emphasis>
accessXTimeout</emphasis>
, <emphasis>
accessXTimeoutMask</emphasis>
, <emphasis>
accessXTimeoutValues</emphasis>
, <emphasis>
accessXTimeoutOptionsMask</emphasis>
, <emphasis>
accessXTimeoutOptionsValues</emphasis>
</entry>
  </row>
  <row>
    <entry>XkbAccessXFeedbackMask</entry>
    <entry><emphasis>
accessXOptions</emphasis>
 (all options except those affected by the <emphasis>
XkbStickyKeysMask</emphasis>
 bit)</entry>
  </row>
  <row>
    <entry><emphasis>
XkbGroupsWrapMask</emphasis>
</entry>
    <entry><emphasis>
groupsWrap</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbInternalModsMask</emphasis>
</entry>
    <entry><emphasis>
affectInternalRealMods</emphasis>
, <emphasis>
internalRealMods</emphasis>
, <emphasis>
affectInternalVirtualMods</emphasis>
, <emphasis>
internalVirtualMods</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbIgnoreLockModsMask</emphasis>
</entry>
    <entry><emphasis>
affectIgnoreLockRealMods</emphasis>
, <emphasis>
ignoreLockRealMods</emphasis>
, <emphasis>
affectIgnoreLockVirtualMods</emphasis>
, <emphasis>
ignoreLockVirtualMods</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbPerKeyRepeatMask</emphasis>
</entry>
    <entry><emphasis>
perKeyRepeat</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbControlsEnabledMask</emphasis>
</entry>
    <entry><emphasis>
affectEnabledControls</emphasis>
, <emphasis>
enabledControls</emphasis>
</entry>
  </row>
</tbody>
</tgroup>
</informaltable>

<para>
If any other bits are set in <emphasis>
changeControls</emphasis>
, a <emphasis>
Value</emphasis>
 error results. If any of the bits listed above are not set in <emphasis>
changeControls</emphasis>
, the corresponding fields must have the value <emphasis>
0</emphasis>
, or a <emphasis>
Match</emphasis>
 error results.
</para>


<para>
If applied, <emphasis>
repeatDelay</emphasis>
 and <emphasis>
repeatInterval</emphasis>
 change the autorepeat characteristics of the keyboard, as described in
<link linkend='The_RepeatKeys_Control'>The RepeatKeys Control</link>. If
specified, <emphasis>
repeatDelay</emphasis>
 and <emphasis>
repeatInterval</emphasis>
 must both be non-zero or a <emphasis>
Value</emphasis>
 error results.
</para>


<para>
If applied, the <emphasis>
slowKeysDelay</emphasis>
 field specifies a new delay for the <emphasis>
SlowKeys</emphasis>
 control, as defined in <link linkend='The_SlowKeys_Control'>The
SlowKeys Control</link>. If specified, <emphasis>
slowKeysDelay</emphasis>
 must be non-zero, or a <emphasis>
Value</emphasis>
 error results.
</para>


<para>
If applied, the <emphasis>
debounceDelay</emphasis>
 field specifies a new delay for the <emphasis>
BounceKeys</emphasis>
 control, as described in <link linkend='The_BounceKeys_Control'>The
BounceKeys Control</link>. If present, the <emphasis>
debounceDelay</emphasis>
 must be non-zero or a <emphasis>
Value</emphasis>
 error results.
</para>


<para>
If applied, the <emphasis>
mouseKeysDfltBtn</emphasis>
 field specifies the core pointer button for which events are generated
whenever a <emphasis>
SA_PtrBtn</emphasis>
 or <emphasis>
SA_LockPtrBtn</emphasis>
 key action is activated. If present, <emphasis>
mouseKeysDfltBtn</emphasis>
 must specify a legal button for the core pointer device, or a <emphasis>
Value</emphasis>
 error results. <link linkend='Key_Actions'>Key
Actions</link> describes the <emphasis>
SA_PtrBtn</emphasis>
 and <emphasis>
SA_LockPtrBtn</emphasis>
 actions in more detail.
</para>


<para>
If applied, the <emphasis>
mouseKeysDelay</emphasis>
, <emphasis>
mouseKeysInterval</emphasis>
, <emphasis>
mouseKeysTimeToMax</emphasis>
, <emphasis>
mouseKeysMaxSpeed</emphasis>
 and <emphasis>
mouseKeysCurve</emphasis>
 fields change the rate at which the pointer moves when a key which generates a
<emphasis>
SA_MovePtr</emphasis>
 action is held down. <link linkend='The_MouseKeysAccel_Control'>The
MouseKeysAccel Control</link> describes these <emphasis>
MouseKeysAccel</emphasis>
 parameters in more detail. If defined, the <emphasis>
mouseKeysDelay</emphasis>
, <emphasis>
mouseKeysInterval</emphasis>
, <emphasis>
mouseKeysTimeToMax</emphasis>
 and <emphasis>
mouseKeysMaxSpeed</emphasis>
 values must all be greater than zero, or a <emphasis>
Value</emphasis>
 error results. The <emphasis>
mouseKeysCurve</emphasis>
 value must be greater than <emphasis>
-1000</emphasis>
 or a <emphasis>
Value</emphasis>
 error results.
</para>


<para>
If applied, the <emphasis>
accessXOptions</emphasis>
 field sets the AccessX options, which are described in detail in
<link linkend='The_AccessXKeys_Control'>The AccessXKeys Control</link>. If
either one of <emphasis>
XkbStickyKeysMask</emphasis>
 and <emphasis>
XkbAccessXFeedbackMask</emphasis>
 are set in <emphasis>
changeControls</emphasis>
 and <emphasis>
XkbAccessXKeysMask</emphasis>
 is not, only a subset of the AccessX options are changed, as described in the
table above; if both are set or if the <emphasis>
AccessXKeys</emphasis>
 bit is set in <emphasis>
changeControls</emphasis>
, all of the AccessX options are updated. Any bit in <emphasis>
accessXOptions</emphasis>
 whose interpretation is undefined must be zero, or a <emphasis>
Value</emphasis>
 error results.
</para>


<para>
If applied, the <emphasis>
accessXTimeout</emphasis>
, <emphasis>
accessXTimeoutMask</emphasis>
, <emphasis>
accessXTimeoutValues</emphasis>
, <emphasis>
accessXTimeoutOptionsMask</emphasis>
 and <emphasis>
accessXTimeoutOptionsValues</emphasis>
 fields change the behavior of the AccessX Timeout control, as described in
<link linkend='The_AccessXTimeout_Control'>The AccessXTimeout
Control</link>. The <emphasis>
accessXTimeout</emphasis>
 must be greater than zero, or a <emphasis>
Value</emphasis>
 error results. The <emphasis>
accessXTimeoutMask</emphasis>
 or <emphasis>
accessXTimeoutValues</emphasis>
 fields must specify only legal boolean controls, or a <emphasis>
Value</emphasis>
 error results. The <emphasis>
accessXTimeoutOptionsMask</emphasis>
 and <emphasis>
accessXTimeoutOptionsValues</emphasis>
 fields must contain only legal AccessX options or a <emphasis>
Value</emphasis>
 error results. If any bits are set in either values field but not in the
corresponding mask, a <emphasis>
Match</emphasis>
 error results.
</para>


<para>
If present, the <emphasis>
groupsWrap</emphasis>
 field specifies the treatment of out-of-range keyboard groups, as described in
<link linkend='Key_Symbol_Map'>Key Symbol Map</link>. If the
<emphasis>
groupsWrap</emphasis>
 field does not specify a legal treatment for out-of-range groups, a <emphasis>
Value</emphasis>
 error results.
</para>


<para>
If present, the <emphasis>
affectInternalRealMods</emphasis>
 field specifies the set of real modifiers to be changed in the internal
modifier definition and the <emphasis>
internalRealMods</emphasis>
 field specifies new values for those modifiers. The <emphasis>
affectInternalVirtualMods</emphasis>
 and <emphasis>
internalVirtualMods</emphasis>
 fields update the virtual modifier component of the modifier definition that
describes the internal modifiers in the same way. If any bits are set in either
values field but not in the corresponding mask field, a <emphasis>
Match</emphasis>
 error results.
</para>


<para>
If present, the <emphasis>
affectIgnoreLockRealMods</emphasis>
 field specifies the set of real modifiers to be changed in the ignore locks
modifier definition and the <emphasis>
ignoreLockRealMods</emphasis>
 field specifies new values for those modifiers. The <emphasis>
affectIgnoreLockVirtualMods</emphasis>
 and <emphasis>
ignoreLockVirtualMods</emphasis>
 fields update the virtual modifier component of the ignore locks modifier
definition in the same way. If any bits are set in either values field but not
in the corresponding mask field, a <emphasis>
Match</emphasis>
 error results.
</para>


<para>
If present, the <emphasis>
perKeyRepeat</emphasis>
 array specifies the repeat behavior of the individual keyboard keys. The
corresponding core protocol or input extension per-key autorepeat information
is updated to reflect any changes specified in <emphasis>
perKeyRepeat</emphasis>
. If the bits that correspond to any out-of-range keys are set in <emphasis>
perKeyRepeat</emphasis>
, a <emphasis>
Value</emphasis>
 error results.
</para>


<para>
If present, the <emphasis>
affectEnabledControls</emphasis>
 and <emphasis>
enabledControls</emphasis>
 field enable and disable global boolean controls. Any controls set in both
fields are enabled; any controls that are set in <emphasis>
affectEnabledControls</emphasis>
 but not in <emphasis>
enabledControls</emphasis>
 are disabled. Controls that are not set in either field are not affected. If
any controls are specified in <emphasis>
enabledControls</emphasis>
 but not in <emphasis>
affectEnabledControls</emphasis>
, a <emphasis>
Match</emphasis>
 error results. If either field contains anything except boolean controls, a
<emphasis>
Value</emphasis>
 error results.
</para>


</sect2>
<sect2 id='Querying_and_Changing_the_Keyboard_Mapping'>
<title>Querying and Changing the Keyboard Mapping</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbGetMap</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
full, partial:	KB_MAPPARTMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
firstType, nTypes: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
firstKeySym, firstKeyAction: KEYCODE</entry>
    </row>
    <row>
      <entry role='protoname'>
nKeySyms, nKeyActions: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
firstKeyBehavior,firstKeyExplicit: KEYCODE</entry>
    </row>
    <row>
      <entry role='protoname'>
nKeyBehaviors,nKeyExplicit: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
firstModMapKey,firstVModMapKey: KEYCODE</entry>
    </row>
    <row>
      <entry role='protoname'>
nModMapKeys, nVModMapKeys: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
virtualMods: KB_VMODMASK
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
deviceID: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
minKeyCode, maxKeyCode: KEYCODE
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
present: KB_MAPPARTMASK
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
firstType, nTypes, nTotalTypes: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
firstKeySym, firstKeyAction: KEYCODE
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
nKeySyms, nKeyActions: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
totalSyms, totalActions: CARD16
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
firstKeyBehavior, firstKeyExplicit: KEYCODE
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
nKeyBehaviors, nKeyExplicit: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
totalKeyBehaviors, totalKeyExplicit: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
firstModMapKey, firstVModMapKey: KEYCODE
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
nModMapKeys, nVModMapKeys: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
totalModMapKeys, totalVModMapKeys: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
virtualMods: KB_VMODMASK
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
typesRtrn: LISTofKB_KEYTYPE
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
symsRtrn: LISTofKB_KEYSYMMAP
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
actsRtrn: { count: LISTofCARD8, acts: LISTofKB_ACTION }
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
behaviorsRtrn: LISTofKB_SETBEHAVIOR
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
vmodsRtrn: LISTofSETofKEYMASK
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
explicitRtrn: LISTofKB_SETEXPLICIT
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
modmapRtrn: LISTofKB_KEYMODMAP
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
vmodMapRtrn: LISTofKB_KEYVMODMAP
      </entry>
    </row>
    <row>
      <entry role='protoerror'>
Errors: <emphasis>
Keyboard</emphasis>
, <emphasis>
Value</emphasis>
, <emphasis>
Match</emphasis>
, <emphasis>
Alloc</emphasis>
</entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
This request returns the indicated components of the server and client maps of
the keyboard specified by <emphasis>
deviceSpec</emphasis>
. The <emphasis>
full</emphasis>
 mask specifies the map components to be returned in full; the <emphasis>
partial</emphasis>
 mask specifies the components for which some subset of the legal elements are
to be returned. The server returns a <emphasis>
Match</emphasis>
 error if any component is specified in both <emphasis>
full</emphasis>
 and <emphasis>
partial</emphasis>
, or a <emphasis>
Value</emphasis>
 error if any undefined bits are set in either <emphasis>
full</emphasis>
 or <emphasis>
partial</emphasis>
.
</para>


<para>
Each bit in the <emphasis>
partial</emphasis>
 mask controls the interpretation of one or more of the other request fields,
as follows:
</para>

<informaltable frame='topbot'>
<?dbfo keep-together="always" ?>
<tgroup cols='3' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.5*'/>
<colspec colname='c2' colwidth='1.0*'/>
<colspec colname='c3' colwidth='1.5*'/>
<thead>
  <row rowsep='1'>
    <entry>Bit in the Partial Mask</entry>
    <entry>Type</entry>
    <entry>Corresponding Field(s)</entry>
  </row>
</thead>
<tbody>
  <row>
    <entry><emphasis>
XkbKeyTypesMask</emphasis>
</entry>
    <entry>key types</entry>
    <entry><emphasis>
firstType</emphasis>
, <emphasis>
nTypes</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbKeySymsMask</emphasis>
</entry>
    <entry>keycodes</entry>
    <entry><emphasis>
firstKeySym</emphasis>
, <emphasis>
nKeySyms</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbKeyActionsMask</emphasis>
</entry>
    <entry>keycodes</entry>
    <entry><emphasis>
firstKeyAction</emphasis>
, <emphasis>
nKeyActions</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbKeyBehaviorsMask</emphasis>
</entry>
    <entry>keycodes</entry>
    <entry><emphasis>
firstKeyBehavior</emphasis>
, <emphasis>
nKeyBehaviors</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbExplicitComponentsMask</emphasis>
</entry>
    <entry>keycodes</entry>
    <entry><emphasis>
firstKeyExplicit</emphasis>
, <emphasis>
nKeyExplicit</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbModifierMapMask</emphasis>
</entry>
    <entry>keycodes</entry>
    <entry><emphasis>
firstModMapKey</emphasis>
, <emphasis>
nModMapKeys</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbVirtualModMapMask</emphasis>
</entry>
    <entry>keycodes</entry>
    <entry><emphasis>
firstVModMapKey</emphasis>
, <emphasis>
nVModMapKeys</emphasis>
</entry>
  </row>
  <row>
    <entry><emphasis>
XkbVirtualModsMask</emphasis>
</entry>
    <entry>virtual modifiers</entry>
    <entry><emphasis>
virtualMods</emphasis>
</entry>
  </row>
</tbody>
</tgroup>
</informaltable>

<para>
If any of these keyboard map components are specified in <emphasis>
partial</emphasis>
, the corresponding values must specify a valid subset of the requested
components or this request reports a <emphasis>
Value</emphasis>
 error. If a keyboard map component is not specified in <emphasis>
partial</emphasis>
, the corresponding fields must contain zeroes, or a <emphasis>
Match</emphasis>
 error results.
</para>


<para>
If any error is generated, the request aborts and does not report any values.
</para>


<para>
On successful return, the <emphasis>
deviceID</emphasis>
 field reports the X input extension device ID of the keyboard for which
information is being returned, or <emphasis>
0</emphasis>
 if the server does not support the X input extension. The <emphasis>
minKeyCode</emphasis>
 and <emphasis>
maxKeyCode</emphasis>
 return values report the minimum and maximum keycodes that are legal for the
keyboard in question.
</para>


<para>
The <emphasis>
present</emphasis>
 return value lists all of the keyboard map components contained in the reply.
The bits in <emphasis>
present</emphasis>
 affect the interpretation of the other return values as follows:
</para>


<para>
If <emphasis>
XkbKeyTypesMask</emphasis>
 is set in <emphasis>
present</emphasis>
:
</para>

<itemizedlist>
<listitem>
  <para><emphasis>
firstType</emphasis>
 and <emphasis>
nTypes</emphasis>
 specify the types reported in the reply.
  </para>
</listitem>
<listitem>
  <para><emphasis>
nTotalTypes</emphasis>
 reports the total number of types defined for the keyboard
  </para>
</listitem>
<listitem>
  <para><emphasis>
typesRtrn</emphasis>
 has <emphasis>
nTypes</emphasis>
 elements of type KB_KEYTYPE which describe consecutive key types starting from
<emphasis>
firstType</emphasis>
.
  </para>
</listitem>
</itemizedlist>

<para>
If <emphasis>
XkbKeySymsMask</emphasis>
 is set in <emphasis>
present</emphasis>
:
</para>

<itemizedlist>
<listitem>
  <para><emphasis>
firstKeySym</emphasis>
 and <emphasis>
nKeySyms</emphasis>
 specify the subset of the keyboard keys for which symbols will be reported.
  </para>
</listitem>
<listitem>
  <para><emphasis>
totalSyms</emphasis>
 reports the total number of keysyms bound to the keys returned in this reply.
  </para>
</listitem>
<listitem>
  <para><emphasis>
symsRtrn</emphasis>
 has <emphasis>
nKeySyms</emphasis>
 elements of type KB_KEYSYMMAP, which describe the symbols bound to consecutive
keys starting from <emphasis>
firstKeySym</emphasis>
.
  </para>
</listitem>
</itemizedlist>

<para>
If <emphasis>
XkbKeyActionsMask</emphasis>
 is set in <emphasis>
present</emphasis>
:
</para>

<itemizedlist>
<listitem>
  <para><emphasis>
firstKeyAction</emphasis>
 and <emphasis>
nKeyActions</emphasis>
 specify the subset of the keys for which actions are reported.
  </para>
</listitem>
<listitem>
  <para><emphasis>
totalActions</emphasis>
 reports the total number of actions bound to the returned keys.
  </para>
</listitem>
<listitem>
  <para>The <emphasis>
count </emphasis>
field of the <emphasis>
actsRtrn</emphasis>
 return value has <emphasis>
nKeyActions</emphasis>
 entries of type CARD8, which specify the number of actions bound to
consecutive keys starting from <emphasis>
firstKeyAction</emphasis>
. The <emphasis>
acts</emphasis>
 field of <emphasis>
actsRtrn</emphasis>
 has <emphasis>
totalActions</emphasis>
 elements of type KB_ACTION and specifies the actions bound to the keys.
  </para>
</listitem>
</itemizedlist>

<para>
If <emphasis>
XkbKeyBehaviorsMask</emphasis>
 is set in <emphasis>
present</emphasis>
:
</para>

<itemizedlist>
<listitem>
  <para>The <emphasis>
firstKeyBehavior</emphasis>
 and <emphasis>
nKeyBehaviors</emphasis>
 return values report the range of keyboard keys for which behaviors will be
reported.
  </para>
</listitem>
<listitem>
  <para>The <emphasis>
totalKeyBehaviors</emphasis>
 return value reports the number of keys in the range to be reported that have
non-default values.
  </para>
</listitem>
<listitem>
  <para>The <emphasis>
behaviorsRtrn</emphasis>
 value has <emphasis>
totalKeyBehaviors</emphasis>
 entries of type KB_BEHAVIOR. Each entry specifies a key in the range for which
behaviors are being reported and the behavior associated with that key. Any
keys in that range that do not have an entry in <emphasis>
behaviorsRtrn</emphasis>
 have the default behavior, <emphasis>
KB_Default</emphasis>
.
  </para>
</listitem>
</itemizedlist>

<para>
If <emphasis>
XkbExplicitComponentsMask</emphasis>
 is set in <emphasis>
present</emphasis>
:
</para>

<itemizedlist>
<listitem>
  <para>The <emphasis>
firstKeyExplicit</emphasis>
 and <emphasis>
nKeyExplicit</emphasis>
 return values report the range of keyboard keys for which the set of explicit
components is to be returned.
  </para>
</listitem>
<listitem>
  <para>The <emphasis>
totalKeyExplicit</emphasis>
 return value reports the number of keys in the range specified by <emphasis>
firstKeyExplicit</emphasis>
 and <emphasis>
nKeyExplicit</emphasis>
 that have one or more explicit components.
  </para>
</listitem>
<listitem>
  <para>The <emphasis>
explicitRtrn</emphasis>
 return value has <emphasis>
totalKeyExplicit</emphasis>
 entries of type KB_KEYEXPLICIT. Each entry specifies the a key in the range
for which explicit components are being reported and the explicit components
that are bound to it. Any keys in that range that do not have an entry in
<emphasis>
explicitRtrn</emphasis>
 have no explicit components.
  </para>
</listitem>
</itemizedlist>

<para>
If <emphasis>
XkbModifierMapMask</emphasis>
 is set in <emphasis>
present</emphasis>
:
</para>

<itemizedlist>
<listitem>
  <para>The <emphasis>
firstModMapKey</emphasis>
 and <emphasis>
nModMapKeys</emphasis>
 return values report the range of keyboard keys for which the modifier map is
to be reported.
  </para>
</listitem>
<listitem>
  <para>The <emphasis>
totalModMapKeys</emphasis>
 return value reports the number of keys in the range specified by <emphasis>
firstModMapKey</emphasis>
 and <emphasis>
nModMapKeys</emphasis>
 that are bound with to one or more modifiers.
  </para>
</listitem>
<listitem>
  <para>The <emphasis>
modmapRtrn</emphasis>
 return value has <emphasis>
totalModMapKeys</emphasis>
 entries of type KB_KEYMODMAP. Each entry specifies the a key in the range for
which the modifier map is being reported and the set of modifiers that are
bound to that key. Any keys in that range that do not have an entry in
<emphasis>
modmapRtrn</emphasis>
 are not associated with any modifiers by the modifier mapping.
  </para>
</listitem>
</itemizedlist>

<para>
If <emphasis>
XkbVirtualModMapMask</emphasis>
 is set in <emphasis>
present</emphasis>
:
</para>

<itemizedlist>
<listitem>
  <para>The <emphasis>
firstVModMapKey</emphasis>
 and <emphasis>
nVModMapKeys</emphasis>
 return values report the range of keyboard keys for which the virtual modifier
map is to be reported.
  </para>
</listitem>
<listitem>
  <para>The <emphasis>
totalVModMapKeys</emphasis>
 return value reports the number of keys in the range specified by <emphasis>
firstVModMapKey</emphasis>
 and <emphasis>
nVModMapKeys</emphasis>
 that are bound with to or more virtual modifiers.
  </para>
</listitem>
<listitem>
  <para>The <emphasis>
vmodmapRtrn</emphasis>
 return value has <emphasis>
totalVModMapKeys</emphasis>
 entries of type KB_KEYVMODMAP. Each entry specifies the a key in the range for
which the virtual modifier map is being reported and the set of virtual
modifiers that are bound to that key. Any keys in that range that do not have
an entry in <emphasis>
vmodmapRtrn</emphasis>
 are not associated with any virtual modifiers,
  </para>
</listitem>
</itemizedlist>

<para>
If <emphasis>
XkbVirtualModsMask</emphasis>
 is set in <emphasis>
present</emphasis>
:
</para>

<itemizedlist>
<listitem>
  <para>The <emphasis>
virtualMods</emphasis>
 return value is a mask with one bit per virtual modifier which specifies the
virtual modifiers for which a set of corresponding real modifiers is to be
returned.
  </para>
</listitem>
<listitem>
  <para>The <emphasis>
vmodsRtrn</emphasis>
 return value is a list with one entry of type KEYBUTMASK for each virtual
modifier that is specified in <emphasis>
virtualMods</emphasis>
. The entries in <emphasis>
vmodsRtrn</emphasis>
 contain the real modifier bindings for the specified virtual modifiers,
beginning with the lowest-numbered virtual modifier that is present in
<emphasis>
virtualMods</emphasis>
 and proceeding to the highest.
  </para>
</listitem>
</itemizedlist>

<para>
If any of these bits are not set in <emphasis>
present</emphasis>
, the corresponding numeric fields all have the value zero, and the
corresponding lists are all of length zero.
</para>



<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbSetMap</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
flags: { <emphasis>
SetMapResizeTypes, SetMapRecomputeActions </emphasis>
}</entry>
    </row>
    <row>
      <entry role='protoname'>
present: KB_MAPPARTMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
minKeyCode, maxKeyCode: KEYCODE</entry>
    </row>
    <row>
      <entry role='protoname'>
firstType, nTypes: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
firstKeySym, firstKeyAction: KEYCODE</entry>
    </row>
    <row>
      <entry role='protoname'>
nKeySyms, nKeyActions: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
totalSyms, totalActions: CARD16</entry>
    </row>
    <row>
      <entry role='protoname'>
firstKeyBehavior, firstKeyExplicit: KEYCODE</entry>
    </row>
    <row>
      <entry role='protoname'>
nKeyBehaviors, nKeyExplicit: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
totalKeyBehaviors, totalKeyExplicit: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
firstModMapKey, firstVModMapKey: KEYCODE</entry>
    </row>
    <row>
      <entry role='protoname'>
nModMapKeys, nVModMapKeys: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
totalModMapKeys, totalVModMapKeys: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
virtualMods: VMODMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
types: LISTofKB_KEYTYPE</entry>
    </row>
    <row>
      <entry role='protoname'>
syms: LISTofKB_KEYSYMMAP</entry>
    </row>
    <row>
      <entry role='protoname'>
actions: { count: LISTofCARD8, actions: LISTofKB_ACTION }</entry>
    </row>
    <row>
      <entry role='protoname'>
behaviors: LISTofKB_BEHAVIOR</entry>
    </row>
    <row>
      <entry role='protoname'>
vmods: LISTofKEYMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
explicit: LISTofKB_EXPLICIT</entry>
    </row>
    <row>
      <entry role='protoname'>
modmap: LISTofKB_KEYMODMAP</entry>
    </row>
    <row>
      <entry role='protoname'>
vmodmap: LISTofKB_KEYVMODMAP</entry>
    </row>

    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
, <emphasis>
Value</emphasis>
, <emphasis>
Match</emphasis>
, <emphasis>
Alloc</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
This request changes the indicated parts of the keyboard specified by <emphasis>
deviceSpec</emphasis>
. With XKB, the effect of a key release is independent of the keyboard mapping
at the time of the release, so this request can be processed regardless of the
logical state of the modifier keys at the time of the request.
</para>


<para>
The <emphasis>
present</emphasis>
 field specifies the keyboard map components contained to be changed. The bits
in <emphasis>
present</emphasis>
 affect the interpretation of the other fields as follows:
</para>


<para>
If <emphasis>
XkbKeyTypesMask</emphasis>
 is set in <emphasis>
present</emphasis>
, <emphasis>
firstType</emphasis>
 and <emphasis>
nTypes</emphasis>
 specify a subset of the key types bound to the keyboard to be changed or
created. The index of the first key type to be changed must be less than or
equal to the unmodified length of the list of key types or a <emphasis>
Value</emphasis>
 error results.
</para>


<para>
If <emphasis>
XkbKeyTypesMask</emphasis>
 is set in <emphasis>
present</emphasis>
 and <emphasis>
SetMapResizeTypes</emphasis>
 is set in <emphasis>
flags</emphasis>
, the server resizes the list of key types bound to the keyboard so that the
last key type specified by this request is the last element in the list. If the
list of key types is shrunk, any existing key definitions that use key types
that eliminated are automatically assigned key types from the list of canonical
key types as described in <link linkend='Assigning_Types_To_Groups_of_Symbols_for_a_Key'>
Assigning Types To Groups of Symbols for a Key</link>. The list of key types
bound to a keyboard must always include the four canonical types and cannot
have more than <emphasis>
XkbMaxTypesPerKey</emphasis>
 (32) types; any attempt to reduce the number of types bound to a keyboard
below four or above <emphasis>
XkbMaxTypesPerKey</emphasis>
 causes a <emphasis>
Value</emphasis>
 error. Symbolic names for newly created key types or levels within a key type
are initialized to <emphasis>
None</emphasis>
.
</para>


<para>
If <emphasis>
XkbKeyTypesMask</emphasis>
 is set in <emphasis>
present</emphasis>
, the types list has <emphasis>
nTypes</emphasis>
 entries of type KB_KEYTYPE.Each key type specified in <emphasis>
types</emphasis>
 must be valid or a <emphasis>
Value</emphasis>
 error results. To be valid a key type definition must meet the following
criteria:
</para>

<itemizedlist>
<listitem>
  <para>The <emphasis>
numLevels</emphasis>
 for the type must be greater than zero.
  </para>
</listitem>
<listitem>
  <para>If the key type is <emphasis>
ONE_LEVEL</emphasis>
 (i.e. index zero in the list of key types), <emphasis>
numLevels</emphasis>
 must be one.
  </para>
</listitem>
<listitem>
  <para>If the key type is <emphasis>
TWO_LEVEL</emphasis>
 or <emphasis>
KEYPAD</emphasis>
, or <emphasis>
ALPHABETIC</emphasis>
 (i.e. index one, two, or three in the lest of key types) group width must be
two.
  </para>
</listitem>
</itemizedlist>

<para>
Each key type in types must also be internally consistent, or a Match error
results. To be internally consistent, a key type definition must meet the
following criteria:
</para>

<itemizedlist>
<listitem>
  <para>Each map entry must specify a resulting level that is legal for the
type.
  </para>
</listitem>
<listitem>
  <para>Any real or virtual modifiers specified in any of the map entries must
also be specified in the <emphasis>
mods</emphasis>
 for the type.
  </para>
</listitem>
</itemizedlist>

<para>
If <emphasis>
XkbKeySymsMask</emphasis>
 is set in <emphasis>
present</emphasis>
, <emphasis>
firstKeySym</emphasis>
 and <emphasis>
nKeySyms</emphasis>
 specify a subset of the keyboard keys to which new symbols are to be assigned
and <emphasis>
totalSyms</emphasis>
 specifies the total number of symbols to be assigned to those keys. If any of
the keys specified by <emphasis>
firstKeySym</emphasis>
 and <emphasis>
nKeySyms</emphasis>
 are not legal, a <emphasis>
Match</emphasis>
 error results. The <emphasis>
syms</emphasis>
 list has <emphasis>
nKeySyms</emphasis>
 elements of type KB_KEYSYMMAP. Each key in the resulting key symbol map must
be valid and internally consistent or a <emphasis>
Value</emphasis>
 error results. To be valid and internally consistent, a key symbol map must
meet the following criteria:
</para>

<itemizedlist>
<listitem>
  <para>The key type indices must specify legal result key types.
  </para>
</listitem>
<listitem>
  <para>The number of groups specified by <emphasis>
groupInfo</emphasis>
 must be in the range <emphasis>
0…4</emphasis>
.
  </para>
</listitem>
<listitem>
  <para>The <emphasis>
width</emphasis>
 of the key symbol map must be equal to <emphasis>
numLevels</emphasis>
 of the widest key type bound to the key.
  </para>
</listitem>
<listitem>
  <para>The number of symbols, <emphasis>
nSyms</emphasis>
, must equal the number of groups times <emphasis>
width</emphasis>
.
  </para>
</listitem>
</itemizedlist>

<para>
If <emphasis>
XkbKeyActionsMask</emphasis>
 is set in <emphasis>
present</emphasis>
, <emphasis>
firstKeyAction</emphasis>
 and <emphasis>
nKeyActions</emphasis>
 specify a subset of the keyboard keys to which new actions are to be assigned
and <emphasis>
totalActions</emphasis>
 specifies the total number of actions to be assigned to those keys. If any of
the keys specified by <emphasis>
firstKeyAction</emphasis>
 and <emphasis>
nKeyActions</emphasis>
 are not legal, a <emphasis>
Match</emphasis>
 error results. The <emphasis>
count</emphasis>
 field of the <emphasis>
actions</emphasis>
 return value has <emphasis>
nKeyActions</emphasis>
 elements of type CARD8; each element of <emphasis>
count</emphasis>
 specifies the number of actions bound to the corresponding key. The <emphasis>
actions</emphasis>
 list in the <emphasis>
actions</emphasis>
 field has <emphasis>
totalActions</emphasis>
 elements of type KB_ACTION. These actions are assigned to each target key in
turn, as specified by <emphasis>
count</emphasis>
. The list of actions assigned to each key must either be empty or have exactly
as many actions as the key has symbols, or a <emphasis>
Match</emphasis>
 error results.
</para>


<para>
If <emphasis>
XkbKeyBehaviorsMask</emphasis>
 is set in <emphasis>
present</emphasis>
, <emphasis>
firstKeyBehavior</emphasis>
 and <emphasis>
nKeyBehaviors</emphasis>
 specify a subset of the keyboard keys to which new behaviors are to be
assigned, and <emphasis>
totalKeyBehaviors</emphasis>
 specifies the total number of keys in that range to be assigned non-default
behavior. If any of the keys specified by <emphasis>
firstKeyBehavior</emphasis>
 and <emphasis>
nKeyBehaviors</emphasis>
 are not legal, a <emphasis>
Match</emphasis>
 error results. The <emphasis>
behaviors</emphasis>
 list has <emphasis>
totalKeyBehaviors</emphasis>
 elements of type KB_BEHAVIOR; each entry of <emphasis>
behaviors</emphasis>
 specifies a key in the specified range and a new behavior for that key; any
key that falls in the range specified by <emphasis>
firstBehavior</emphasis>
 and <emphasis>
nBehaviors</emphasis>
 for which no behavior is specified in <emphasis>
behaviors</emphasis>
 is assigned the default behavior, <emphasis>
KB_Default</emphasis>
. The new behaviors must be legal, or a <emphasis>
Value</emphasis>
 error results. To be legal, the behavior specified in the <emphasis>
XkbSetMap</emphasis>
 request must:
</para>

<itemizedlist>
<listitem>
  <para>Specify a key in the range indicated by <emphasis>
firstKeyBehavior</emphasis>
 and <emphasis>
nKeyBehaviors</emphasis>
.
  </para>
</listitem>
<listitem>
  <para>Not specify the <emphasis>
permanent</emphasis>
 flag; permanent behaviors cannot be set or changed using the <emphasis>
XkbSetMap</emphasis>
 request.
  </para>
</listitem>
<listitem>
  <para>If present, the <emphasis>
KB_Overlay1</emphasis>
 and <emphasis>
KB_Overlay2</emphasis>
 behaviors must specify a keycode for the overlay key that is valid for the
current keyboard.
  </para>
</listitem>
<listitem>
  <para>If present, the <emphasis>
KB_RadioGroup</emphasis>
 behavior must specify a legal index (0…31) for the radio group to which the
key belongs.
  </para>
</listitem>
</itemizedlist>

<para>
Key behaviors that are not recognized by the server are accepted but ignored.
Attempts to replace a "permanent" behavior are silently ignored; the behavior
is not replaced, but not error is generated and any other components specified
in the <emphasis>
XkbSetMap</emphasis>
 request are updated, as appropriate.
</para>


<para>
If <emphasis>
XkbVirtualModsMask</emphasis>
 is set in <emphasis>
present</emphasis>
, <emphasis>
virtualMods</emphasis>
 is a mask which specifies the virtual modifiers to be rebound. The <emphasis>
vmods</emphasis>
 list specifies the real modifiers that are bound to each of the virtual
modifiers specified in <emphasis>
virtualMods</emphasis>
, starting from the lowest numbered virtual modifier and progressing upward.
Any virtual modifier that is not specified in <emphasis>
virtualMods</emphasis>
 has no corresponding entry in <emphasis>
vmods</emphasis>
, so the <emphasis>
vmods</emphasis>
 list has one entry for each bit that is set in <emphasis>
virtualMods</emphasis>
.
</para>


<para>
If <emphasis>
XkbExplicitComponentsMask</emphasis>
 is set in <emphasis>
present</emphasis>
, <emphasis>
firstKeyExplicit</emphasis>
 and <emphasis>
nKeyExplicit</emphasis>
 specify a subset of the keyboard keys to which new explicit components are to
be assigned, and <emphasis>
totalKeyExplicit</emphasis>
 specifies the total number of keys in that range that have at least one
explicit component. The <emphasis>
explicit</emphasis>
 list has <emphasis>
totalKeyExplicit</emphasis>
 elements of type KB_KEYEXPLICIT; each entry of <emphasis>
explicit</emphasis>
 specifies a key in the specified range and a new set of explicit components
for that key. Any key that falls in the range specified by <emphasis>
firstKeyExplicit</emphasis>
 and <emphasis>
nKeyExplicit</emphasis>
 that is not assigned some value in <emphasis>
explicit</emphasis>
 has no explicit components.
</para>


<para>
If <emphasis>
XkbModifierMapMask</emphasis>
 is set in <emphasis>
present</emphasis>
, <emphasis>
firstModMapKey</emphasis>
 and <emphasis>
nModMapKeys</emphasis>
 specify a subset of the keyboard keys for which new modifier mappings are to
be assigned, and <emphasis>
totalModMapKeys</emphasis>
 specifies the total number of keys in that range to which at least one
modifier is bound. The <emphasis>
modmap</emphasis>
 list has <emphasis>
totalModMapKeys</emphasis>
 elements of type KB_KEYMODMAP; each entry of <emphasis>
modmap</emphasis>
 specifies a key in the specified range and a new set of modifiers to be
associated with that key. Any key that falls in the range specified by
<emphasis>
firstModMapKey</emphasis>
 and <emphasis>
nModMapKeys</emphasis>
 that is not assigned some value in <emphasis>
modmap</emphasis>
 has no associated modifiers.
</para>


<para>
If the modifier map is changed by the <emphasis>
XkbSetMap</emphasis>
 request, any changes are also reflected in the core protocol modifier mapping.
Changes to the core protocol modifier mapping are reported to XKB-unaware
clients via <emphasis>
MappingNotify</emphasis>
 events and can be retrieved with the core protocol <emphasis>
GetModifierMapping</emphasis>
 request.
</para>


<para>
If <emphasis>
XkbVirtualModMapMask</emphasis>
 is set in <emphasis>
present</emphasis>
, <emphasis>
firstVModMapKey</emphasis>
 and <emphasis>
nVModMapKeys</emphasis>
 specify a subset of the keyboard keys for which new modifier mappings are to
be assigned, and <emphasis>
totalVModMapKeys</emphasis>
 specifies the total number of keys in that range to which at least one virtual
modifier is bound. The <emphasis>
vmodmap</emphasis>
 list has <emphasis>
totalVModMapKeys</emphasis>
 elements of type KB_KEYVMODMAP; each entry of <emphasis>
vmodmap</emphasis>
 specifies a key in the specified range and a new set of virtual modifiers to
be associated with that key. Any key that falls in the range specified by
<emphasis>
firstVModMapKey</emphasis>
 and <emphasis>
nVModMapKeys</emphasis>
 that is not assigned some value in <emphasis>
vmodmap</emphasis>
 has no associated virtual modifiers.
</para>


<para>
If the resulting keyboard map is legal, the server updates the keyboard map.
Changes to some keyboard components have indirect effects on others:
</para>


<para>
If the <emphasis>
XkbSetMapRecomputeActions</emphasis>
 bit is set in <emphasis>
flags</emphasis>
, the actions associated with any keys for which symbol or modifier bindings
were changed by this request are recomputed as described in
<link linkend='Assigning_Actions_To_Keys'>Assigning Actions To Keys</link>. Note
that actions are recomputed <emphasis>
after </emphasis>
any actions specified in this request are bound to keys, so the actions
specified in this request might be clobbered by the automatic assignment of
actions to keys.
</para>


<para>
If the group width of an existing key type is changed, the list of symbols
associated with any keys of the changed type might be resized accordingly. If
the list increases in size, any unspecified new symbols are initialized to
<emphasis>
NoSymbol</emphasis>
.
</para>


<para>
If the list of actions associated with a key is not empty, changing the key
type of the key resizes the list. Unspecified new actions are calculated by
applying any keyboard symbol interpretations to the corresponding symbols.
</para>


<para>
The number of groups global to the keyboard is always equal to the largest
number of groups specified by any of the key symbol maps. Changing the number
of groups in one or more key symbol maps may change the number of groups global
to the keyboard.
</para>


<para>
Assigning key behavior <emphasis>
KB_RadioGroup</emphasis>
 to a key adds that key as a member of the specified radio group. Changing a
key with the existing behavior <emphasis>
KB_RadioGroup</emphasis>
 removes that key from the group. Changing the elements of a radio group can
cause synthetic key press or key release events if the key to be added or
removed is logically down at the time of the change.
</para>


<para>
Changing a key with behavior <emphasis>
KB_Lock</emphasis>
 causes a synthetic key release event if the key is logically but not
physically down at the time of the change.
</para>


<para>
This request sends an <emphasis>
XkbMapNotify</emphasis>
 event which reflects both explicit and indirect map changes to any interested
clients. If any symbolic names are changed, it sends a <emphasis>
XkbNamesNotify</emphasis>
 reflecting the changes to any interested clients. XKB-unaware clients are
notified of keyboard changes via core protocol <emphasis>
MappingNotify</emphasis>
 events.
</para>


<para>
Key press and key release events caused by changing key behavior may cause
additional <emphasis>
XkbStateNotify</emphasis>
 or <emphasis>
XkbIndicatorStateNotify</emphasis>
 events.
</para>


</sect2>
<sect2 id='Querying_and_Changing_the_Compatibility_Map'>
<title>Querying and Changing the Compatibility Map</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbGetCompatMap</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
groups: KB_GROUPMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
getAllSI: BOOL</entry>
    </row>
    <row>
      <entry role='protoname'>
firstSI, nSI: CARD16
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
deviceID: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
groupsRtrn: KB_GROUPMASK
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
firstSIRtrn, nSIRtrn, nTotalSI: CARD16
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
siRtrn: LISTofKB_SYMINTERP
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
groupRtrn: LISTofKB_MODDEF</entry>
    </row>
    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
, <emphasis>
Match</emphasis>
, <emphasis>
Alloc</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
This request returns the listed compatibility map components for the keyboard
specified by <emphasis>
deviceSpec</emphasis>
. If <emphasis>
deviceSpec</emphasis>
 does not specify a valid keyboard device, a <emphasis>
Keyboard</emphasis>
 Error results. On return, <emphasis>
deviceID</emphasis>
 reports the input extension identifier of the keyboard device or <emphasis>
0</emphasis>
 if the server does not support the input extension.
</para>


<para>
If <emphasis>
getAllSI</emphasis>
 is <emphasis>
False</emphasis>
, <emphasis>
firstSI</emphasis>
 and <emphasis>
nSI</emphasis>
 specify a subset of the symbol interpretations to be returned; if used,
<emphasis>
nSI</emphasis>
 must be greater than <emphasis>
0</emphasis>
 and all of the elements specified by <emphasis>
firstSI</emphasis>
 and <emphasis>
nSI</emphasis>
 must be defined or a <emphasis>
Value</emphasis>
 error results. If <emphasis>
getAllSyms</emphasis>
 is <emphasis>
True</emphasis>
, the server ignores <emphasis>
firstSym</emphasis>
 and <emphasis>
nSyms</emphasis>
 and returns all of the symbol interpretations defined for the keyboard.
</para>


<para>
The <emphasis>
groups</emphasis>
 mask specifies the groups for which compatibility maps are to be returned.
</para>


<para>
The <emphasis>
nTotalSI</emphasis>
 return value reports the total number of symbol interpretations defined for
the keyboard. On successful return, the <emphasis>
siRtrn</emphasis>
 return list contains the definitions for <emphasis>
nSIRtrn</emphasis>
 symbol interpretations beginning at <emphasis>
firstSIRtrn</emphasis>
.
</para>


<para>
The <emphasis>
groupRtrn</emphasis>
 return values report the entries in the group compatibility map for any groups
specified in the <emphasis>
groupsRtrn</emphasis>
 return value.
</para>



<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbSetCompatMap</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
recomputeActions: BOOL</entry>
    </row>
    <row>
      <entry role='protoname'>
truncateSI: BOOL</entry>
    </row>
    <row>
      <entry role='protoname'>
groups: KB_GROUPMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
firstSI, nSI: CARD16</entry>
    </row>
    <row>
      <entry role='protoname'>
si: LISTofKB_SYMINTERPRET</entry>
    </row>
    <row>
      <entry role='protoname'>
groupMaps: LISTofKB_MODDEF</entry>
    </row>

    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
, <emphasis>
Match</emphasis>
, <emphasis>
Value</emphasis>
, <emphasis>
Alloc</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
This request changes a specified subset of the compatibility map of the
keyboard indicated by <emphasis>
deviceSpec</emphasis>
. If <emphasis>
deviceSpec</emphasis>
 specifies an invalid device, a <emphasis>
Keyboard</emphasis>
 error results and nothing is changed.
</para>


<para>
The <emphasis>
firstSI</emphasis>
 and <emphasis>
nSI</emphasis>
 fields specify a subset of the keyboard symbol interpretations to be changed.
The <emphasis>
si</emphasis>
 list specifies new values for each of the interpretations in that range.
</para>


<para>
The first symbol interpretation to be changed, <emphasis>
firstSI</emphasis>
, must be less than or equal to the unchanged length of the list of symbol
interpretations, or a <emphasis>
Value</emphasis>
 error results. If the resulting list would be larger than the unchanged list,
it server list of symbol interpretations is automatically increased in size.
Otherwise, if <emphasis>
truncateSyms</emphasis>
 is <emphasis>
True</emphasis>
, the server deletes any symbol interpretations after the last element changed
by this request, and reduces the length of the list accordingly.
</para>


<para>
The <emphasis>
groupMaps</emphasis>
 fields contain new definitions for a subset of the group compatibility map;
<emphasis>
groups</emphasis>
 specifies the group compatibility map entries to be updated from <emphasis>
groupMaps</emphasis>
.
</para>


<para>
 All changed compatibility maps and symbol interpretations must either ignore
group state or specify a legal range of groups, or a <emphasis>
Value</emphasis>
 error results.
</para>


<para>
If the <emphasis>
recomputeActions</emphasis>
 field is <emphasis>
True</emphasis>
, the server regenerates recalculates the actions bound to all keyboard keys by
applying the new symbol interpretations to the entire key symbol map, as
described in <link linkend='Assigning_Actions_To_Keys'>Assigning Actions To
Keys</link>.
</para>


</sect2>
<sect2 id='Querying_and_Changing_Indicators'>
<title>Querying and Changing Indicators</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbGetIndicatorState</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoreply'>
deviceID: CARD8
state: KB_INDICATORMASK</entry>
    </row>

    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
This request reports the current state of the indicators for the keyboard
specified by <emphasis>
deviceSpec</emphasis>
. If <emphasis>
deviceSpec</emphasis>
 does not specify a valid keyboard, a <emphasis>
Keyboard</emphasis>
 error results.
</para>


<para>
On successful return, the <emphasis>
deviceID</emphasis>
 field reports the input extension identifier of the keyboard or <emphasis>
0</emphasis>
 if the server does not support the input extension. The <emphasis>
state</emphasis>
 return value reports the state of each of the thirty-two indicators on the
specified keyboard. The least-significant bit corresponds to indicator 0, the
most significant bit to indicator 31; if a bit is set, the corresponding
indicator is lit.
</para>



<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbGetIndicatorMap</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
which: KB_INDICATORMASK
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
deviceID: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
which: KB_INDICATORMASK
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
realIndicators: KB_INDICATORMASK
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
nIndicators: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
maps: LISTofKB_INDICATORMAP</entry>
    </row>
    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
, <emphasis>
Value</emphasis>
</entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
This request returns a subset of the maps for the indicators on the keyboard
specified by <emphasis>
deviceSpec</emphasis>
. If <emphasis>
deviceSpec</emphasis>
 does not specify a valid keyboard device, a <emphasis>
Keyboard</emphasis>
 error results.
</para>


<para>
The <emphasis>
which</emphasis>
 field specifies the subset to be returned; a set bit in the which field
indicates that the map for the corresponding indicator should be returned.
</para>


<para>
On successful return, the <emphasis>
deviceID</emphasis>
 field reports the input extension identifier of the keyboard or <emphasis>
0</emphasis>
 if the server does not support the input extension. Any indicators specified
in <emphasis>
realIndicators</emphasis>
 are actually present on the keyboard; the rest are virtual indicators. Virtual
indicators do not directly cause any visible or audible effect when they change
state, but they do cause <emphasis>
XkbIndicatorStateNotify</emphasis>
 events.
</para>


<para>
The <emphasis>
maps</emphasis>
 return value reports the requested indicator maps. Indicator maps are
described in <link linkend='Indicator_Maps'>Indicator Maps</link>
</para>



<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbSetIndicatorMap</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
which: KB_INDICATORMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
maps: LISTofKB_INDICATORMAP</entry>
    </row>

    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
, <emphasis>
Value</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
This request changes a subset of the maps on the keyboard specified by
<emphasis>
deviceSpec</emphasis>
. If <emphasis>
deviceSpec</emphasis>
 does not specify a valid keyboard device, a <emphasis>
Keyboard</emphasis>
 error results.
</para>


<para>
The <emphasis>
which</emphasis>
 field specifies the subset to be changed; the <emphasis>
maps</emphasis>
 field contains the new definitions.
</para>


<para>
If successful, the new indicator maps are applied immediately. If any
indicators change state as a result of the new maps, the server generates
<emphasis>
XkbIndicatorStateNotify</emphasis>
 events as appropriate.
</para>



<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbGetNamedIndicator</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
ledClass: KB_LEDCLASSSPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
ledID: KB_IDSPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
indicator: ATOM
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
deviceID: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
supported: BOOL
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
indicator: ATOM
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
found: BOOL
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
on: BOOL
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
realIndicator: BOOL
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
ndx: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
map: KB_INDICATORMAP</entry>
    </row>

    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
, <emphasis>
Atom</emphasis>
, <emphasis>
Value</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
This request returns information about the indicator specified by <emphasis>
ledClass</emphasis>
, <emphasis>
ledID</emphasis>
, and <emphasis>
indicator</emphasis>
 on the keyboard specified by <emphasis>
deviceSpec</emphasis>
. The <emphasis>
indicator</emphasis>
 field specifies the name of the indicator for which information is to be
returned.
</para>


<para>
If <emphasis>
deviceSpec</emphasis>
 does not specify a device with indicators, a <emphasis>
Keyboard</emphasis>
 error results. If <emphasis>
ledClass</emphasis>
 does not have the value <emphasis>
DfltXIClass</emphasis>
, <emphasis>
LedFeedbackClass</emphasis>
, or <emphasis>
KbdFeedbackClass</emphasis>
, a <emphasis>
Value</emphasis>
 error results. If <emphasis>
ledID</emphasis>
 does not have the value <emphasis>
DfltXIId</emphasis>
 or specify the identifier of a feedback of the class specified by <emphasis>
ledClass</emphasis>
 on the device specified by <emphasis>
deviceSpec</emphasis>
, a <emphasis>
Match</emphasis>
 error results. If <emphasis>
indicator</emphasis>
 is not a valid ATOM other than <emphasis>
None</emphasis>
, an <emphasis>
Atom</emphasis>
 error results.
</para>


<para>
This request is always supported with default class and identifier on the core
keyboard device. If the request specifies a device other than the core keyboard
device or a feedback class and identifier other than the defaults, and the
server does not support indicator names or indicator maps for extension
devices, the <emphasis>
supported</emphasis>
 return value is <emphasis>
False</emphasis>
 and the values of the other fields in the reply are undefined. If the client
which issued the unsupported request has also selected to do so, it will also
receive an <emphasis>
XkbExtensionDeviceNotify</emphasis>
 event which reports the attempt to use an unsupported feature, in this case
one or both of <emphasis>
XkbXI_IndicatorMaps</emphasis>
 or <emphasis>
XkbXI_IndicatorNames</emphasis>
.
</para>


<para>
Otherwise, <emphasis>
supported</emphasis>
 is <emphasis>
True</emphasis>
 and the <emphasis>
deviceID</emphasis>
 field reports the input extension identifier of the keyboard or <emphasis>
0</emphasis>
 if the server does not support the input extension. The <emphasis>
indicator</emphasis>
 return value reports the name for which information was requested and the
<emphasis>
found</emphasis>
 return value is <emphasis>
True</emphasis>
 if an indicator with the specified name was found on the device.
</para>


<para>
If a matching indicator was found:
</para>

<itemizedlist>
<listitem>
  <para>The <emphasis>
on</emphasis>
 return value reports the state of the indicator at the time of the request.
  </para>
</listitem>
<listitem>
  <para>The <emphasis>
realIndicator</emphasis>
 return value is <emphasis>
True</emphasis>
 if the requested indicator is actually present on the keyboard or <emphasis>
False</emphasis>
 if it is virtual.
  </para>
</listitem>
<listitem>
  <para>The <emphasis>
ndx</emphasis>
 return value reports the index of the indicator in the requested feedback.
  </para>
</listitem>
<listitem>
  <para>The <emphasis>
map</emphasis>
 return value reports the indicator map used by to automatically change the
state of the specified indicator in response to changes in keyboard state or
controls.
  </para>
</listitem>
</itemizedlist>

<para>
If no matching indicator is found, the <emphasis>
found</emphasis>
 return value is <emphasis>
False</emphasis>
, and the <emphasis>
on</emphasis>
, <emphasis>
realIndicator</emphasis>
, <emphasis>
ndx</emphasis>
, and <emphasis>
map</emphasis>
 return values are undefined.
</para>



<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbSetNamedIndicator</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
ledClass: KB_LEDCLASSSPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
ledID: KB_IDSPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
indicator: ATOM</entry>
    </row>
    <row>
      <entry role='protoname'>
setState: BOOL</entry>
    </row>
    <row>
      <entry role='protoname'>
on: BOOL</entry>
    </row>
    <row>
      <entry role='protoname'>
setMap: BOOL</entry>
    </row>
    <row>
      <entry role='protoname'>
createMap: BOOL</entry>
    </row>
    <row>
      <entry role='protoname'>
map: KB_SETINDICATORMAP</entry>
    </row>

    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
, <emphasis>
Atom</emphasis>
, <emphasis>
Access</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
This request changes various aspects of the indicator specified by <emphasis>
ledClass</emphasis>
, <emphasis>
ledID</emphasis>
, and <emphasis>
indicator</emphasis>
 on the keyboard specified by <emphasis>
deviceSpec</emphasis>
. The <emphasis>
indicator</emphasis>
 argument specifies the name of the indicator to be updated.
</para>


<para>
If <emphasis>
deviceSpec</emphasis>
 does not specify a device with indicators, a <emphasis>
Keyboard</emphasis>
 error results. If <emphasis>
ledClass</emphasis>
 does not have the value <emphasis>
DfltXIClass</emphasis>
, <emphasis>
LedFeedbackClass</emphasis>
, or <emphasis>
KbdFeedbackClass</emphasis>
, a <emphasis>
Value</emphasis>
 error results. If <emphasis>
ledID</emphasis>
 does not have the value <emphasis>
DfltXIId</emphasis>
 or specify the identifier of a feedback of the class specified by <emphasis>
ledClass</emphasis>
 on the device specified by <emphasis>
deviceSpec</emphasis>
, a <emphasis>
Match</emphasis>
 error results. If <emphasis>
indicator</emphasis>
 is not a valid ATOM other than <emphasis>
None</emphasis>
, an <emphasis>
Atom</emphasis>
 error results.
</para>


<para>
This request is always supported with default class and identifier on the core
keyboard device. If the request specifies a device other than the core keyboard
device or a feedback class and identifier other than the defaults, and the
server does not support indicator names or indicator maps for extension
devices, the <emphasis>
supported</emphasis>
 return value is <emphasis>
False</emphasis>
 and the values of the other fields in the reply are undefined. If the client
which issued the unsupported request has also selected to do so, it will also
receive an <emphasis>
XkbExtensionDeviceNotify</emphasis>
 event which reports the attempt to use an unsupported feature, in this case
one or both of <emphasis>
XkbXI_IndicatorMaps</emphasis>
 and <emphasis>
XkbXI_IndicatorNames</emphasis>
.
</para>


<para>
Otherwise, <emphasis>
supported</emphasis>
 is <emphasis>
True</emphasis>
 and the <emphasis>
deviceID</emphasis>
 field reports the input extension identifier of the keyboard or <emphasis>
0</emphasis>
 if the server does not support the input extension. The <emphasis>
indicator</emphasis>
 return value reports the name for which information was requested and the
<emphasis>
found</emphasis>
 return value is <emphasis>
True</emphasis>
 if an indicator with the specified name was found on the device.
</para>


<para>
If no indicator with the specified name is found on the specified device, and
the <emphasis>
createMap</emphasis>
 field is <emphasis>
True</emphasis>
, XKB assigns the specified name to the lowest-numbered indicator that has no
name (i.e. whose name is <emphasis>
None</emphasis>
) and applies the rest of the fields in the request to the newly named
indicator. If no unnamed indicators remain, this request reports no error and
has no effect.
</para>


<para>
If no matching indicator is found or new indicator assigned this request
reports no error and has no effect. Otherwise, it updates the indicator as
follows:
</para>


<para>
If <emphasis>
setMap </emphasis>
is <emphasis>
True</emphasis>
, XKB changes the map for the indicator (see <link linkend='Indicator_Maps'>Indicator Maps</link>) to reflect the
values specified in <emphasis>
map</emphasis>
.
</para>


<para>
If <emphasis>
setState</emphasis>
 is <emphasis>
True</emphasis>
, XKB attempts to explicitly change the state of the indicator to the state
specified in <emphasis>
on</emphasis>
. The effects of an attempt to explicitly change the state of an indicator
depend on the values in the map for that indicator and are not guaranteed to
succeed.
</para>


<para>
If this request affects both indicator map and state, it updates the indicator
map before attempting to change its state, so the success of the explicit
change depends on the indicator map values specified in the request.
</para>


<para>
If this request changes the indicator map, it applies the new map immediately
to determine the appropriate state for the indicator given the new indicator
map and the current state of the keyboard.
</para>


</sect2>
<sect2 id='Querying_and_Changing_Symbolic_Names'>
<title>Querying and Changing Symbolic Names</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbGetNames</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
which: KB_NAMEDETAILMASK</entry>
    </row>
    <row>
      <entry role='protoreply'>
deviceID: CARD8
which: KB_NAMESMASK
minKeyCode, maxKeyCode: KEYCODE
nTypes: CARD8
nKTLevels: CARD16
groupNames: KB_GROUPMASK
virtualMods: KB_VMODMASK
firstKey: KEYCODE
nKeys: CARD8
indicators: KB_INDICATORMASK
nRadioGroups, nKeyAliases: CARD8
present: KB_NAMEDETAILMASK
valueList: LISTofITEMs</entry>
    </row>

    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
, <emphasis>
Value</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
This request returns the symbolic names for various components of the keyboard
mapping for the device specified by <emphasis>
deviceSpec</emphasis>
. The <emphasis>
which</emphasis>
 field specifies the keyboard components for which names are to be returned. If
<emphasis>
deviceSpec</emphasis>
 does not specify a valid keyboard device, a <emphasis>
Keyboard</emphasis>
 error results. If any undefined bits in <emphasis>
which</emphasis>
 are non-zero, a <emphasis>
Value</emphasis>
 error results.
</para>


<para>
The <emphasis>
deviceID</emphasis>
 return value contains the X Input Extension device identifier of the specified
device or <emphasis>
0</emphasis>
 if the server does not support the input extension. The <emphasis>
present</emphasis>
 and <emphasis>
valueList</emphasis>
 return values specify the components for which names are being reported. If a
component is specified in <emphasis>
present</emphasis>
, the corresponding element is present in the <emphasis>
valueList</emphasis>
, otherwise that component has length <emphasis>
0</emphasis>
. The components of the <emphasis>
valueList</emphasis>
 appear in the following order, when present:.
</para>

<informaltable frame='none'>
<?dbfo keep-together="always" ?>
<tgroup cols='3' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
<colspec colname='c2' colwidth='1.0*'/>
<colspec colname='c3' colwidth='1.0*'/>
<thead>
  <row rowsep='1'>
    <entry>Component</entry>
    <entry>Size</entry>
    <entry>Type</entry>
  </row>
</thead>
<tbody>
  <row>
    <entry><emphasis>
XkbKeycodesName</emphasis>
</entry>
    <entry>1</entry>
    <entry>	ATOM</entry>
  </row>
  <row>
    <entry><emphasis>
XkbGeometryName</emphasis>
</entry>
    <entry>1</entry>
    <entry>	ATOM</entry>
  </row>
  <row>
    <entry><emphasis>
XkbSymbolsName</emphasis>
</entry>
    <entry>1</entry>
    <entry>	ATOM</entry>
  </row>
  <row>
    <entry><emphasis>
XkbPhysSymbolsName</emphasis>
</entry>
    <entry>1</entry>
    <entry>	ATOM</entry>
  </row>
  <row>
    <entry><emphasis>
XkbTypesName</emphasis>
</entry>
    <entry>1</entry>
    <entry>	ATOM</entry>
  </row>
  <row>
    <entry><emphasis>
XkbCompatName</emphasis>
</entry>
    <entry>1</entry>
    <entry>	ATOM</entry>
  </row>
  <row>
    <entry><emphasis>
XkbKeyTypeNames</emphasis>
 </entry>
    <entry><emphasis>
nTypes</emphasis>
</entry>
    <entry>	LISTofATOM</entry>
  </row>
  <row>
    <entry><emphasis>
XkbKTLevelNames</emphasis>
 </entry>
    <entry><emphasis>
nTypes</emphasis>
,
<emphasis>
nKTLevels</emphasis>
</entry>
    <entry>{	count: LISTofCARD8,
	names: LISTofATOM }</entry>
  </row>
  <row>
    <entry><emphasis>
XkbIndicatorNames</emphasis>
 </entry>
    <entry>One per bit set in <emphasis>
indicators</emphasis>
</entry>
    <entry>	LISTofATOM</entry>
  </row>
  <row>
    <entry><emphasis>
XkbVirtualModNames</emphasis>
 </entry>
    <entry>One per bit set in <emphasis>
virtualMods</emphasis>
</entry>
    <entry>	LISTofATOM</entry>
  </row>
  <row>
    <entry><emphasis>
XkbGroupNames </emphasis>
</entry>
    <entry>One per bit set in <emphasis>
groupNames</emphasis>
</entry>
    <entry>	LISTofATOM</entry>
  </row>
  <row>
    <entry><emphasis>
XkbKeyNames</emphasis>
</entry>
    <entry><emphasis>
nKeys</emphasis>
</entry>
    <entry>	LISTofKB_KEYNAME</entry>
  </row>
  <row>
    <entry><emphasis>
XkbKeyAliases</emphasis>
</entry>
    <entry><emphasis>
nKeyAliases</emphasis>
</entry>
    <entry>	LISTofKB_KEYALIAS</entry>
  </row>
  <row>
    <entry><emphasis>
XkbRGNames</emphasis>
</entry>
    <entry><emphasis>
nRadioGroups</emphasis>
</entry>
    <entry>	LISTofATOM</entry>
  </row>
</tbody>
</tgroup>
</informaltable>

<para>
If type names are reported, the <emphasis>
nTypes</emphasis>
 return value reports the number of types defined for the keyboard, and the
list of key type names in <emphasis>
valueList</emphasis>
 has <emphasis>
nTypes</emphasis>
 elements.
</para>


<para>
If key type level names are reported, the list of key type level names in the
<emphasis>
valueList</emphasis>
 has two parts: The <emphasis>
count</emphasis>
 array has <emphasis>
nTypes</emphasis>
 elements, each of which reports the number of level names reported for the
corresponding key type. The <emphasis>
names</emphasis>
 array has <emphasis>
nKTLevels</emphasis>
 atoms and reports the names of each type sequentially. The <emphasis>
nKTLevels</emphasis>
 return value is always equal to the sum of all of the elements of the
<emphasis>
count</emphasis>
 array.
</para>


<para>
If indicator names are reported, the <emphasis>
indicators</emphasis>
 mask specifies the indicators for which names are defined; any indicators not
specified in <emphasis>
indicators</emphasis>
 have the name <emphasis>
None</emphasis>
. The list of indicator names in <emphasis>
valueList</emphasis>
 contains the names of the listed indicators, beginning with the
lowest-numbered indicator for which a name is defined and proceeding to the
highest.
</para>


<para>
If virtual modifier names are reported, the <emphasis>
virtualMods</emphasis>
 mask specifies the virtual modifiers for which names are defined; any virtual
modifiers not specified in <emphasis>
virtualMods</emphasis>
 have the name <emphasis>
None</emphasis>
. The list of virtual modifier names in <emphasis>
valueList</emphasis>
 contains the names of the listed virtual modifiers, beginning with the
lowest-numbered virtual modifier for which a name is defined and proceeding to
the highest.
</para>


<para>
If group names are reported, the <emphasis>
groupNames</emphasis>
 mask specifies the groups for which names are defined; any groups not
specified in <emphasis>
groupNames</emphasis>
 have the name <emphasis>
None</emphasis>
. The list of group names in <emphasis>
valueList</emphasis>
 contains the names of the listed groups, beginning with the lowest-numbered
group for which a name is defined and proceeding to the highest.
</para>


<para>
If key names are reported, the <emphasis>
firstKey</emphasis>
 and <emphasis>
nKeys</emphasis>
 return values specify a range of keys which includes all keys for which names
are defined; any key that does not fall in the range specified by <emphasis>
firstKey</emphasis>
 and <emphasis>
nKeys</emphasis>
 has the name <emphasis>
NullKeyName</emphasis>
. The list of key names in the <emphasis>
valueList</emphasis>
 has <emphasis>
nKeys</emphasis>
 entries and specifies the names of the keys beginning at <emphasis>
firstKey</emphasis>
.
</para>


<para>
If key aliases are reported, the <emphasis>
nKeyAliases</emphasis>
 return value specifies the total number of key aliases defined for the
keyboard. The list of key aliases in <emphasis>
valueList</emphasis>
 has <emphasis>
nKeyAliases</emphasis>
 entries, each of which reports an alias and the real name of the key to which
it corresponds.
</para>


<para>
If radio group names are reported, the <emphasis>
nRadioGroups</emphasis>
 return value specifies the number of radio groups on the keyboard for which
names are defined. The list of radio group names in <emphasis>
valueList</emphasis>
 reports the names of each group and has <emphasis>
nRadioGroups</emphasis>
 entries.
</para>



<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbSetNames</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoargs'>which: KB_NAMEDETAILMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
virtualMods: KB_VMODMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
firstType, nTypes: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
firstKTLevel, nKTLevels: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
totalKTLevelNames: CARD16</entry>
    </row>
    <row>
      <entry role='protoname'>
indicators: KB_INDICATORMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
groupNames: KB_GROUPMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
nRadioGroups: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
firstKey: KEYCODE</entry>
    </row>
    <row>
      <entry role='protoname'>
nKeys, nKeyAliases: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
valueList: LISTofITEMs</entry>
    </row>

    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
, <emphasis>
Atom</emphasis>
, <emphasis>
Value</emphasis>
, <emphasis>
Match</emphasis>
, <emphasis>
Alloc</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
This request changes the symbolic names for the requested components of the
keyboard specified by <emphasis>
deviceSpec</emphasis>
. The <emphasis>
which</emphasis>
 field specifies the components for which one or more names are to be updated.
If <emphasis>
deviceSpec</emphasis>
 does not specify a valid keyboard device, a <emphasis>
Keyboard</emphasis>
 error results. If any undefined bits in <emphasis>
which</emphasis>
 are non-zero, a <emphasis>
Value</emphasis>
 error results. If any error (other than <emphasis>
Alloc</emphasis>
 or <emphasis>
Implementation</emphasis>
) occurs, this request returns without modifying any names.
</para>


<para>
The <emphasis>
which</emphasis>
 and <emphasis>
valueList</emphasis>
 fields specify the components to be changed; the type of each <emphasis>
valueList</emphasis>
 entry, the order in which components appear in the <emphasis>
valueList</emphasis>
 when specified, and the correspondence between components in <emphasis>
which</emphasis>
 and the entries in the <emphasis>
valueList</emphasis>
 are as specified for the <emphasis>
XkbGetNames</emphasis>
 request.
</para>


<para>
If keycodes, geometry, symbols, physical symbols, types or compatibility map
names are to be changed, the corresponding entries in the <emphasis>
valueList</emphasis>
 must have the value <emphasis>
None</emphasis>
 or specify a valid ATOM, else an <emphasis>
Atom</emphasis>
 error occurs.
</para>


<para>
If key type names are to be changed, the <emphasis>
firstType</emphasis>
 and <emphasis>
nTypes</emphasis>
 fields specify a range of types for which new names are supplied, and the list
of key type names in <emphasis>
valueList</emphasis>
 has <emphasis>
nTypes</emphasis>
 elements. Names for types that fall outside of the range specified by
<emphasis>
firstType</emphasis>
 and <emphasis>
nTypes</emphasis>
 are not affected. If this request specifies names for types that are not
present on the keyboard, a <emphasis>
Match</emphasis>
 error results. All of the type names in the <emphasis>
valueList</emphasis>
 must be valid ATOMs or have the value <emphasis>
None</emphasis>
, or an <emphasis>
Atom</emphasis>
 error results.
</para>


<para>
The names of the first four keyboard types are specified by the XKB extension
and cannot be changed; including any of the canonical types in this request
causes an <emphasis>
Access</emphasis>
 error, as does trying to assign the name reserved for a canonical type to one
of the other key types.
</para>


<para>
If key type level names are to be changed, the <emphasis>
firstKTLevel</emphasis>
 and <emphasis>
nKTLevels</emphasis>
 fields specify a range of key types for which new level names are supplied,
and the list of key type level names in the <emphasis>
valueList</emphasis>
 has two parts: The <emphasis>
count</emphasis>
 array has <emphasis>
nKTLevels</emphasis>
 elements, each of which specifies the number of levels for which names are
supplied on the corresponding key type; any levels for which no names are
specified are assigned the name <emphasis>
None</emphasis>
. The <emphasis>
names</emphasis>
 array has <emphasis>
totalKTLevels</emphasis>
 atoms and specifies the names of each type sequentially. The <emphasis>
totalKTLevels</emphasis>
 field must always equal the sum of all of the elements of the <emphasis>
count</emphasis>
 array. Level names for types that fall outside of the specified range are not
affected. If this request specifies level names for types that are not present
on the keyboard, or if it specifies more names for a type than the type has
levels, a <emphasis>
Match</emphasis>
 error results. All specified type level names must be <emphasis>
None</emphasis>
 or a valid ATOM or an <emphasis>
Atom</emphasis>
 error results.
</para>


<para>
If indicator names are to be changed, the <emphasis>
indicators</emphasis>
 mask specifies the indicators for which new names are specified; the names for
indicators not specified in <emphasis>
indicators</emphasis>
 are not affected. The list of indicator names in <emphasis>
valueList</emphasis>
 contains the new names for the listed indicators, beginning with the
lowest-numbered indicator for which a name is defined and proceeding to the
highest. All specified indicator names must be a valid ATOM or <emphasis>
None</emphasis>
, or an <emphasis>
Atom</emphasis>
 error results.
</para>


<para>
If virtual modifier names are to be changed, the <emphasis>
virtualMods</emphasis>
 mask specifies the virtual modifiers for which new names are specified; names
for any virtual modifiers not specified in <emphasis>
virtualMods</emphasis>
 are not affected. The list of virtual modifier names in <emphasis>
valueList</emphasis>
 contains the new names for the specified virtual modifiers, beginning with the
lowest-numbered virtual modifier for which a name is defined and proceeding to
the highest. All virtual modifier names must be valid ATOMs or <emphasis>
None</emphasis>
, or an <emphasis>
Atom</emphasis>
 error results.
</para>


<para>
If group names are to be changed, the <emphasis>
groupNames</emphasis>
 mask specifies the groups for which new names are specified; the name of any
group not specified in <emphasis>
groupNames</emphasis>
 is not changed. The list of group names in <emphasis>
valueList</emphasis>
 contains the new names for the listed groups, beginning with the
lowest-numbered group for which a name is defined and proceeding to the
highest. All specified group names must be a valid ATOM or <emphasis>
None</emphasis>
, or an <emphasis>
Atom</emphasis>
 error results.
</para>


<para>
If key names are to be changed, the <emphasis>
firstKey</emphasis>
 and <emphasis>
nKeys</emphasis>
 fields specify a range of keys for which new names are defined; the name of
any key that does not fall in the range specified by <emphasis>
firstKey</emphasis>
 and <emphasis>
nKeys</emphasis>
 is not changed. The list of key names in the <emphasis>
valueList</emphasis>
 has <emphasis>
nKeys</emphasis>
 entries and specifies the names of the keys beginning at <emphasis>
firstKey</emphasis>
.
</para>


<para>
If key aliases are to be changed, the <emphasis>
nKeyAliases</emphasis>
 field specifies the length of a new list of key aliases for the keyboard. The
list of key aliases can only be replaced in its entirety; it cannot be
replaced. The list of key aliases in <emphasis>
valueList</emphasis>
 has <emphasis>
nKeyAliases</emphasis>
 entries, each of which reports an alias and the real name of the key to which
it corresponds.
</para>


<para>
XKB does not check key names or aliases for consistency and validity, so
applications should take care not to assign duplicate names or aliases
</para>


<para>
If radio group names are to be changed, the <emphasis>
nRadioGroups</emphasis>
 field specifies the length of a new list of radio group names for the
keyboard. There is no way to edit the list of radio group names; it can only be
replaced in its entirety. The list of radio group names in <emphasis>
valueList</emphasis>
 reports the names of each group and has <emphasis>
nRadioGroups</emphasis>
 entries. If the list of radio group names specifies names for more radio
groups than XKB allows (32), a <emphasis>
Match</emphasis>
 error results. All specified radio group names must be valid ATOMs or have the
value <emphasis>
None</emphasis>
, or an <emphasis>
Atom</emphasis>
 error results.
</para>


</sect2>
<sect2 id='Querying_and_Changing_Keyboard_Geometry'>
<title>Querying and Changing Keyboard Geometry</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbGetGeometry</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC
      </entry>
    </row>
    <row>
      <entry role='protoname'>
name: ATOM</entry>
    </row>
    <row>
      <entry role='protoreply'>
deviceID: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoname'>
name: ATOM
      </entry>
    </row>
    <row>
      <entry role='protoname'>
found: BOOL
      </entry>
    </row>
    <row>
      <entry role='protoname'>
widthMM, heightMM: CARD16
      </entry>
    </row>
    <row>
      <entry role='protoname'>
baseColorNdx, labelColorNdx: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoname'>
properties: LISTofKB_PROPERTY
      </entry>
    </row>
    <row>
      <entry role='protoname'>
colors: LISTofSTRING8
      </entry>
    </row>
    <row>
      <entry role='protoname'>
shapes: LISTofKB_SHAPE
      </entry>
    </row>
    <row>
      <entry role='protoname'>
sections: LISTofKB_SECTION
      </entry>
    </row>
    <row>
      <entry role='protoname'>
doodads: LISTofKB_DOODAD
      </entry>
    </row>
    <row>
      <entry role='protoname'>
keyAliases: LISTofKB_KEYALIAS</entry>
    </row>
    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
</entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
This request returns a description of the physical layout of a keyboard. If the
<emphasis>
name</emphasis>
 field has the value <emphasis>
None</emphasis>
, or if name is identical to the name of the geometry for the keyboard
specified by <emphasis>
deviceSpec</emphasis>
, this request returns the geometry of the keyboard specified by <emphasis>
deviceSpec</emphasis>
; otherwise, if <emphasis>
name</emphasis>
 is a valid atom other than <emphasis>
None</emphasis>
, the server returns the keyboard geometry description with that name in the
server database of keyboard components (see <link linkend='The_Server_Database_of_Keyboard_Components'>The Server Database of Keyboard
Components</link>) if one exists. If <emphasis>
deviceSpec</emphasis>
 does not specify a valid keyboard device, a <emphasis>
Keyboard</emphasis>
 error results. If <emphasis>
name</emphasis>
 has a value other than <emphasis>
None</emphasis>
 or a valid ATOM, an <emphasis>
Atom</emphasis>
 error results.
</para>


<para>
On successful return, the <emphasis>
deviceID</emphasis>
 field reports the X Input extension identifier of the keyboard device
specified in the request, or <emphasis>
0</emphasis>
 if the server does not support the input extension.
</para>


<para>
The <emphasis>
found</emphasis>
 return value reports whether the requested geometry was available. If
<emphasis>
found</emphasis>
 is <emphasis>
False</emphasis>
, no matching geometry was found and the remaining fields in the request reply
are undefined; if <emphasis>
found</emphasis>
 is <emphasis>
True</emphasis>
, the remaining fields of the reply describe the requested keyboard geometry.
The interpretation of the components that make up a keyboard geometry is
described in detail in <link linkend='Keyboard_Geometry'>Keyboard
Geometry</link>
</para>



<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbSetGeometry</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
name: ATOM</entry>
    </row>
    <row>
      <entry role='protoname'>
widthMM, heightMM, CARD16</entry>
    </row>
    <row>
      <entry role='protoname'>
baseColorNdx, labelColorNdx: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
shapes: LISTofKB_SHAPE</entry>
    </row>
    <row>
      <entry role='protoname'>
sections: LISTofKB_SECTION</entry>
    </row>
    <row>
      <entry role='protoname'>
properties: LISTofKB_PROPERTY</entry>
    </row>
    <row>
      <entry role='protoname'>
colors: LISTofSTRING8</entry>
    </row>
    <row>
      <entry role='protoname'>
doodads: LISTofKB_DOODAD</entry>
    </row>
    <row>
      <entry role='protoname'>
keyAliases: LISTofKB_KEYALIAS</entry>
    </row>

    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
, <emphasis>
Atom</emphasis>
, <emphasis>
Value</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
This request changes the reported description of the geometry for the keyboard
specified by <emphasis>
deviceSpec</emphasis>
. If deviceSpec does not specify a valid keyboard device, a <emphasis>
Keyboard</emphasis>
 error results.
</para>


<para>
The <emphasis>
name</emphasis>
 field specifies the name of the new keyboard geometry and must be a valid ATOM
or an <emphasis>
Atom</emphasis>
 error results. The new geometry is not added to the server database of
keyboard components, but it can be retrieved using the <emphasis>
XkbGetGeometry</emphasis>
 request for as long as it is bound to the keyboard. The keyboard geometry
symbolic name is also updated from the name field, and an <emphasis>
XkbNamesNotify</emphasis>
 event is generated, if necessary.
</para>


<para>
The list of <emphasis>
colors</emphasis>
 must include at least two definitions, or a <emphasis>
Value</emphasis>
 error results. All color definitions in the geometry must specify a legal
color (i.e. must specify a valid index for one of the entries of the <emphasis>
colors</emphasis>
 list) or a <emphasis>
Match</emphasis>
 error results. The <emphasis>
baseColorNdx</emphasis>
 and the <emphasis>
labelColorNdx</emphasis>
 must be different or a <emphasis>
Match</emphasis>
 error results.
</para>


<para>
The list of <emphasis>
shapes</emphasis>
 must include at least one shape definition, or a <emphasis>
Value</emphasis>
 error results. If any two shapes have the same name, a <emphasis>
Match</emphasis>
 error result. All doodads and keys which specify shape must specify a valid
index for one of the elements of the <emphasis>
shapes</emphasis>
 list, or a <emphasis>
Match</emphasis>
 error results.
</para>


<para>
All section, shape and doodad names must be valid ATOMs or an <emphasis>
Atom</emphasis>
 error results; the constant <emphasis>
None</emphasis>
 is not permitted for any of these components.
</para>


<para>
All doodads must be of a known type; XKB does not support "private" doodad
types.
</para>


<para>
If, after rotation, any keys or doodads fall outside of the bounding box for a
section, the bounding box is automatically adjusted to the minimum size which
encloses all of its components.
</para>


<para>
If, after adjustment and rotation, the bounding box of any section or doodad
extends below zero on either the X or Y axes, the entire geometry is translated
so that the minimum extent along either axis is zero.
</para>


<para>
If, after rotation and translation, any keyboard components fall outside of the
rectangle specified by <emphasis>
widthMM</emphasis>
 and <emphasis>
heightMM</emphasis>
, the keyboard dimensions are automatically resized to the minimum bounding box
that surrounds all components. Otherwise, the width and height of the keyboard
are left as specified.
</para>


<para>
The <emphasis>
under</emphasis>
 field of any overlay key definitions must specify a key that is in the section
that contains the overlay key, or a <emphasis>
Match</emphasis>
 error results. This request does not check the value of the <emphasis>
over</emphasis>
 field of an overlay key definition, so applications must be careful to avoid
conflicts with actual keys.
</para>


<para>
This request does not verify that key names or aliases are unique. It also does
not verify that all key names specified in the geometry are bound to some
keycode or that all keys that are named in the keyboard definition are also
available in the geometry. Applications should make sure that keyboard geometry
has no internal conflicts and is consistent with the other components of the
keyboard definition, but XKB does not check for or guarantee it.
</para>


</sect2>
<sect2 id='Querying_and_Changing_Per_Client_Flags'>
<title>Querying and Changing Per-Client Flags</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbPerClientFlags</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
change: KB_PCFMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
value: KB_PCFMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
ctrlsToChange: KB_BOOLCTRLMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
autoCtrls: KB_BOOLCTRLMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
autoCtrlValues: KB_BOOLCTRLMASK</entry>
    </row>
    <row>
      <entry role='protoreply'>
deviceID: CARD8
supported: KB_PCFMASK
value: KB_PCFMASK
autoCtrls: KB_BOOLCTRLMASK
autoCtrlValues: KB_BOOLCTRLMASK
where: KB_PCFMASK:</entry>
    </row>

    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
, <emphasis>
Value</emphasis>
, <emphasis>
Match</emphasis>
, <emphasis>
Alloc</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
Changes the client specific flags for the keyboard specified by <emphasis>
deviceSpec</emphasis>
. Reports a <emphasis>
Keyboard</emphasis>
 error if <emphasis>
deviceSpec</emphasis>
 does not specify a valid keyboard device.
</para>


<para>
Any flags specified in <emphasis>
change</emphasis>
 are set to the corresponding values in <emphasis>
value</emphasis>
, provided that the server supports the requested control. Legal
per-client-flags are:
</para>

<informaltable frame='topbot'>
<?dbfo keep-together="always" ?>
<tgroup cols='2' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
<colspec colname='c2' colwidth='1.0*'/>
<thead>
  <row rowsep='1'>
    <entry>Flag…</entry>
    <entry>Described in…</entry>
  </row>
</thead>
<tbody>
  <row>
    <entry><emphasis>
XkbPCF_DetectableAutorepeat</emphasis>
</entry>
    <entry><link linkend='Detectable_Autorepeat'>Detectable
Autorepeat</link></entry>
  </row>
  <row>
    <entry><emphasis>
XkbPCF_GrabsUseXKBStateMask</emphasis>
</entry>
    <entry><link linkend='Setting_a_Passive_Grab_for_an_XKB_State'>Setting a Passive Grab
for an XKB State</link></entry>
  </row>
  <row>
    <entry><emphasis>
XkbPCF_AutoResetControlsMask</emphasis>
</entry>
    <entry><link linkend='Automatic_Reset_of_Boolean_Controls'>Automatic Reset of
Boolean Controls</link></entry>
  </row>
  <row>
    <entry><emphasis>
XkbPCF_LookupStateWhenGrabbed</emphasis>
</entry>
    <entry><link linkend='Effects_of_XKB_on_Core_Protocol_Events'>Effects of XKB on Core
Protocol Events</link></entry>
  </row>
  <row>
    <entry><emphasis>
XkbPCF_SendEventUsesXKBState</emphasis>
</entry>
    <entry><link linkend='Sending_Events_to_Clients'>Sending Events to
Clients</link></entry>
  </row>
</tbody>
</tgroup>
</informaltable>

<para>
If <emphasis>
PCF_AutoResetControls</emphasis>
 is set in both <emphasis>
change</emphasis>
 and <emphasis>
value</emphasis>
, the client’s mask of controls to be changed is updated from <emphasis>
ctrlsToChange</emphasis>
, <emphasis>
autoCtrls</emphasis>
, and <emphasis>
autoCtrlValues</emphasis>
. Any controls specified in <emphasis>
ctrlsToChange</emphasis>
 are modified in the auto-reset controls mask for the client; the corresponding
bits from the <emphasis>
autoCtrls</emphasis>
 field are copied into the auto-reset controls mask and the corresponding bits
from <emphasis>
autoCtrlValues</emphasis>
 are copied into the auto-reset controls state values. If any controls are
specified in <emphasis>
autoCtrlValues</emphasis>
 but not in <emphasis>
autoCtrls</emphasis>
, a <emphasis>
Match</emphasis>
 error results. If any controls are specified in <emphasis>
autoCtrls</emphasis>
 but not in <emphasis>
ctrlsToChange</emphasis>
, a <emphasis>
Match</emphasis>
 error results.
</para>


<para>
If <emphasis>
PCF_AutoResetControls</emphasis>
 is set in <emphasis>
change</emphasis>
 but not in <emphasis>
value</emphasis>
, the client’s mask of controls to be changed is reset to all zeroes (i.e.
the client does not change any controls when it exits).
</para>


<para>
This request reports a <emphasis>
Match</emphasis>
 error if a bit is set in any of the value masks but not in the control mask
that governs it or a <emphasis>
Value</emphasis>
 error if any undefined bits are set in any of the masks.
</para>


<para>
On successful return, the <emphasis>
deviceID</emphasis>
 field reports the X Input extension identifier of the keyboard, or <emphasis>
0</emphasis>
 if the server does not support the X Input Extension.
</para>


<para>
The <emphasis>
supported</emphasis>
 return value reports the set of per-client flags that are supported by the
server; in this version of XKB, only the <emphasis>
XkbPCF_DetectableAutorepeat</emphasis>
 per-client flag is optional; all other per-client flags must be supported.
</para>


<para>
The <emphasis>
value</emphasis>
 return value reports the current settings of all per-client flags for the
specified keyboard. The <emphasis>
autoCtrls</emphasis>
 return value reports the current set of controls to be reset when the client
exits, while the <emphasis>
autoCtrlValues</emphasis>
 return value reports the state to which they should be set.
</para>


</sect2>
<sect2 id='Using_the_Servers_Database_of_Keyboard_Components'>
<title>Using the Server’s Database of Keyboard Components</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbListComponents</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
maxNames: CARD16</entry>
    </row>
    <row>
      <entry role='protoname'>
keymapsSpec: STRING8</entry>
    </row>
    <row>
      <entry role='protoname'>
keycodesSpec: STRING8</entry>
    </row>
    <row>
      <entry role='protoname'>
typesSpec: STRING8</entry>
    </row>
    <row>
      <entry role='protoname'>
compatMapSpec: STRING8</entry>
    </row>
    <row>
      <entry role='protoname'>
symbolsSpec: STRING8</entry>
    </row>
    <row>
      <entry role='protoname'>
geometrySpec: STRING8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
deviceID: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
extra: CARD16
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
keymaps,keycodes,types,compatMaps: LISTofKB_COMPONENTNAME
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
symbols, geometries: LISTofKB_COMPONENTNAME</entry>
    </row>
    <row>
      <entry role='protoreply'>Where:</entry>
    </row>
    <row>
      <entry role='protoreply'>KB_COMPONENTNAME	{	hints: CARD8, name:
STRING8 }</entry>
    </row>
    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
, <emphasis>
Alloc</emphasis>
</entry>
    </row>
  </tbody>
  </tgroup>
</informaltable>

<para>
This request returns one or more lists of keyboard components that are
available from the X server database of keyboard components for the device
specified by <emphasis>
deviceSpec</emphasis>
. The X server is allowed, but not required or expected, to maintain separate
databases for each keyboard device. A <emphasis>
Keyboard</emphasis>
 error results if <emphasis>
deviceSpec</emphasis>
 does not specify a valid keyboard device.
</para>


<para>
The <emphasis>
maxNames</emphasis>
 field specifies the maximum number of component names to be reported, in
total, by this request.
</para>


<para>
The <emphasis>
keymapsSpec</emphasis>
, <emphasis>
keycodesSpec</emphasis>
, <emphasis>
typesSpec</emphasis>
, <emphasis>
compatMapSpec</emphasis>
, <emphasis>
symbolsSpec</emphasis>
 and <emphasis>
geometrySpec</emphasis>
 request fields specify a pattern to be matched against the names of all
components of the corresponding type in the server database of keyboard
components.
</para>


<para>
Each pattern uses the ISO Latin-1 encoding and should contain only parentheses,
the wildcard characters "?" and "*" or characters that are permitted in a
component class or member name (see <link linkend='Component_Names'>Component Names</link>). Illegal
characters in a pattern are simply ignored; no error results if a pattern
contains illegal characters.
</para>


<para>
Comparison is case-sensitive and, in a pattern, the "?" wildcard character
matches any single character except parentheses while the "*" character matches
any number of characters except parentheses. If an implementation accepts
characters other than those required by XKB, whether or not those characters
match either wildcard is also implementation dependent. An empty pattern does
not match any component names.
</para>


<para>
On successful return, the <emphasis>
deviceID</emphasis>
 return value reports the X Input Extension device identifier of the specified
device, or <emphasis>
0</emphasis>
 if the server does not support the X input extension. The <emphasis>
extra</emphasis>
 return value reports the number of matching component names that could not be
returned due to the setting of the <emphasis>
maxNames</emphasis>
 field in the request.
</para>


<para>
The <emphasis>
keymaps</emphasis>
, <emphasis>
keycodes</emphasis>
, <emphasis>
types</emphasis>
, <emphasis>
compatMaps</emphasis>
, <emphasis>
symbols</emphasis>
 and <emphasis>
geometries</emphasis>
 return the hints (see <link linkend='Component_Hints'>Component
Hints</link>) and names of any components from the server database that match
the corresponding pattern.
</para>


<para>
<link linkend='The_Server_Database_of_Keyboard_Components'>The Server Database of Keyboard
Components</link> describes the X server database of keyboard components in
more detail.
</para>



<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbGetKbdByName</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
need, want: KB_GBNDETAILMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
load: BOOL</entry>
    </row>
    <row>
      <entry role='protoname'>
keymapsSpec: STRING8</entry>
    </row>
    <row>
      <entry role='protoname'>
keycodesSpec, typesSpec: STRING8</entry>
    </row>
    <row>
      <entry role='protoname'>
compatMapSpec, symbolsSpec: STRING8</entry>
    </row>
    <row>
      <entry role='protoname'>
geometrySpec: STRING8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
deviceID: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
minKeyCode, maxKeyCode: KEYCODE
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
loaded, newKeyboard: BOOL
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
found, reported: KB_GBNDETAILMASK
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
map: optional <emphasis>
XkbGetMap</emphasis>
 reply
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
compat: optional <emphasis>
XkbGetCompatMap</emphasis>
 reply
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
indicators: optional <emphasis>
XkbGetIndicatorMap</emphasis>
 reply
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
names: optional <emphasis>
XkbGetNames</emphasis>
 reply
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
geometry: optional <emphasis>
XkbGetGeometry</emphasis>
 reply</entry>
    </row>

    <row>
      <entry role='protoerror'>Errors: <emphasis>
Keyboard</emphasis>
, <emphasis>
Access</emphasis>
, <emphasis>
Alloc</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
Assembles and returns a keymap from the current mapping and specified elements
from the server database of keymap components for the keyboard specified by
<emphasis>
deviceSpec</emphasis>
, and optionally replaces the current keyboard mapping with the newly generated
description. If <emphasis>
deviceSpec</emphasis>
 does not specify a valid keyboard device, a <emphasis>
Keyboard</emphasis>
 error results.
</para>


<para>
The <emphasis>
keymapsSpec</emphasis>
, <emphasis>
keycodesSpec</emphasis>
, <emphasis>
typesSpec</emphasis>
, <emphasis>
compatMapSpec</emphasis>
, <emphasis>
symbolsSpec</emphasis>
 and <emphasis>
geometrySpec</emphasis>
 component expressions (see <link linkend='Partial_Components_and_Combining_Multiple_Components'>
Partial Components and Combining Multiple Components</link>) specify the
database components to be used to assemble the keyboard description.
</para>


<para>
The <emphasis>
want</emphasis>
 field lists the pieces of the keyboard description that the client wants to
have reported for the newly constructed keymap. The <emphasis>
need</emphasis>
 field lists all of the pieces that must be reported. If any of the pieces in
<emphasis>
need</emphasis>
 cannot be loaded from the specified names, no description of the keyboard is
returned.
</para>


<para>
The <emphasis>
want</emphasis>
 and <emphasis>
need</emphasis>
 fields can include any combinations of these <emphasis>
XkbGetMapByName</emphasis>
 (GBN) components:
</para>

<informaltable frame='topbot'>
<?dbfo keep-together="always" ?>
<tgroup cols='3' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
<colspec colname='c2' colwidth='1.0*'/>
<colspec colname='c3' colwidth='1.0*'/>
<thead>
  <row rowsep='1'>
    <entry>XkbGetMapByName Keyboard Component…</entry>
    <entry>Database Component…</entry>
    <entry>Components of Keyboard Description</entry>
  </row>
</thead>
<tbody>
  <row>
    <entry><emphasis>
XkbGBN_Types</emphasis>
</entry>
    <entry>types</entry>
    <entry>key types</entry>
  </row>
  <row>
    <entry><emphasis>
XkbGBN_CompatMap</emphasis>
</entry>
    <entry>compat</entry>
    <entry>symbol interpretations, group compatibility map</entry>
  </row>
  <row>
    <entry><emphasis>
XkbGBN_ClientSymbols</emphasis>
</entry>
    <entry>symbols, types, keycodes</entry>
    <entry>key types, key symbol mappings, modifier mapping</entry>
  </row>
  <row>
    <entry><emphasis>
XkbGBN_ServerSymbols</emphasis>
</entry>
    <entry>symbols, types, keycodes</entry>
    <entry>key behaviors, key actions, key explicit components, virtual
modifiers, virtual modifier mapping</entry>
  </row>
  <row>
    <entry><emphasis>
XkbGBN_IndicatorMap</emphasis>
</entry>
    <entry>compat</entry>
    <entry>indicator maps, indicator names</entry>
  </row>
  <row>
    <entry><emphasis>
XkbGBN_KeyNames</emphasis>
</entry>
    <entry>keycodes</entry>
    <entry>key names, key aliases</entry>
  </row>
  <row>
    <entry><emphasis>
XkbGBN_Geometry</emphasis>
</entry>
    <entry>geometry</entry>
    <entry>keyboard geometry</entry>
  </row>
  <row>
    <entry><emphasis>
XkbGBN_OtherNames</emphasis>
</entry>
    <entry>all</entry>
    <entry>key types, symbol interpretations, indicator maps, names,
geometry</entry>
  </row>
</tbody>
</tgroup>
</informaltable>

<para>
If either field contains a GBN component that depends on some database
component for which the request does not supply an expression, XKB
automatically substitutes the special pattern "%" which copies the
corresponding component from the current keyboard description, as described in
<link linkend='Partial_Components_and_Combining_Multiple_Components'>Partial Components and Combining
Multiple Components</link>.
</para>


<para>
The <emphasis>
load</emphasis>
 flag asks the server to replace the current keyboard description for <emphasis>
deviceSpec</emphasis>
 with the newly constructed keyboard description. If <emphasis>
load</emphasis>
 is <emphasis>
True</emphasis>
, the request must include component expressions for all of the database
components; if any are missing, XKB substitutes "%" as described above.
</para>


<para>
If all necessary components are both specified and found, the new keyboard
description is loaded. If the new keyboard description has a different geometry
or keycode range than the previous keyboard description, XKB sends <emphasis>
XkbNewKeyboardNotify</emphasis>
 events to all interested clients. See
<link linkend='Replacing_the_Keyboard_On_the_Fly'>Replacing the Keyboard
"On-the-Fly"</link> for more information about the effects of replacing the
keyboard description on the fly.
</para>


<para>
If the range of keycodes changes, clients that have requested <emphasis>
XkbNewKeyboardNotify</emphasis>
 events are not sent any other change notification events by this request.
Clients that do not request <emphasis>
XkbNewKeyboardNotify</emphasis>
 events are sent other XKB change notification events (e.g. <emphasis>
XkbMapNotify</emphasis>
, <emphasis>
XkbNamesNotify</emphasis>
) as necessary to alert them to as many of the keyboard changes as possible.
</para>


<para>
If no error occurs, the request reply reports the GBN components that were
found and sends a description of any of the resulting keyboard that includes
and of the components that were requested.
</para>


<para>
The <emphasis>
deviceID</emphasis>
 return value reports the X Input extension device identifier of the keyboard
that was used, or <emphasis>
0</emphasis>
 if the server does not support the X input extension.
</para>


<para>
The <emphasis>
minKeyCode</emphasis>
 and <emphasis>
maxKeyCode</emphasis>
 return values report the legal range of keycodes for the keyboard description
that was created. If the resulting keyboard description does not include at
least one of the key names, client symbols or server symbols components,
<emphasis>
minKeyCode</emphasis>
 and <emphasis>
maxKeyCode</emphasis>
 are both <emphasis>
0</emphasis>
.
</para>


<para>
The <emphasis>
loaded</emphasis>
 return value reports whether or not the existing keyboard definition was
replaced with the newly created one. If <emphasis>
loaded</emphasis>
 is <emphasis>
True</emphasis>
, the <emphasis>
newKeyboard</emphasis>
 return value reports whether or not the new map changed the geometry or range
of keycodes and caused <emphasis>
XkbNewKeyboardNotify</emphasis>
 events for clients that have requested them.
</para>


<para>
The <emphasis>
found</emphasis>
 return value reports the GBN components that were present in the keymap that
was constructed by this request. The <emphasis>
reported</emphasis>
 return value lists the subset of those components for which descriptions
follow. if any of the components specified in the <emphasis>
need</emphasis>
 field of the request were not found, <emphasis>
reported</emphasis>
 is empty, otherwise it contains the intersection of the <emphasis>
found</emphasis>
 return value with the union of the <emphasis>
need</emphasis>
 and <emphasis>
want</emphasis>
 request fields.
</para>


<para>
If any of <emphasis>
GBN_Types</emphasis>
, <emphasis>
GBN_ClientSymbols</emphasis>
 or <emphasis>
GBN_ServerSymbols</emphasis>
 are set in <emphasis>
reported</emphasis>
, the <emphasis>
map</emphasis>
 return value has the same format as the reply to an <emphasis>
XkbGetMap</emphasis>
 request and reports the corresponding pieces of the newly constructed keyboard
description.
</para>


<para>
If <emphasis>
GBN_CompatMap</emphasis>
 is set in <emphasis>
reported</emphasis>
, the <emphasis>
compat</emphasis>
 return value has the same format as the reply to an <emphasis>
XkbGetCompatMap</emphasis>
 request and reports the symbol interpretations and group compatibility map for
the newly constructed keyboard description.
</para>


<para>
If <emphasis>
GBN_IndicatorMap</emphasis>
 is set in <emphasis>
reported</emphasis>
, the <emphasis>
indicators</emphasis>
 return value has the same format as the reply to an <emphasis>
XkbGetIndicatorMap</emphasis>
 request and reports the physical indicators and indicator maps for the newly
constructed keyboard description.
</para>


<para>
If <emphasis>
GBN_KeyNames</emphasis>
 or <emphasis>
GBN_OtherNames</emphasis>
 are set in <emphasis>
reported</emphasis>
, the <emphasis>
names</emphasis>
 return value has the same format as the reply to an <emphasis>
XkbGetNames</emphasis>
 reply and reports the corresponding set of symbolic names for the newly
constructed keyboard description.
</para>


<para>
If <emphasis>
GBN_Geometry</emphasis>
 is set in <emphasis>
reported</emphasis>
, the <emphasis>
geometry</emphasis>
 return value has the same format as the reply to an <emphasis>
XkbGetGeometryMap</emphasis>
 request and reports the keyboard geometry for the newly constructed keyboard
description.
</para>


</sect2>
<sect2 id='Querying_and_Changing_Input_Extension_Devices'>
<title>Querying and Changing Input Extension Devices</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbGetDeviceInfo</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
wanted: KB_XIDEVFEATUREMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
ledClass: KB_LEDCLASSSPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
ledID: KB_IDSPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
allButtons: BOOL</entry>
    </row>
    <row>
      <entry role='protoname'>
firstButton, nButtons: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
deviceID: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
present: KB_XIDEVFEATUREMASK
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
supported: KB_XIFEATUREMASK
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
unsupported: KB_XIFEATUREMASK
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
firstBtnWanted: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
nBtnsWanted: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
firstBtnRtrn: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
nBtnsRtrn: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
totalBtns: CARD8
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
hasOwnState: BOOL
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
dfltKbdFB, dfltLedFB: KB_IDSPEC
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
devType: ATOM
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
name: STRING
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
btnActions: LISTofKB_ACTION
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
leds: LISTofKB_DEVICELEDINFO</entry>
    </row>

    <row>
      <entry role='protoerror'>Errors: <emphasis>
Device</emphasis>
, <emphasis>
Match</emphasis>
, <emphasis>
Access</emphasis>
, <emphasis>
Alloc</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
Reports a subset of the XKB-supplied information about the input device
specified by <emphasis>
deviceSpec</emphasis>
. Unlike most XKB requests, the device specified for <emphasis>
XkbGetDeviceInfo</emphasis>
 need not be a keyboard device. Nonetheless, a <emphasis>
Keyboard</emphasis>
 error results if <emphasis>
deviceSpec</emphasis>
 does not specify a valid core or input extension device.
</para>


<para>
The <emphasis>
wanted</emphasis>
 field specifies the types of information to be returned, and controls the
interpretation of the other request fields.
</para>


<para>
If the server does not support assignment of XKB actions to extension device
buttons, the <emphasis>
allButtons</emphasis>
, <emphasis>
firstButton</emphasis>
 and <emphasis>
nButtons</emphasis>
 fields are ignored.
</para>


<para>
Otherwise, if the <emphasis>
XkbXI_ButtonActions</emphasis>
 flag is set in <emphasis>
wanted</emphasis>
, the <emphasis>
allButtons</emphasis>
, <emphasis>
firstButton</emphasis>
 and <emphasis>
nButtons</emphasis>
 fields specify the device buttons for which actions should be returned.
Setting <emphasis>
allButtons</emphasis>
 to <emphasis>
True</emphasis>
 requests actions for all device buttons; if <emphasis>
allButtons</emphasis>
 is <emphasis>
False</emphasis>
, <emphasis>
firstButton</emphasis>
 and <emphasis>
nButtons</emphasis>
 specify a range of buttons for which actions are requested. If the device has
no buttons or if <emphasis>
firstButton</emphasis>
 and <emphasis>
nButtons</emphasis>
 specify illegal buttons, a <emphasis>
Match</emphasis>
 error results. If <emphasis>
allButtons</emphasis>
 is <emphasis>
True</emphasis>
, <emphasis>
firstButton</emphasis>
 and <emphasis>
nButtons</emphasis>
 are ignored.
</para>


<para>
If the server does not support XKB access to any aspect of the indicators on
extension devices, or if the <emphasis>
wanted</emphasis>
 field does not include any of the indicator flags, the <emphasis>
ledClass</emphasis>
 and <emphasis>
ledID</emphasis>
 fields are ignored. Otherwise, <emphasis>
ledClass</emphasis>
 and <emphasis>
ledID</emphasis>
 specify one or more feedback(s) for which indicator information is requested.
If <emphasis>
ledClass</emphasis>
 or <emphasis>
ledID</emphasis>
 have illegal values, a <emphasis>
Value</emphasis>
 error results. If they have legal values but do not specify a keyboard or
indicator class feedback for the device in question, a <emphasis>
Match</emphasis>
 error results.
</para>


<para>
The <emphasis>
ledClass</emphasis>
 field can specify either <emphasis>
KbdFeedbackClass</emphasis>
, <emphasis>
LedFeedbackClass</emphasis>
, <emphasis>
XkbDfltXIClass</emphasis>
, or <emphasis>
XkbAllXIClasses</emphasis>
. If at least one keyboard feedback is defined for the specified device,
<emphasis>
XkbDfltXIClass</emphasis>
 is equivalent to <emphasis>
KbdFeedbackClass</emphasis>
, otherwise it is equivalent to <emphasis>
LedFeedbackClass</emphasis>
. If <emphasis>
XkbAllXIClasses</emphasis>
 is specified, this request returns information about both indicator and
keyboard class feedbacks which match the requested identifier, as described
below.
</para>


<para>
The <emphasis>
ledID</emphasis>
 field can specify any valid input extension feedback identifier, <emphasis>
XkbDfltXIId</emphasis>
, or <emphasis>
XkbAllXIIds</emphasis>
. The default keyboard feedback is the one that is affected by core protocol
requests; the default led feedback is implementation-specific. If <emphasis>
XkbAllXIIds</emphasis>
 is specified, this request returns indicator information about all feedbacks
of the class(es) specified by <emphasis>
ledClass</emphasis>
.
</para>


<para>
If no error results, the <emphasis>
deviceID</emphasis>
 return value reports the input extension device identifier of the device for
which values are being returned. The <emphasis>
supported</emphasis>
 return value reports the set of optional XKB extension device features that
are supported by this implementation (see
<link linkend='Interactions_Between_XKB_and_the_X_Input_Extension'>
Interactions Between XKB and the X Input
Extension</link>) for the specified device, and the unsupported return value
reports any <emphasis>
unsupported</emphasis>
 features.
</para>


<para>
If <emphasis>
hasOwnState</emphasis>
 is <emphasis>
True</emphasis>
, the device is also a keyboard, and any indicator maps bound to the device use
the current state and control settings for this device to control automatic
changes. If <emphasis>
hasOwnState</emphasis>
 is <emphasis>
False</emphasis>
, the state and control settings of the core keyboard device control automatic
indicator changes.
</para>


<para>
The <emphasis>
name</emphasis>
 field reports the X Input Extension name for the device. The <emphasis>
devType</emphasis>
 field reports the X Input Extension device type. Both fields are provided
merely for convenience and are not interpreted by XKB.
</para>


<para>
The <emphasis>
present</emphasis>
 return value reports the kinds of device information being returned, and
controls the interpretation of the remaining fields. The <emphasis>
present</emphasis>
 field consists of the <emphasis>
wanted</emphasis>
 field from the original request minus the flags for any unsupported features.
</para>


<para>
If <emphasis>
XkbXI_ButtonActions</emphasis>
 is set in <emphasis>
present</emphasis>
, the <emphasis>
totalBtns</emphasis>
 return value reports the total number of buttons present on the device,
<emphasis>
firstBtnWanted</emphasis>
 and <emphasis>
nBtnsWanted</emphasis>
 specify the range of buttons for which actions were requested, and the
<emphasis>
firstBtnRtrn</emphasis>
 and <emphasis>
nBtnsRtrn </emphasis>
values specify the range of buttons for which actions are reported. The
<emphasis>
actionsRtrn</emphasis>
 list has <emphasis>
nButtonsRtrn</emphasis>
 entries which contain the actions bound to the specified buttons on the
device. Any buttons for which actions were requested but not returned have the
action <emphasis>
NoAction()</emphasis>
.
</para>


<para>
If any indicator information is reported, the leds list contains one element
for each requested feedback. For example, if <emphasis>
ledClass</emphasis>
 is <emphasis>
XkbAllXIClasses</emphasis>
 and <emphasis>
ledID</emphasis>
 is <emphasis>
XkbAllXIIds</emphasis>
, <emphasis>
leds</emphasis>
 describes all of the indicators on the device and has one element for each
keyboard or led class feedback defined for the device. If any information at
all is reported about a feedback, the set of physical indicators is also
reported in the <emphasis>
physIndicators</emphasis>
 field of the corresponding element of <emphasis>
leds</emphasis>
.
</para>


<para>
If the server supports assignment of indicator maps to extension device
indicators, and if the <emphasis>
XkbXI_IndicatorMaps</emphasis>
 flag is set in <emphasis>
wanted</emphasis>
, each member of <emphasis>
leds</emphasis>
 reports any indicators on the corresponding feedback to which names have been
assigned. Any indicators for which no map is reported have the default map,
which allows explicit changes and does not request any automatic changes.
</para>


<para>
If the server supports assignment of indicator names to extension device
indicators, and the <emphasis>
XkbXI_IndicatorNames</emphasis>
 flag is set in <emphasis>
wanted</emphasis>
, each member of <emphasis>
leds</emphasis>
 reports any indicators on the corresponding feedback to which names have been
assigned. Any indicators for which no name is reported have the name <emphasis>
None</emphasis>
.
</para>


<para>
If the server supports XKB access to the state of extension device indicators,
and the <emphasis>
XkbXI_IndicatorState</emphasis>
 flag is set in wanted, each member of leds reports the state of the indicators
on the corresponding feedback.
</para>


<para>
If any unsupported features are requested, and the requesting client has
selected for them, the server sends the client an <emphasis>
XkbExtensionDeviceNotify</emphasis>
 event which indicates that an unsupported feature was requested. This event is
only generated if the client which issued the unsupported request has selected
for it and, if generated, is not sent to any other clients.
</para>



<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbSetDeviceInfo</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>deviceSpec: KB_DEVICESPEC</entry>
    </row>
    <row>
      <entry role='protoname'>
change: KB_XIDEVFEATUREMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
firstBtn, nBtns: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
btnActions:LISTofKB_ACTION</entry>
    </row>
    <row>
      <entry role='protoname'>
leds: LISTofKB_DEVICELEDINFO</entry>
    </row>

    <row>
      <entry role='protoerror'>Errors: <emphasis>
Device</emphasis>
, <emphasis>
Match</emphasis>
, <emphasis>
Access</emphasis>
, <emphasis>
Alloc</emphasis>
</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
Changes a subset of the XKB-supplied information about the input device
specified by <emphasis>
deviceSpec</emphasis>
. Unlike most XKB requests, the device specified for <emphasis>
XkbGetDeviceInfo</emphasis>
 need not be a keyboard device. Nonetheless, a <emphasis>
Keyboard</emphasis>
 error results if <emphasis>
deviceSpec</emphasis>
 does not specify a valid core or input extension device
</para>


<para>
The <emphasis>
change</emphasis>
 field specifies the features for which new values are supplied, and controls
the interpretation of the other request fields.
</para>


<para>
If the server does not support assignment of XKB actions to extension device
buttons, the <emphasis>
firstButton</emphasis>
 and <emphasis>
nButtons</emphasis>
 fields are ignored.
</para>


<para>
Otherwise, if the <emphasis>
XkbXI_ButtonActions</emphasis>
 flag is set in <emphasis>
change</emphasis>
, the <emphasis>
firstBtn</emphasis>
 and <emphasis>
nBtns</emphasis>
 fields specify a range of buttons for which actions are specified in this
request. If the device has no buttons or if <emphasis>
firstBtn</emphasis>
 and <emphasis>
nBtns</emphasis>
 specify illegal buttons, a <emphasis>
Match</emphasis>
 error results.
</para>


<para>
Each element of the <emphasis>
leds</emphasis>
 list describes the changes for a single keyboard or led feedback. If the
<emphasis>
ledClass</emphasis>
 field of any element of <emphasis>
leds</emphasis>
 contains any value other than <emphasis>
KbdFeedbackClass</emphasis>
, <emphasis>
LedFeedbackClass</emphasis>
 or <emphasis>
XkbDfltXIClass</emphasis>
, a <emphasis>
Value</emphasis>
 error results. If the <emphasis>
ledId</emphasis>
 field of any element of leds contains any value other than a valid input
extension feedback identifier or <emphasis>
XkbDfltXIId</emphasis>
, a <emphasis>
Value</emphasis>
 error results. If both fields are valid, but the device has no matching
feedback, a <emphasis>
Match</emphasis>
 error results.
</para>


<para>
The fields of each element of <emphasis>
leds</emphasis>
 are interpreted as follows:
</para>

<itemizedlist>
<listitem>
  <para>If <emphasis>
XkbXI_IndicatorMaps</emphasis>
 is set in <emphasis>
change</emphasis>
 and the server supports XKB assignment of indicator maps to the corresponding
feedback, the maps for all indicators on the corresponding feedback are taken
from <emphasis>
leds</emphasis>
. If the server does not support this feature, any maps specified in <emphasis>
leds</emphasis>
 are ignored.
  </para>
</listitem>
<listitem>
  <para>If <emphasis>
XkbXI_IndicatorNames</emphasis>
 is set in <emphasis>
change</emphasis>
, and the server supports XKB assignment of names to indicators for the
corresponding feedback, the names for all indicators on the corresponding
feedback are taken from <emphasis>
leds</emphasis>
. If the server does not support this feature, any names specified in <emphasis>
leds</emphasis>
 are ignored. Regardless of whether they are used, any names be a valid Atom or
<emphasis>
None</emphasis>
, or an <emphasis>
Atom</emphasis>
 error results.
  </para>
</listitem>
<listitem>
  <para>If <emphasis>
XkbXI_IndicatorState</emphasis>
 is set in change, and the server supports XKB changes to extension device
indicator state, the server attempts to change the indicators on the
corresponding feedback as specified by <emphasis>
leds</emphasis>
. Any indicator maps bound to the feedback are applied, so state changes might
be blocked or have side-effects.
  </para>
</listitem>
</itemizedlist>

<para>
If any unsupported features are requested, and the requesting client has
selected for them, the server sends the client an <emphasis>
XkbExtensionDeviceNotify</emphasis>
 event which indicates that an unsupported feature was requested. This event is
only generated if the client which issued the unsupported request has selected
for it and, if generated, is not sent to any other clients.
</para>


</sect2>
<sect2 id='Debugging_the_X_Keyboard_Extension'>
<title>Debugging the X Keyboard Extension</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbSetDebuggingFlags</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>affectFlags, flags: CARD32</entry>
    </row>
    <row>
      <entry role='protoname'>
affectCtrls, ctrls: CARD32</entry>
    </row>
    <row>
      <entry role='protoname'>
message: STRING
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
currentFlags, supportedFlags: CARD32
      </entry>
    </row>
    <row>
      <entry role='protoreply'>
currentCtrls, supportedCtrls: CARD32</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
This request sets up various internal XKB debugging flags and controls. It is
intended for developer use and may be disabled in production servers. If
disabled, <emphasis>
XkbSetDebuggingFlags</emphasis>
 has no effect but returns <emphasis>
Success</emphasis>
.
</para>


<para>
The <emphasis>
affectFlags</emphasis>
 field specifies the debugging flags to be changed, the <emphasis>
flags</emphasis>
 field specifies new values for the changed flags. The interpretation of the
debugging flags is implementation-specific, but flags are intended to control
debugging output and should not otherwise affect the operation of the server.
</para>


<para>
The <emphasis>
affectCtrls</emphasis>
 field specifies the debugging controls to be changed, the <emphasis>
ctrls</emphasis>
 field specifies new values for the changed controls. The interpretation of the
debugging controls is implementation-specific, but debugging controls are
allowed to affect the behavior of the server.
</para>


<para>
The <emphasis>
message</emphasis>
 field provides a message that the X server can print in any logging or
debugging files before changing the flags. The server must accept this field
but it is not required to actually display it anywhere.
</para>


<para>
The X Test Suite makes some assumptions about the implementation of locking
modifier keys that do not apply when XKB is present. The <emphasis>
XkbDF_DisableLocks</emphasis>
 debugging control provides a simple workaround to these test suite problems by
simply disabling all locking keys. If <emphasis>
XkbDF_DisableLocks</emphasis>
 is enabled, the <emphasis>
SA_LockMods</emphasis>
 and <emphasis>
SA_LockGroup</emphasis>
 actions behave like <emphasis>
SA_SetMods</emphasis>
 and <emphasis>
SA_LockMods</emphasis>
, respectively. If it is disabled, <emphasis>
SA_LockMods</emphasis>
 and <emphasis>
SA_LockGroup</emphasis>
 actions behave normally.
</para>


<para>
Implementations are free to ignore the <emphasis>
XkbDF_DisableLocks</emphasis>
 debugging control or to define others.
</para>


<para>
The <emphasis>
currentFlags</emphasis>
 return value reports the current setting for the debugging flags, if
applicable. The <emphasis>
currentCtrls</emphasis>
 return value reports the setting for the debugging controls, if applicable.
The <emphasis>
supportedFlags</emphasis>
 and <emphasis>
supportedCtrls</emphasis>
 fields report the flags and controls that are recognized by the
implementation. Attempts to change unsupported fields or controls are silently
ignored.
</para>


<para>
If the <emphasis>
XkbSetDebuggingFlags</emphasis>
 request contains more data than expected, the server ignores the extra data,
but no error results. If the request has less data than expected, a <emphasis>
Length</emphasis>
 error results.
</para>


<para>
If the <emphasis>
XkbSetDebuggingFlags</emphasis>
 reply contains more data than expected, the client just ignores any
uninterpreted data without reporting an error. If the reply has less data than
expected, a <emphasis>
Length</emphasis>
 error results.
</para>


</sect2>
</sect1>
<sect1 id='Events'>
<title>Events</title>

<para>
All XKB events report the time at which they occurred in a field named
<emphasis>
time</emphasis>
 and the device on which they occurred in a field named <emphasis>
deviceID</emphasis>
. XKB uses a single X event code for all events and uses a common field to
distinguish XKB event type.
</para>


<sect2 id='Tracking_Keyboard_Replacement'>
<title>Tracking Keyboard Replacement</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbNewKeyboardNotify</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>time: TIMESTAMP</entry>
    </row>
    <row>
      <entry role='protoname'>
deviceID: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
changed: KB_NKNDETAILMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
minKeyCode, maxKeyCode: KEYCODE</entry>
    </row>
    <row>
      <entry role='protoname'>
oldDeviceID: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
oldMinKeyCode, oldMaxKeyCode: KEYCODE</entry>
    </row>
    <row>
      <entry role='protoname'>
requestMajor, requestMinor: CARD8</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
An <emphasis>
XkbNewKeyboardNotify</emphasis>
 event reports that a new core keyboard has been installed. New keyboard notify
events can be generated:
</para>

<itemizedlist>
<listitem>
  <para>When the X server detects that the keyboard was changed.
  </para>
</listitem>
<listitem>
  <para>When a client installs a new extension device as the core keyboard
using the X Input Extension <emphasis>
ChangeKeyboardDevice</emphasis>
 request.
  </para>
</listitem>
<listitem>
  <para>When a client issues an <emphasis>
XkbGetMapByName</emphasis>
 request which changes the keycodes range or geometry.
  </para>
</listitem>
</itemizedlist>

<para>
The <emphasis>
changed</emphasis>
 field of the event reports the aspects of the keyboard that have changed, and
can contain any combination of the event details for this event:
</para>

<informaltable frame='topbot'>
<?dbfo keep-together="always" ?>
<tgroup cols='2' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
<colspec colname='c2' colwidth='2.0*'/>
<thead>
  <row rowsep='1'>
    <entry>Bit in Changed</entry>
    <entry>Meaning</entry>
  </row>
</thead>
<tbody>
  <row>
    <entry>NKN_Keycodes</entry>
    <entry>The new keyboard has a different minimum or maximum keycode.</entry>
  </row>
  <row>
    <entry>NKN_Geometry</entry>
    <entry>The new keyboard has a different keyboard geometry.</entry>
  </row>
  <row>
    <entry>NKN_DeviceID</entry>
    <entry>The new keyboard has a new X Input Extension device
identifier</entry>
  </row>
</tbody>
</tgroup>
</informaltable>

<para>
The server sends an <emphasis>
XkbNewKeyboardNotify</emphasis>
 event to a client only if at least one of the bits that is set in the
<emphasis>
changed</emphasis>
 field of the event is also set in the appropriate event details mask for the
client.
</para>


<para>
The <emphasis>
minKeyCode</emphasis>
 and <emphasis>
maxKeyCode</emphasis>
 fields report the minimum and maximum keycodes that can be returned by the new
keyboard. The <emphasis>
oldMinKeyCode</emphasis>
 and <emphasis>
oldMaxKeyCode</emphasis>
 fields report the minimum and maximum values that could be returned before the
change. This event always reports all four values, but the old and new values
are the same unless <emphasis>
NKN_Keycodes</emphasis>
 is set in <emphasis>
changed</emphasis>
.
</para>


<para>
Once a client receives a new keyboard notify event which reports a new keycode
range, the X server reports events from all keys in the new range to that
client. Clients that do not request or receive new keyboard notify events
receive events only from keys that fall in the last range for legal keys
reported to that client. See <link linkend='Replacing_the_Keyboard_On_the_Fly'>
Replacing the Keyboard "On-the-Fly"</link> for a more detailed explanation.
</para>


<para>
If <emphasis>
NKN_Keycodes</emphasis>
 is set in <emphasis>
changed</emphasis>
, the <emphasis>
XkbNewKeyboardNotify</emphasis>
 event subsumes all other change notification events (e.g. <emphasis>
XkbMapNotify</emphasis>
, <emphasis>
XkbNamesNotify</emphasis>
) that would otherwise result from the keyboard change. Clients who receive an
<emphasis>
XkbNewKeyboardNotify</emphasis>
 event should assume that all other aspects of the keyboard mapping have
changed and regenerate the entire local copy of the keyboard description.
</para>


<para>
The <emphasis>
deviceID</emphasis>
 field reports the X Input Extension device identifier of the new keyboard
device; <emphasis>
oldDeviceID</emphasis>
 reports the device identifier before the change. This event always includes
both values, but they are the same unless <emphasis>
NKN_DeviceID</emphasis>
 is set in <emphasis>
changed</emphasis>
. If the server does not support the X Input Extension, both fields have the
value <emphasis>
0</emphasis>
.
</para>


<para>
The <emphasis>
requestMajor</emphasis>
 and <emphasis>
requestMinor</emphasis>
 fields report the major and minor opcode of the request that caused the
keyboard change. If the keyboard change was not caused by some client request,
both fields have the value <emphasis>
0</emphasis>
.
</para>


</sect2>
<sect2 id='Tracking_Keyboard_Mapping_Changes'>
<title>Tracking Keyboard Mapping Changes</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbMapNotify</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>time: TIMESTAMP</entry>
    </row>
    <row>
      <entry role='protoname'>
deviceID: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
ptrBtnActions: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
changed: KB_MAPPARTMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
minKeyCode, maxKeyCode: KEYCODE</entry>
    </row>
    <row>
      <entry role='protoname'>
firstType, nTypes: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
firstKeySym, firstKeyAction: KEYCODE</entry>
    </row>
    <row>
      <entry role='protoname'>
nKeySyms, nKeyActions: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
firstKeyBehavior, firstKeyExplicit: KEYCODE</entry>
    </row>
    <row>
      <entry role='protoname'>
nKeyBehaviors, nKeyExplicit: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
virtualMods: KB_VMODMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
firstModMapKey, firstVModMapKey: KEYCODE</entry>
    </row>
    <row>
      <entry role='protoname'>
nModMapKeys, nVModMapKeys: CARD8</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
An <emphasis>
XkbMapNotify</emphasis>
 event reports that some aspect of XKB map for a keyboard has changed. Map
notify events can be generated whenever some aspect of the keyboard map is
changed by an XKB or core protocol request.
</para>


<para>
The <emphasis>
deviceID</emphasis>
 field reports the keyboard for which some map component has changed and the
<emphasis>
changed</emphasis>
 field reports the components with new values, and can contain any of the
values that are legal for the <emphasis>
full</emphasis>
 and <emphasis>
partial</emphasis>
 fields of the <emphasis>
XkbGetMap</emphasis>
 request. The server sends an <emphasis>
XkbMapNotify</emphasis>
 event to a client only if at least one of the bits that is set in the
<emphasis>
changed</emphasis>
 field of the event is also set in the appropriate event details mask for the
client.
</para>


<para>
The <emphasis>
minKeyCode</emphasis>
 and <emphasis>
maxKeyCode</emphasis>
 fields report the range of keycodes that are legal on the keyboard for which
the change is being reported.
</para>


<para>
If <emphasis>
XkbKeyTypesMask</emphasis>
 is set in <emphasis>
changed</emphasis>
, the <emphasis>
firstType</emphasis>
 and <emphasis>
nTypes</emphasis>
 fields report a range of key types that includes all changed types. Otherwise,
both fields are <emphasis>
0</emphasis>
.
</para>


<para>
If <emphasis>
XkbKeySymsMask</emphasis>
 is set in <emphasis>
changed</emphasis>
, the <emphasis>
firstKeySym</emphasis>
 and <emphasis>
nKeySyms</emphasis>
 fields report a range of keycodes that includes all keys with new symbols.
Otherwise, both fields are <emphasis>
0</emphasis>
.
</para>


<para>
If <emphasis>
XkbKeyActionsMask</emphasis>
 is set in <emphasis>
changed</emphasis>
, the <emphasis>
firstKeyAction</emphasis>
 and <emphasis>
nKeyActions</emphasis>
 fields report a range of keycodes that includes all keys with new actions.
Otherwise, both fields are <emphasis>
0</emphasis>
.
</para>


<para>
If <emphasis>
XkbKeyBehaviorsMask</emphasis>
 is set in <emphasis>
changed</emphasis>
, the <emphasis>
firstKeyBehavior </emphasis>
and <emphasis>
nKeyBehaviors</emphasis>
 fields report a range of keycodes that includes all keys with new key
behavior. Otherwise, both fields are <emphasis>
0</emphasis>
.
</para>


<para>
If <emphasis>
XkbVirtualModsMask</emphasis>
 is set in <emphasis>
changed</emphasis>
, <emphasis>
virtualMods</emphasis>
 contains all virtual modifiers to which a new set of real modifiers is bound.
Otherwise, <emphasis>
virtualMods</emphasis>
 is <emphasis>
0</emphasis>
.
</para>


<para>
If <emphasis>
XkbExplicitComponentsMask</emphasis>
 is set in <emphasis>
changed</emphasis>
, the <emphasis>
firstKeyExplicit</emphasis>
 and <emphasis>
nKeyExplicit</emphasis>
 fields report a range of keycodes that includes all keys with changed explicit
components. Otherwise, both fields are <emphasis>
0</emphasis>
.
</para>


<para>
If <emphasis>
XkbModifierMapMask</emphasis>
 is set in <emphasis>
changed</emphasis>
, the <emphasis>
firstModMapKey</emphasis>
 and <emphasis>
nModMapKeys</emphasis>
 fields report a range of keycodes that includes all keys with changed modifier
bindings. Otherwise, both fields are <emphasis>
0</emphasis>
.
</para>


<para>
If <emphasis>
XkbVirtualModMapMask</emphasis>
 is set in <emphasis>
changed</emphasis>
, the <emphasis>
firstVModMapKey</emphasis>
 and <emphasis>
nVModMapKeys</emphasis>
 fields report a range of keycodes that includes all keys with changed virtual
modifier mappings. Otherwise, both fields are <emphasis>
0</emphasis>
.
</para>


</sect2>
<sect2 id='Tracking_Keyboard_State_Changes'>
<title>Tracking Keyboard State Changes</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbStateNotify</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>time: TIMESTAMP</entry>
    </row>
    <row>
      <entry role='protoname'>
deviceID: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
mods, baseMods, latchedMods, lockedMods: KEYMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
group, lockedGroup: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
baseGroup, latchedGroup: INT16</entry>
    </row>
    <row>
      <entry role='protoname'>
compatState: KEYMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
grabMods, compatGrabMods: KEYMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
lookupMods, compatLookupMods: KEYMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
ptrBtnState: BUTMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
changed: KB_STATEPARTMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
keycode: KEYCODE</entry>
    </row>
    <row>
      <entry role='protoname'>
eventType: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
requestMajor, requestMinor: CARD8</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
An <emphasis>XkbStateNotify</emphasis>
event reports that some component of the XKB state (see
<link linkend='Keyboard_State'>Keyboard State</link>) has changed.
State notify events are usually caused by key or pointer activity, but they can
also result from explicit state changes requested by the <emphasis>
XkbLatchLockState</emphasis>
 request or by other extensions.
</para>


<para>
The <emphasis>
deviceID</emphasis>
 field reports the keyboard on which some state component changed. The
<emphasis>
changed</emphasis>
 field reports the XKB state components (see
<link linkend='Keyboard_State'>Keyboard State</link>) that have changed
and contain any combination of:
</para>

<informaltable frame='none'>
<?dbfo keep-together="always" ?>
<tgroup cols='3' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
<colspec colname='c2' colwidth='1.0*'/>
<colspec colname='c3' colwidth='2.0*'/>
<thead>
  <row rowsep='1'>
    <entry>Bit in changed</entry>
    <entry>Event field</entry>
    <entry>Changed component</entry>
  </row>
</thead>
<tbody>
  <row>
    <entry><emphasis>
ModifierState</emphasis>
</entry>
    <entry><emphasis>
mods</emphasis>
</entry>
    <entry>The effective modifiers</entry>
  </row>
  <row>
    <entry><emphasis>
ModifierBase</emphasis>
</entry>
    <entry><emphasis>
baseMods</emphasis>
</entry>
    <entry>The base modifiers</entry>
  </row>
  <row>
    <entry><emphasis>
ModifierLatch</emphasis>
</entry>
    <entry><emphasis>
latchedMods</emphasis>
</entry>
    <entry>The latched modifiers</entry>
  </row>
  <row>
    <entry><emphasis>
ModifierLock</emphasis>
</entry>
    <entry><emphasis>
lockedMods</emphasis>
</entry>
    <entry>The locked modifiers</entry>
  </row>
  <row>
    <entry><emphasis>
GroupState</emphasis>
</entry>
    <entry><emphasis>
group</emphasis>
</entry>
    <entry>The effective keyboard group</entry>
  </row>
  <row>
    <entry><emphasis>
GroupBase</emphasis>
</entry>
    <entry><emphasis>
baseGroup</emphasis>
</entry>
    <entry>The base keyboard group</entry>
  </row>
  <row>
    <entry><emphasis>
GroupLatch</emphasis>
</entry>
    <entry><emphasis>
latchedGroup</emphasis>
</entry>
    <entry>The latched keyboard group</entry>
  </row>
  <row>
    <entry><emphasis>
GroupLock</emphasis>
</entry>
    <entry><emphasis>
lockedGroup</emphasis>
</entry>
    <entry>The locked keyboard group</entry>
  </row>
  <row>
    <entry><emphasis>
PointerButtons</emphasis>
</entry>
    <entry><emphasis>
ptrBtnState</emphasis>
</entry>
    <entry>The state of the core pointer buttons</entry>
  </row>
  <row>
    <entry><emphasis>
GrabMods</emphasis>
</entry>
    <entry><emphasis>
grabMods</emphasis>
</entry>
    <entry>The XKB state used to compute grabs</entry>
  </row>
  <row>
    <entry><emphasis>
LookupMods</emphasis>
</entry>
    <entry><emphasis>
lookupMods</emphasis>
</entry>
    <entry>The XKB state used to look up symbols</entry>
  </row>
  <row>
    <entry><emphasis>
CompatState</emphasis>
</entry>
    <entry><emphasis>
compatState</emphasis>
</entry>
    <entry>Default state for non-XKB clients</entry>
  </row>
  <row>
    <entry><emphasis>
CompatGrabMods</emphasis>
</entry>
    <entry><emphasis>
compatGrabMods</emphasis>
</entry>
    <entry>The core state used to compute grabs</entry>
  </row>
  <row>
    <entry><emphasis>
CompatLookupMods</emphasis>
</entry>
    <entry><emphasis>
compatLookupMods</emphasis>
</entry>
    <entry>The core state used to look up symbols</entry>
  </row>
</tbody>
</tgroup>
</informaltable>

<para>
The server sends an <emphasis>
XkbStateNotify</emphasis>
 event to a client only if at least one of the bits that is set in the
<emphasis>
changed</emphasis>
 field of the event is also set in the appropriate event details mask for the
client.
</para>


<para>
A state notify event reports current values for all state components, even
those with unchanged values.
</para>


<para>
The <emphasis>
keycode</emphasis>
 field reports the key or button which caused the change in state while the
<emphasis>
eventType</emphasis>
 field reports the exact type of event (e.g. <emphasis>
KeyPress</emphasis>
). If the change in state was not caused by key or button activity, both fields
have the value <emphasis>
0</emphasis>
.
</para>


<para>
The <emphasis>
requestMajor</emphasis>
 and <emphasis>
requestMinor</emphasis>
 fields report the major and minor opcodes of the request that caused the
change in state and have the value <emphasis>
0</emphasis>
 if it was resulted from key or button activity.
</para>


</sect2>
<sect2 id='Tracking_Keyboard_Control_Changes'>
<title>Tracking Keyboard Control Changes</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbControlsNotify</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>time: TIMESTAMP</entry>
    </row>
    <row>
      <entry role='protoname'>
deviceID: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
numGroups: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
changedControls: KB_CONTROLMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
enabledControls,enabledControlChanges: KB_BOOLCTRLMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
keycode: KEYCODE</entry>
    </row>
    <row>
      <entry role='protoname'>
eventType: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
requestMajor: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
requestMinor: CARD8</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
An <emphasis>
XkbControlsNotify</emphasis>
 event reports a change in one or more of the global keyboard controls (see
<link linkend='Global_Keyboard_Controls'>Global Keyboard Controls</link>)
or in the internal modifiers or ignore locks masks (see
<link linkend='Server_Internal_Modifiers_and_Ignore_Locks_Behavior'>
Server Internal Modifiers and Ignore
Locks Behavior</link>). Controls notify events are usually caused by and
<emphasis>
XkbSetControls</emphasis>
 request, but they can also be caused by keyboard activity or certain core
protocol and input extension requests.
</para>


<para>
The <emphasis>
deviceID</emphasis>
 field reports the keyboard for which some control has changed, and the
<emphasis>
changed</emphasis>
 field reports the controls that have new values.
</para>


<para>
The <emphasis>
changed</emphasis>
 field can contain any of the values that are permitted for the <emphasis>
changeControls</emphasis>
 field of the <emphasis>
XkbSetControls</emphasis>
 request. The server sends an <emphasis>
XkbControlsNotify</emphasis>
 event to a client only if at least one of the bits that is set in the
<emphasis>
changed</emphasis>
 field of the event is also set in the appropriate event details mask for the
client.
</para>


<para>
The <emphasis>
numGroups</emphasis>
 field reports the total number of groups defined for the keyboard, whether or
not the number of groups has changed.
</para>


<para>
The <emphasis>
enabledControls</emphasis>
 field reports the current status of all of the boolean controls, whether or
not any boolean controls changed state. If <emphasis>
EnabledControls</emphasis>
 is set in <emphasis>
changed</emphasis>
, the <emphasis>
enabledControlChanges</emphasis>
 field reports the boolean controls that were enabled or disabled; if a control
is specified in <emphasis>
enabledControlChanges</emphasis>
, the value that is reported for that control in <emphasis>
enabledControls</emphasis>
 represents a change in state.
</para>


<para>
The <emphasis>
keycode</emphasis>
 field reports the key or button which caused the change in state while the
<emphasis>
eventType</emphasis>
 field reports the exact type of event (e.g. <emphasis>
KeyPress</emphasis>
). If the change in state was not caused by key or button activity, both fields
have the value <emphasis>
0</emphasis>
.
</para>


<para>
The <emphasis>
requestMajor</emphasis>
 and <emphasis>
requestMinor</emphasis>
 fields report the major and minor opcodes of the request that caused the
change in state and have the value <emphasis>
0</emphasis>
 if it was resulted from key or button activity.
</para>


</sect2>
<sect2 id='Tracking_Keyboard_Indicator_State_Changes'>
<title>Tracking Keyboard Indicator State Changes</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbIndicatorStateNotify</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>time: TIMESTAMP</entry>
    </row>
    <row>
      <entry role='protoname'>
deviceID: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
stateChanged, state: KB_INDICATORMASK</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
An <emphasis>
XkbIndicatorStateNotify</emphasis>
 event indicates that one or more of the indicators on a keyboard have changed
state. Indicator state notify events can be caused by:
</para>

<itemizedlist>
<listitem>
  <para>Automatic update to reflect changes in keyboard state (keyboard
activity, <emphasis>
XkbLatchLockState</emphasis>
 requests).
  </para>
</listitem>
<listitem>
  <para>Automatic update to reflect changes in keyboard controls (<emphasis>
XkbSetControls</emphasis>
, keyboard activity, certain core protocol and input extension requests).
  </para>
</listitem>
<listitem>
  <para>Explicit attempts to change indicator state (core protocol and input
extension requests, <emphasis>
XkbSetNamedIndicator</emphasis>
 requests).
  </para>
</listitem>
<listitem>
  <para>Changes to indicator maps (<emphasis>
XkbSetIndicatorMap</emphasis>
 and <emphasis>
XkbSetNamedIndicator</emphasis>
 requests).
  </para>
</listitem>
</itemizedlist>

<para>
The <emphasis>
deviceID</emphasis>
 field reports the keyboard for which some indicator has changed, and the
<emphasis>
state</emphasis>
 field reports the new state for all indicators on the specified keyboard. The
<emphasis>
stateChanged</emphasis>
 field specifies which of the values in <emphasis>
state</emphasis>
 represent a new state for the corresponding indicator. The server sends an
<emphasis>
XkbIndicatorStateNotify</emphasis>
 event to a client only if at least one of the bits that is set in the
<emphasis>
stateChanged</emphasis>
 field of the event is also set in the appropriate event details mask for the
client.
</para>


</sect2>
<sect2 id='Tracking_Keyboard_Indicator_Map_Changes'>
<title>Tracking Keyboard Indicator Map Changes</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbIndicatorMapNotify</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>time: TIMESTAMP</entry>
    </row>
    <row>
      <entry role='protoname'>
deviceID: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
state: KB_INDICATORMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
mapChanged: KB_INDICATORMASK</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
An <emphasis>
XkbIndicatorMapNotify</emphasis>
 event indicates that the maps for one or more keyboard indicators have been
changed. Indicator map notify events can be caused by <emphasis>
XkbSetIndicatorMap</emphasis>
 and <emphasis>
XkbSetNamedIndicator</emphasis>
 requests.
</para>


<para>
The <emphasis>
deviceID</emphasis>
 field reports the keyboard for which some indicator map has changed, and the
<emphasis>
mapChanged</emphasis>
 field reports the indicators with changed maps. The server sends an <emphasis>
XkbIndicatorMapNotify</emphasis>
 event to a client only if at least one of the bits that is set in the
<emphasis>
mapChanged</emphasis>
 field of the event is also set in the appropriate event details mask for the
client.
</para>


<para>
The <emphasis>
state</emphasis>
 field reports the current state of all indicators on the specified keyboard.
</para>


</sect2>
<sect2 id='Tracking_Keyboard_Name_Changes'>
<title>Tracking Keyboard Name Changes</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbNamesNotify</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>time: TIMESTAMP</entry>
    </row>
    <row>
      <entry role='protoname'>
deviceID: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
changed: KB_NAMEDETAILMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
firstType, nTypes: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
firstLevelName, nLevelNames: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
firstKey: KEYCODE</entry>
    </row>
    <row>
      <entry role='protoname'>
nKeys, nKeyAliases, nRadioGroups: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
changedGroupNames: KB_GROUPMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
changedVirtualMods: KB_VMODMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
changedIndicators: KB_INDICATORMASK</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
An <emphasis>
XkbNamesNotify</emphasis>
 event reports a change to one or more of the symbolic names associated with a
keyboard. Symbolic names can change when:
</para>

<itemizedlist>
<listitem>
  <para> Some client explicitly changes them using <emphasis>
XkbSetNames</emphasis>
.
  </para>
</listitem>
<listitem>
  <para>The list of key types or radio groups is resized
  </para>
</listitem>
<listitem>
  <para>The group width of some key type is changed
  </para>
</listitem>
</itemizedlist>

<para>
The <emphasis>
deviceID</emphasis>
 field reports the keyboard on which names were changed. The <emphasis>
changed</emphasis>
 mask lists the components for which some names have changed and can have any
combination of the values permitted for the <emphasis>
which</emphasis>
 field of the <emphasis>
XkbGetNames</emphasis>
 request. The server sends an <emphasis>
XkbNamesNotify</emphasis>
 event to a client only if at least one of the bits that is set in the
<emphasis>
changed</emphasis>
 field of the event is also set in the appropriate event details mask for the
client.
</para>


<para>
If <emphasis>
KeyTypeNames</emphasis>
 is set in <emphasis>
changed</emphasis>
, the <emphasis>
firstType</emphasis>
 and <emphasis>
nTypes</emphasis>
 fields report a range of types that includes all types with changed names.
Otherwise, both fields are <emphasis>
0</emphasis>
.
</para>


<para>
If <emphasis>
KTLevelNames</emphasis>
 is set in <emphasis>
changed</emphasis>
, the <emphasis>
firstLevelName</emphasis>
 and <emphasis>
nLevelNames</emphasis>
 fields report a range of types that includes all types with changed level
names. Otherwise, both fields are <emphasis>
0</emphasis>
.
</para>


<para>
If <emphasis>
IndicatorNames</emphasis>
 is set in <emphasis>
changed</emphasis>
, the <emphasis>
changedIndicators</emphasis>
 field reports the indicators with changed names. Otherwise, <emphasis>
changedIndicators</emphasis>
 is <emphasis>
0</emphasis>
.
</para>


<para>
If <emphasis>
VirtualModNames</emphasis>
 is set in <emphasis>
changed</emphasis>
, the <emphasis>
changedVirtualMods</emphasis>
 field reports the virtual modifiers with changed names. Otherwise, <emphasis>
changedVirtualMods</emphasis>
 is <emphasis>
0</emphasis>
.
</para>


<para>
If <emphasis>
GroupNames</emphasis>
 is set in <emphasis>
changed</emphasis>
, the <emphasis>
changedGroupNames</emphasis>
 field reports the groups with changed names. Otherwise, <emphasis>
changedGroupNames</emphasis>
 is <emphasis>
0</emphasis>
.
</para>


<para>
If <emphasis>
KeyNames</emphasis>
 is set in <emphasis>
changed</emphasis>
, the <emphasis>
firstKey</emphasis>
 and <emphasis>
nKeys</emphasis>
 fields report a range of keycodes that includes all keys with changed names.
Otherwise, both fields are <emphasis>
0</emphasis>
.
</para>


<para>
The <emphasis>
nKeyAliases</emphasis>
 field reports the total number of key aliases associated with the keyboard,
regardless of whether <emphasis>
KeyAliases</emphasis>
 is set in <emphasis>
changed</emphasis>
.
</para>


<para>
The <emphasis>
nRadioGroups</emphasis>
 field reports the total number of radio group names associated with the
keyboard, regardless of whether <emphasis>
RGNames</emphasis>
 is set in <emphasis>
changed</emphasis>
.
</para>


</sect2>
<sect2 id='Tracking_Compatibility_Map_Changes'>
<title>Tracking Compatibility Map Changes</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbCompatMapNotify</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>time: TIMESTAMP</entry>
    </row>
    <row>
      <entry role='protoname'>
deviceID: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
changedGroups: KB_GROUPMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
firstSI, nSI: CARD16</entry>
    </row>
    <row>
      <entry role='protoname'>
nTotalSI: CARD16</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
An <emphasis>
XkbCompatMapNotify</emphasis>
 event indicates that some component of the compatibility map for a keyboard
has been changed. Compatibility map notify events can be caused by <emphasis>
XkbSetCompatMap</emphasis>
 and <emphasis>
XkbGetMapByName</emphasis>
 requests.
</para>


<para>
The <emphasis>
deviceID</emphasis>
 field reports the keyboard for which the compatibility map has changed; if the
server does not support the X input extension, <emphasis>
deviceID</emphasis>
 is <emphasis>
0</emphasis>
.
</para>


<para>
The <emphasis>
changedGroups</emphasis>
 field reports the keyboard groups, if any, with a changed entry in the group
compatibility map. The <emphasis>
firstSI</emphasis>
 and <emphasis>
nSI</emphasis>
 fields specify a range of symbol interpretations in the symbol compatibility
map that includes all changed symbol interpretations; if the symbol
compatibility map is unchanged, both fields are <emphasis>
0</emphasis>
. The <emphasis>
nTotalSI</emphasis>
 field always reports the total number of symbol interpretations present in the
symbol compatibility map, regardless of whether any symbol interpretations have
been changed.
</para>


<para>
The server sends an <emphasis>
XkbCompatMapNotify</emphasis>
 event to a client only if at least one of the following conditions is met:
</para>

<itemizedlist>
<listitem>
  <para>The <emphasis>
nSI</emphasis>
 field of the event is non-zero, and the <emphasis>
XkbSymInterpMask</emphasis>
 bit is set in the appropriate event details mask for the client.
  </para>
</listitem>
<listitem>
  <para>The <emphasis>
changedGroups</emphasis>
 field of the event contains at least one group, and the <emphasis>
XkbGroupCompatMask</emphasis>
 bit is set in the appropriate event details mask for the client.
  </para>
</listitem>
</itemizedlist>

</sect2>
<sect2 id='Tracking_Application_Bell_Requests'>
<title>Tracking Application Bell Requests</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbBellNotify</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>time: TIMESTAMP</entry>
    </row>
    <row>
      <entry role='protoname'>
deviceID: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
bellClass: { KbdFeedbackClass, BellFeedbackClass }</entry>
    </row>
    <row>
      <entry role='protoname'>
bellID: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
percent: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
pitch: CARD16</entry>
    </row>
    <row>
      <entry role='protoname'>
duration: CARD16</entry>
    </row>
    <row>
      <entry role='protoname'>
eventOnly: BOOL</entry>
    </row>
    <row>
      <entry role='protoname'>
name: ATOM</entry>
    </row>
    <row>
      <entry role='protoname'>
window: WINDOW</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
An <emphasis>
XkbBellNotify</emphasis>
 event indicates that some client has requested a keyboard bell. Bell notify
events are usually caused by <emphasis>
Bell</emphasis>
, <emphasis>
DeviceBell</emphasis>
, or <emphasis>
XkbBell</emphasis>
 requests, but they can also be generated by the server (e.g. if the <emphasis>
AccessXFeedback</emphasis>
 control is active).
</para>


<para>
The server sends an <emphasis>
XkbBellNotify</emphasis>
 event to a client if the appropriate event details field for the client has
the value <emphasis>
True</emphasis>
.
</para>


<para>
The <emphasis>
deviceID</emphasis>
 field specifies the device for which a bell was requested, while the <emphasis>
bellClass</emphasis>
 and <emphasis>
bellID</emphasis>
 fields specify the input extension class and identifier of the feedback for
which the bell was requested. If the reporting server does not support the
input extension, all three fields have the value 0.
</para>


<para>
The <emphasis>
percent</emphasis>
, <emphasis>
pitch</emphasis>
 and <emphasis>
duration</emphasis>
 fields report the volume, tone and duration requested for the bell as
specified by the <emphasis>
XkbBell</emphasis>
 request. Bell notify events caused by core protocol or input extension
requests use the pitch and duration specified in the corresponding bell or
keyboard feedback control.
</para>


<para>
If the bell was caused by an <emphasis>
XkbBell</emphasis>
 request or by the X server, <emphasis>
name</emphasis>
 reports an optional symbolic name for the bell and the <emphasis>
window</emphasis>
 field optionally reports the window for which the bell was generated.
Otherwise, both fields have the value <emphasis>
None</emphasis>
.
</para>


<para>
If the <emphasis>
eventOnly</emphasis>
 field is <emphasis>
True</emphasis>
, the server did not generate a sound in response to the request, otherwise the
server issues the beep before sending the event. The eventOnly field can be
<emphasis>
True</emphasis>
 if the <emphasis>
AudibleBell</emphasis>
 control is disabled or if a client explicitly requests <emphasis>
eventOnly</emphasis>
 when it issues an <emphasis>
XkbBell</emphasis>
 request.
</para>


</sect2>
<sect2 id='Tracking_Messages_Generated_by_Key_Actions'>
<title>Tracking Messages Generated by Key Actions</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbActionMessage</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>time: TIMESTAMP</entry>
    </row>
    <row>
      <entry role='protoname'>
deviceID: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
keycode: KEYCODE</entry>
    </row>
    <row>
      <entry role='protoname'>
press: BOOL</entry>
    </row>
    <row>
      <entry role='protoname'>
mods: KEYMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
group: KB_GROUP</entry>
    </row>
    <row>
      <entry role='protoname'>
keyEventFollows: BOOL</entry>
    </row>
    <row>
      <entry role='protoname'>
message: LISTofCARD8</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
An <emphasis>
XkbActionMessage</emphasis>
 event is generated when the user operates a key to which an <emphasis>
SA_ActionMessage</emphasis>
 message is bound under the appropriate state and group. The server sends an
<emphasis>
XkbActionMessage</emphasis>
 event to a client if the appropriate event details field for the client has
the value <emphasis>
True</emphasis>
.
</para>


<para>
The <emphasis>
deviceID</emphasis>
 field specifies the keyboard device that contains the key which activated the
event. The <emphasis>
keycode</emphasis>
 field specifies the key whose operation caused the message and press is
<emphasis>
True</emphasis>
 if the message was caused by the user pressing the key. The <emphasis>
mods</emphasis>
 and <emphasis>
group</emphasis>
 fields report the effective keyboard modifiers and group in effect at the time
the key was pressed or released.
</para>


<para>
If <emphasis>
keyEventFollows</emphasis>
 is <emphasis>
True</emphasis>
, the server will also send a key press or release event, as appropriate, for
the key that generated the message. If it is <emphasis>
False</emphasis>
, the key causes only a message. Note that the key event is delivered normally
with respect to passive grabs, keyboard focus, and cursor position, so that
<emphasis>
keyEventFollows</emphasis>
 does not guarantee that any particular client which receives the <emphasis>
XkbActionMessage</emphasis>
 notify event will also receive a key press or release event.
</para>


<para>
The <emphasis>
message</emphasis>
 field is <emphasis>
NULL</emphasis>
-terminated string of up to <emphasis>
ActionMessageLength</emphasis>
 (<emphasis>
6</emphasis>
) bytes, which reports the contents of the <emphasis>
message</emphasis>
 field in the action that caused the message notify event.
</para>


</sect2>
<sect2 id='Tracking_Changes_to_AccessX_State_and_Keys'>
<title>Tracking Changes to AccessX State and Keys</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbAccessXNotify</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>time: TIMESTAMP</entry>
    </row>
    <row>
      <entry role='protoname'>
deviceID: CARD8</entry>
    </row>
    <row>
      <entry role='protoname'>
detail: KB_AXNDETAILMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
keycode: KEYCODE</entry>
    </row>
    <row>
      <entry role='protoname'>
slowKeysDelay: CARD16</entry>
    </row>
    <row>
      <entry role='protoname'>
debounceDelay: CARD16</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
An <emphasis>
XkbAccessXNotify</emphasis>
 event reports on some kinds of keyboard activity when any of the <emphasis>
SlowKeys</emphasis>
, <emphasis>
BounceKeys</emphasis>
 or <emphasis>
AccessXKeys</emphasis>
 controls are active. Compatibility map notify events can only be caused by
keyboard activity.
</para>


<para>
The <emphasis>
deviceID</emphasis>
 and <emphasis>
keycode</emphasis>
 fields specify the keyboard and key for which the event occurred. The
<emphasis>
detail</emphasis>
 field describes the event that occurred and has one of the following values:
</para>

<informaltable frame='topbot'>
<?dbfo keep-together="always" ?>
<tgroup cols='3' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
<colspec colname='c2' colwidth='1.0*'/>
<colspec colname='c3' colwidth='2.0*'/>
<thead>
  <row rowsep='1'>
    <entry>Detail</entry>
    <entry>Control</entry>
    <entry>Meaning</entry>
  </row>
</thead>
<tbody>
  <row>
    <entry><emphasis>
AXN_SKPress</emphasis>
</entry>
    <entry><emphasis>
SlowKeys</emphasis>
</entry>
    <entry>Key pressed</entry>
  </row>
  <row>
    <entry><emphasis>
AXN_SKAccept</emphasis>
</entry>
    <entry><emphasis>
SlowKeys</emphasis>
</entry>
    <entry><emphasis>
K</emphasis>
ey held until it was accepted.</entry>
  </row>
  <row>
    <entry><emphasis>
AXN_SKReject</emphasis>
</entry>
    <entry><emphasis>
SlowKeys</emphasis>
</entry>
    <entry>Key released before it was accepted.</entry>
  </row>
  <row>
    <entry><emphasis>
AXN_SKRelease</emphasis>
</entry>
    <entry><emphasis>
SlowKeys</emphasis>
</entry>
    <entry>Key released after it was accepted.</entry>
  </row>
  <row>
    <entry><emphasis>
AXN_BKAccept</emphasis>
</entry>
    <entry><emphasis>
BounceKeys</emphasis>
</entry>
    <entry>Key pressed while it was active.</entry>
  </row>
  <row>
    <entry><emphasis>
AXN_BKReject</emphasis>
</entry>
    <entry><emphasis>
BounceKeys</emphasis>
</entry>
    <entry>Key pressed while it was still disabled.</entry>
  </row>
  <row>
    <entry><emphasis>
AXN_AXKWarning</emphasis>
</entry>
    <entry><emphasis>
AccessXKeys</emphasis>
</entry>
    <entry>Shift key held down for four seconds</entry>
  </row>
</tbody>
</tgroup>
</informaltable>

<para>
Each subclass of the AccessX notify event is generated only when the control
specified in the table above is enabled. The server sends an <emphasis>
XkbAccessXNotify</emphasis>
 event to a client only if the bit which corresponds to the value of the
<emphasis>
detail</emphasis>
 field for the event is set in the appropriate event details mask for the
client.
</para>


<para>
Regardless of the value of <emphasis>
detail</emphasis>
, the <emphasis>
slowKeysDelay</emphasis>
 and <emphasis>
debounceDelay</emphasis>
 fields always reports the current slow keys acceptance delay (see
<link linkend='The_SlowKeys_Control'>The SlowKeys Control</link>) and
debounce delay (see <link linkend='The_BounceKeys_Control'>The BounceKeys
Control</link>) for the specified keyboard.
</para>


</sect2>
<sect2 id='Tracking_Changes_To_Extension_Devices'>
<title>Tracking Changes To Extension Devices</title>


<informaltable frame='none' tabstyle='proto'>
<?dbfo keep-together="always" ?>
<tgroup cols='1' align='left' colsep='0' rowsep='0'>
<colspec colname='c1' colwidth='1.0*'/>
  <thead>
    <row>
      <entry role='protoname'>XkbExtensionDeviceNotify</entry>
    </row>
  </thead>
  <tbody>
    <row>
      <entry role='protoargs'>time: TIMESTAMP</entry>
    </row>
    <row>
      <entry role='protoname'>
deviceID: CARD16</entry>
    </row>
    <row>
      <entry role='protoname'>
ledClass: { KbdFeedbackClass, LedFeedbackClass }</entry>
    </row>
    <row>
      <entry role='protoname'>
ledID: CARD16</entry>
    </row>
    <row>
      <entry role='protoname'>
reason: KB_XIDETAILMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
supported: KB_XIFEATUREMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
unsupported: KB_XIFEATUREMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
ledsDefined: KB_INDICATORMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
ledState: KB_INDICATORMASK</entry>
    </row>
    <row>
      <entry role='protoname'>
firstButton, nButtons: CARD8</entry>
    </row>

  </tbody>
  </tgroup>
</informaltable>

<para>
An <emphasis>
XkbExtensionDeviceNotify</emphasis>
 event reports:
</para>

<itemizedlist>
<listitem>
  <para>A change to some part of the XKB information for an extension device.
  </para>
</listitem>
<listitem>
  <para>An attempt to use an XKB extension device feature that is not supported
for the specified device by the current implementation.
  </para>
</listitem>
</itemizedlist>

<para>
The <emphasis>
deviceID</emphasis>
 field specifies the X Input Extension device identifier of some device on
which an XKB feature was requested, or <emphasis>
XkbUseCorePtr</emphasis>
 if the request affected the core pointer device. The <emphasis>
reason</emphasis>
 field explains why the event was generated in response to the request, and can
contain any combination of <emphasis>
XkbXI_UnsupportedFeature</emphasis>
 and the values permitted for the change field of the <emphasis>
XkbSetDeviceInfo</emphasis>
 request.
</para>


<para>
If <emphasis>
XkbXI_ButtonActions</emphasis>
 is set in <emphasis>
reason</emphasis>
, this event reports a successful change to the XKB actions bound to one or
more buttons on the core pointer or an extension device. The <emphasis>
firstButton</emphasis>
 and <emphasis>
nButtons</emphasis>
 fields report a range of device buttons that include all of the buttons for
which actions were changed.
</para>


<para>
If any combination of <emphasis>
XkbXI_IndicatorNames</emphasis>
, <emphasis>
XkbXI_IndicatorMaps</emphasis>
, or <emphasis>
XkbXI_IndicatorState</emphasis>
 is set in either <emphasis>
reason</emphasis>
 or <emphasis>
unsupported</emphasis>
, the <emphasis>
ledClass</emphasis>
 and <emphasis>
ledID</emphasis>
 fields specify the X Input Extension feedback class and identifier of the
feedback for which the change is reported. If this event reports any changes to
an indicator feedback, the <emphasis>
ledsDefined</emphasis>
 field reports all indicators on that feedback for which either a name or a
indicator map are defined, and <emphasis>
ledState</emphasis>
 reports the current state of all of the indicators on the specified feedback.
</para>


<para>
If <emphasis>
XkbXI_IndicatorNames</emphasis>
 is set in <emphasis>
reason</emphasis>
, this event reports a successful change to the symbolic names bound to one or
more extension device indicators by XKB. If <emphasis>
XkbXI_IndicatorMaps</emphasis>
 is set in <emphasis>
reason</emphasis>
, this event reports a successful change to the indicator maps bound to one or
more extension device indicators by XKB. If <emphasis>
XkbXI_IndicatorState</emphasis>
 is set in reason, this event reports that one or more indicators in the
specified device and feedback have changed state.
</para>


<para>
If <emphasis>
XkbXI_UnsupportedFeature</emphasis>
 is set in reason, this event reports an unsuccessful attempt to use some XKB
extension device feature that is not supported by the XKB implementation in the
server for the specified device. The <emphasis>
unsupported</emphasis>
 mask reports the requested features that are not available on the specified
device. See <link linkend='Interactions_Between_XKB_and_the_X_Input_Extension'>Interactions Between
XKB and the X Input Extension</link> for more information about possible XKB
interactions with the X Input Extension.
</para>


<para>
The server sends an <emphasis>
XkbExtensionDeviceNotify</emphasis>
 event to a client only if at least one of the bits that is set in the
<emphasis>
reason</emphasis>
 field of the event is also set in the appropriate event details mask for the
client.
</para>


<para>
Events that report a successful change to some extension device feature are
reported to all clients that have expressed interest in the event; events that
report an attempt to use an unsupported feature are reported only to the client
which issued the request. Events which report a partial success are reported to
all interested clients, but only the client that issued the request is informed
of the attempt to use unsupported features.
</para>
</sect2>
</sect1>
</chapter>
